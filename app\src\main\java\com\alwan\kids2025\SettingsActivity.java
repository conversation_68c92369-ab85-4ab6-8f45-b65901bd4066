package com.alwan.kids2025;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;

public class SettingsActivity extends BaseActivity {

    private SwitchCompat switchSoundEffects;
    private SwitchCompat switchBackgroundMusic;
    private SwitchCompat switchAutoSave;
    private SwitchCompat switchShowGrid;
    private LinearLayout btnPrivacyPolicy;
    private LinearLayout btnResetAds;
    private CardView languageCard;
    private CardView qualityCard;
    private TextView languageText;
    private TextView qualityText;

    private SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("الإعدادات");
        }

        // Setup navigation drawer
        setUpNavView();

        // Initialize preferences
        preferences = getSharedPreferences("app_settings", Context.MODE_PRIVATE);

        // Initialize views
        initViews();

        // Load settings
        loadSettings();

        // Setup listeners
        setupListeners();
    }

    private void initViews() {
        switchSoundEffects = findViewById(R.id.switch_sound_effects);
        switchBackgroundMusic = findViewById(R.id.switch_background_music);
        switchAutoSave = findViewById(R.id.switch_auto_save);
        switchShowGrid = findViewById(R.id.switch_show_grid);
        btnPrivacyPolicy = findViewById(R.id.btn_privacy_policy);
        btnResetAds = findViewById(R.id.btn_reset_ads);

        // These might not exist in current layout - check for null
        languageCard = findViewById(R.id.language_card);
        qualityCard = findViewById(R.id.quality_card);
        languageText = findViewById(R.id.language_text);
        qualityText = findViewById(R.id.quality_text);
    }

    private void loadSettings() {
        switchSoundEffects.setChecked(preferences.getBoolean("sound_effects", true));
        switchBackgroundMusic.setChecked(preferences.getBoolean("background_music", true));
        switchAutoSave.setChecked(preferences.getBoolean("auto_save", true));
        switchShowGrid.setChecked(preferences.getBoolean("show_grid", false));

        // Load language setting - only if TextView exists
        if (languageText != null) {
            String language = preferences.getString("app_language", "ar");
            languageText.setText(language.equals("ar") ? "العربية" : "English");
        }

        // Load quality setting - only if TextView exists
        if (qualityText != null) {
            String quality = preferences.getString("image_quality", "high");
            qualityText.setText(getQualityText(quality));
        }
    }

    private void setupListeners() {
        switchSoundEffects.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("sound_effects", isChecked).apply();
                showToast(isChecked ? "تم تفعيل الأصوات" : "تم إيقاف الأصوات");
            }
        });

        switchBackgroundMusic.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("background_music", isChecked).apply();
                showToast(isChecked ? "تم تفعيل الموسيقى" : "تم إيقاف الموسيقى");
            }
        });

        switchAutoSave.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("auto_save", isChecked).apply();
                showToast(isChecked ? "تم تفعيل الحفظ التلقائي" : "تم إيقاف الحفظ التلقائي");
            }
        });

        switchShowGrid.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("show_grid", isChecked).apply();
                showToast(isChecked ? "تم تفعيل الشبكة" : "تم إيقاف الشبكة");
            }
        });

        // Only set listeners if cards exist
        if (languageCard != null) {
            languageCard.setOnClickListener(v -> showLanguageDialog());
        }
        if (qualityCard != null) {
            qualityCard.setOnClickListener(v -> showQualityDialog());
        }

        btnPrivacyPolicy.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://sites.google.com/view/colors-kids"));
            startActivity(intent);
        });

        btnResetAds.setOnClickListener(v -> {
            adsSettings();
        });
    }

    private void showLanguageDialog() {
        String[] languages = {"العربية", "English"};
        String[] languageCodes = {"ar", "en"};

        String currentLanguage = preferences.getString("app_language", "ar");
        int selectedIndex = currentLanguage.equals("ar") ? 0 : 1;

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("اختر اللغة / Choose Language");
        builder.setSingleChoiceItems(languages, selectedIndex, (dialog, which) -> {
            preferences.edit().putString("app_language", languageCodes[which]).apply();
            if (languageText != null) {
                languageText.setText(languages[which]);
            }
            dialog.dismiss();
            showToast("تم تغيير اللغة بنجاح / Language changed successfully");
            recreate();
        });
        builder.setNegativeButton("إلغاء / Cancel", null);
        builder.show();
    }

    private void showQualityDialog() {
        String[] qualities = {"عالية", "متوسطة", "منخفضة"};
        String[] qualityCodes = {"high", "medium", "low"};

        String currentQuality = preferences.getString("image_quality", "high");
        int selectedIndex = 0;
        for (int i = 0; i < qualityCodes.length; i++) {
            if (qualityCodes[i].equals(currentQuality)) {
                selectedIndex = i;
                break;
            }
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("جودة الصور");
        builder.setSingleChoiceItems(qualities, selectedIndex, (dialog, which) -> {
            preferences.edit().putString("image_quality", qualityCodes[which]).apply();
            if (qualityText != null) {
                qualityText.setText(qualities[which]);
            }
            dialog.dismiss();
            showToast("تم تغيير جودة الصور إلى: " + qualities[which]);
        });
        builder.setNegativeButton("إلغاء", null);
        builder.show();
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void applyDarkMode(boolean isDarkMode) {
        if (isDarkMode) {
            getWindow().getDecorView().setBackgroundColor(0xFF000000); // Black
        } else {
            getWindow().getDecorView().setBackgroundColor(0xFFFFFFFF); // White
        }
    }

    private String getQualityText(String quality) {
        switch (quality) {
            case "high": return "عالية";
            case "medium": return "متوسطة";
            case "low": return "منخفضة";
            default: return "عالية";
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}