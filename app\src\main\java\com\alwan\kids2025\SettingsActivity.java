package com.alwan.kids2025;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.LinearLayout;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import android.content.Intent;
import android.net.Uri;

public class SettingsActivity extends BaseActivity {

    private SwitchCompat switchSoundEffects;
    private SwitchCompat switchBackgroundMusic;
    private SwitchCompat switchAutoSave;
    private SwitchCompat switchShowGrid;
    private LinearLayout btnPrivacyPolicy;
    private LinearLayout btnResetAds;
    
    private SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("الإعدادات");
        }
        
        // Setup navigation drawer
        setUpNavView();
        
        // Initialize preferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);
        
        // Initialize views
        initViews();
        
        // Load saved settings
        loadSettings();
        
        // Setup listeners
        setupListeners();
    }

    private void initViews() {
        switchSoundEffects = findViewById(R.id.switch_sound_effects);
        switchBackgroundMusic = findViewById(R.id.switch_background_music);
        switchAutoSave = findViewById(R.id.switch_auto_save);
        switchShowGrid = findViewById(R.id.switch_show_grid);
        btnPrivacyPolicy = findViewById(R.id.btn_privacy_policy);
        btnResetAds = findViewById(R.id.btn_reset_ads);
    }

    private void loadSettings() {
        switchSoundEffects.setChecked(preferences.getBoolean("sound_effects", true));
        switchBackgroundMusic.setChecked(preferences.getBoolean("background_music", true));
        switchAutoSave.setChecked(preferences.getBoolean("auto_save", true));
        switchShowGrid.setChecked(preferences.getBoolean("show_grid", false));
    }

    private void setupListeners() {
        switchSoundEffects.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.edit().putBoolean("sound_effects", isChecked).apply();
        });

        switchBackgroundMusic.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.edit().putBoolean("background_music", isChecked).apply();
        });

        switchAutoSave.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.edit().putBoolean("auto_save", isChecked).apply();
        });

        switchShowGrid.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.edit().putBoolean("show_grid", isChecked).apply();
        });

        btnPrivacyPolicy.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, 
                Uri.parse("https://sites.google.com/view/colors-kids"));
            startActivity(intent);
        });

        btnResetAds.setOnClickListener(v -> {
            adsSettings();
        });
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
