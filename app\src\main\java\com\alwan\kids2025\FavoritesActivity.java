package com.alwan.kids2025;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;

public class FavoritesActivity extends BaseActivity {

    private CardView emptyFavoritesCard;
    private CardView btnBrowseCategories;
    private RecyclerView favoritesRecyclerView;
    private LinearLayout favoritesActions;
    private CardView btnClearFavorites;
    private CardView btnExportFavorites;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_favorites);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle("المفضلة");
        }
        
        // Setup navigation drawer
        setUpNavView();
        
        // Initialize views
        initViews();
        
        // Setup listeners
        setupListeners();
        
        // Load favorites data
        loadFavoritesData();
    }

    private void initViews() {
        emptyFavoritesCard = findViewById(R.id.empty_favorites_card);
        btnBrowseCategories = findViewById(R.id.btn_browse_categories);
        favoritesRecyclerView = findViewById(R.id.favorites_recycler_view);
        favoritesActions = findViewById(R.id.favorites_actions);
        btnClearFavorites = findViewById(R.id.btn_clear_favorites);
        btnExportFavorites = findViewById(R.id.btn_export_favorites);
    }

    private void setupListeners() {
        btnBrowseCategories.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            startActivity(intent);
        });

        btnClearFavorites.setOnClickListener(v -> {
            // TODO: Implement clear favorites functionality
            showEmptyState();
        });

        btnExportFavorites.setOnClickListener(v -> {
            // TODO: Implement export favorites functionality
        });
    }

    private void loadFavoritesData() {
        // TODO: Load favorites from SharedPreferences or database
        // For now, show empty state
        showEmptyState();
    }

    private void showEmptyState() {
        emptyFavoritesCard.setVisibility(View.VISIBLE);
        favoritesRecyclerView.setVisibility(View.GONE);
        favoritesActions.setVisibility(View.GONE);
    }

    private void showFavoritesContent() {
        emptyFavoritesCard.setVisibility(View.GONE);
        favoritesRecyclerView.setVisibility(View.VISIBLE);
        favoritesActions.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
