package com.alwan.kids2025;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.cardview.widget.CardView;
import java.io.File;

public class AdvancedSettingsActivity extends BaseActivity {

    private SharedPreferences prefs;

    // Settings Views
    private Switch soundSwitch;
    private Switch vibrationSwitch;
    private Switch autoSaveSwitch;
    private Switch notificationsSwitch;
    private Switch darkModeSwitch;
    private TextView languageText;
    private TextView qualityText;
    private TextView cacheText;

    // Setting Cards
    private CardView languageCard;
    private CardView qualityCard;
    private CardView cacheCard;
    private CardView aboutCard;
    private CardView resetCard;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_advanced_settings);

        prefs = getSharedPreferences("app_settings", MODE_PRIVATE);

        initViews();
        loadSettings();
        setupListeners();
    }

    private void initViews() {
        // Switches
        soundSwitch = findViewById(R.id.sound_switch);
        vibrationSwitch = findViewById(R.id.vibration_switch);
        autoSaveSwitch = findViewById(R.id.auto_save_switch);
        notificationsSwitch = findViewById(R.id.notifications_switch);
        darkModeSwitch = findViewById(R.id.dark_mode_switch);

        // Text Views
        languageText = findViewById(R.id.language_text);
        qualityText = findViewById(R.id.quality_text);
        cacheText = findViewById(R.id.cache_text);

        // Cards
        languageCard = findViewById(R.id.language_card);
        qualityCard = findViewById(R.id.quality_card);
        cacheCard = findViewById(R.id.cache_card);
        aboutCard = findViewById(R.id.about_card);
        resetCard = findViewById(R.id.reset_card);
    }

    private void loadSettings() {
        soundSwitch.setChecked(prefs.getBoolean("sound_enabled", true));
        vibrationSwitch.setChecked(prefs.getBoolean("vibration_enabled", true));
        autoSaveSwitch.setChecked(prefs.getBoolean("auto_save_enabled", true));
        notificationsSwitch.setChecked(prefs.getBoolean("notifications_enabled", true));
        darkModeSwitch.setChecked(prefs.getBoolean("dark_mode_enabled", false));

        String language = prefs.getString("app_language", "ar");
        languageText.setText(language.equals("ar") ? "العربية" : "English");

        String quality = prefs.getString("image_quality", "high");
        qualityText.setText(getQualityText(quality));

        updateCacheSize();
    }

    private void setupListeners() {
        // Switch listeners
        soundSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("sound_enabled", isChecked).apply();
            showToast(isChecked ? "تم تفعيل الأصوات" : "تم إيقاف الأصوات");
        });

        vibrationSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("vibration_enabled", isChecked).apply();
            showToast(isChecked ? "تم تفعيل الاهتزاز" : "تم إيقاف الاهتزاز");
        });

        autoSaveSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("auto_save_enabled", isChecked).apply();
            showToast(isChecked ? "تم تفعيل الحفظ التلقائي" : "تم إيقاف الحفظ التلقائي");
        });

        notificationsSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("notifications_enabled", isChecked).apply();
            showToast(isChecked ? "تم تفعيل الإشعارات" : "تم إيقاف الإشعارات");
        });

        darkModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("dark_mode_enabled", isChecked).apply();
            showToast(isChecked ? "تم تفعيل الوضع الليلي" : "تم إيقاف الوضع الليلي");
            applyDarkMode(isChecked);
        });

        // Card listeners
        languageCard.setOnClickListener(v -> showLanguageDialog());
        qualityCard.setOnClickListener(v -> showQualityDialog());
        cacheCard.setOnClickListener(v -> showClearCacheDialog());
        aboutCard.setOnClickListener(v -> openAboutActivity());
        resetCard.setOnClickListener(v -> showResetDialog());
    }

    private void showLanguageDialog() {
        String[] languages = {"العربية", "English"};
        String[] languageCodes = {"ar", "en"};

        String currentLanguage = prefs.getString("app_language", "ar");
        int selectedIndex = currentLanguage.equals("ar") ? 0 : 1;

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("اختر اللغة / Choose Language");
        builder.setSingleChoiceItems(languages, selectedIndex, (dialog, which) -> {
            prefs.edit().putString("app_language", languageCodes[which]).apply();
            languageText.setText(languages[which]);
            dialog.dismiss();

            // Show confirmation message
            showToast("تم تغيير اللغة بنجاح / Language changed successfully");

            // Restart activity to apply language
            recreate();
        });
        builder.setNegativeButton("إلغاء / Cancel", null);
        builder.show();
    }

    private void showQualityDialog() {
        String[] qualities = {"عالية", "متوسطة", "منخفضة"};
        String[] qualityCodes = {"high", "medium", "low"};

        String currentQuality = prefs.getString("image_quality", "high");
        int selectedIndex = 0;
        for (int i = 0; i < qualityCodes.length; i++) {
            if (qualityCodes[i].equals(currentQuality)) {
                selectedIndex = i;
                break;
            }
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("جودة الصور");
        builder.setSingleChoiceItems(qualities, selectedIndex, (dialog, which) -> {
            prefs.edit().putString("image_quality", qualityCodes[which]).apply();
            qualityText.setText(qualities[which]);
            dialog.dismiss();
        });
        builder.show();
    }

    private void showClearCacheDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("مسح التخزين المؤقت");
        builder.setMessage("هل تريد مسح جميع الملفات المؤقتة؟");
        builder.setPositiveButton("نعم", (dialog, which) -> {
            clearCache();
            updateCacheSize();
        });
        builder.setNegativeButton("لا", null);
        builder.show();
    }

    private void showResetDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("إعادة تعيين الإعدادات");
        builder.setMessage("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟");
        builder.setPositiveButton("نعم", (dialog, which) -> {
            resetAllSettings();
        });
        builder.setNegativeButton("لا", null);
        builder.show();
    }

    private void openAboutActivity() {
        Intent intent = new Intent(this, AboutActivity.class);
        startActivity(intent);
    }

    private void clearCache() {
        try {
            File cacheDir = getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                deleteDir(cacheDir);
            }
            updateCacheSize();
            showToast("تم مسح التخزين المؤقت بنجاح");
        } catch (Exception e) {
            showToast("فشل في مسح التخزين المؤقت");
        }
    }

    private boolean deleteDir(File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
            return dir.delete();
        } else if (dir != null && dir.isFile()) {
            return dir.delete();
        } else {
            return false;
        }
    }

    private void updateCacheSize() {
        try {
            File cacheDir = getCacheDir();
            long size = getDirSize(cacheDir);
            String sizeStr = formatFileSize(size);
            cacheText.setText(sizeStr);
        } catch (Exception e) {
            cacheText.setText("0 MB");
        }
    }

    private long getDirSize(File dir) {
        long size = 0;
        if (dir != null && dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        size += file.length();
                    } else {
                        size += getDirSize(file);
                    }
                }
            }
        }
        return size;
    }

    private String formatFileSize(long size) {
        if (size <= 0) return "0 MB";
        final String[] units = new String[]{"B", "KB", "MB", "GB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void applyDarkMode(boolean isDarkMode) {
        // Apply dark mode theme
        if (isDarkMode) {
            // Apply dark theme colors
            getWindow().getDecorView().setBackgroundColor(getResources().getColor(android.R.color.black));
        } else {
            // Apply light theme colors
            getWindow().getDecorView().setBackgroundColor(getResources().getColor(android.R.color.white));
        }
        // Note: For full dark mode implementation, you would need to recreate the activity
        // or use AppCompatDelegate.setDefaultNightMode()
    }

    private void resetAllSettings() {
        prefs.edit().clear().apply();
        loadSettings();
        showToast("تم إعادة تعيين جميع الإعدادات");
    }

    private String getQualityText(String quality) {
        switch (quality) {
            case "high": return "عالية";
            case "medium": return "متوسطة";
            case "low": return "منخفضة";
            default: return "عالية";
        }
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
