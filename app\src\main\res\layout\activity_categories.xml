<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/modern_background"
    android:layoutDirection="locale"
    tools:context=".Categories">

    <!-- Modern Header Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@drawable/modern_gradient_background"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="24dp">

            <!-- App Logo -->
            <androidx.cardview.widget.CardView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="40dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@android:color/white">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="16dp"
                    android:src="@drawable/small_logo"
                    android:scaleType="centerInside" />

            </androidx.cardview.widget.CardView>

            <!-- App Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@color/text_on_primary"
                android:textSize="28sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="8dp" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="اختر فئة للتلوين"
                android:textColor="@color/text_on_primary"
                android:textSize="16sp"
                android:fontFamily="@font/blabeloo"
                android:alpha="0.9" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Categories Grid -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="240dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Row 1: Flowers & Cartoons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Flowers Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <!-- Background Image -->
                        <ImageView
                            android:id="@+id/c_1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp1_press" />

                        <!-- Gradient Overlay -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <!-- Content Container -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <!-- Top Section with Icon -->
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <!-- Category Icon -->
                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/flowers_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_flowers_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <!-- Decorative Elements -->
                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <!-- Bottom Section with Title -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/flowers"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <!-- Subtitle -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="زهور جميلة"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <!-- Cartoons Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <!-- Background Image -->
                        <ImageView
                            android:id="@+id/c_2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp2_press" />

                        <!-- Gradient Overlay -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <!-- Content Container -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <!-- Top Section with Icon -->
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <!-- Category Icon -->
                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/cartoons_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_cartoons_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <!-- Decorative Elements -->
                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <!-- Bottom Section with Title -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name4"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/cartoons"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <!-- Subtitle -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="شخصيات مرحة"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>



            <!-- Row 2: Animals & Foods -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Animals Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <!-- Background Image -->
                        <ImageView
                            android:id="@+id/c_3"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp3_press" />

                        <!-- Gradient Overlay -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <!-- Content Container -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <!-- Top Section with Icon -->
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <!-- Category Icon -->
                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/animals_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_animals_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <!-- Decorative Elements -->
                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <!-- Bottom Section with Title -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/animals"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <!-- Subtitle -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="حيوانات لطيفة"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <!-- Foods Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <ImageView
                            android:id="@+id/c_4"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp4_press" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/foods_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_foods_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name5"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/foods"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="أطعمة شهية"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- Row 3: Transport & Nature -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <!-- Transport Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <ImageView
                            android:id="@+id/c_5"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp5_press" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/transport_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_transport_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name3"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/transport"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="وسائل نقل"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <!-- Nature Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="200dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:foreground="@drawable/premium_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="16dp"
                    app:cardUseCompatPadding="true">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <ImageView
                            android:id="@+id/c_6"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/gp6_press" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/premium_card_overlay" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1">

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/nature_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/ic_nature_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <View
                                    android:layout_width="40dp"
                                    android:layout_height="40dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="10dp"
                                    android:background="@drawable/decorative_circle"
                                    android:alpha="0.3" />

                            </RelativeLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/premium_text_background"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_card_name6"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/nature"
                                    android:textColor="@android:color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4"
                                    android:letterSpacing="0.05" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="طبيعة خلابة"
                                    android:textColor="@android:color/white"
                                    android:textSize="12sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:alpha="0.9" />

                            </LinearLayout>

                        </LinearLayout>

                    </FrameLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        android:elevation="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>


