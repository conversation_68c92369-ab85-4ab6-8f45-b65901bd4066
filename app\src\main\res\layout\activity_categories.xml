<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/modern_gradient_bg"
    android:layoutDirection="locale"
    tools:context=".Categories">

    <!-- Animated Background -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/animated_bg_pattern"
        android:alpha="0.05"
        android:scaleType="centerCrop" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Hero Header Section -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="280dp"
                android:background="@drawable/hero_gradient_bg"
                android:clipChildren="false">

                <!-- Floating Decorative Elements -->
                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_margin="20dp"
                    android:src="@drawable/floating_star_1"
                    android:alpha="0.7"
                    android:rotation="15" />

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="60dp"
                    android:src="@drawable/floating_heart"
                    android:alpha="0.6"
                    android:rotation="-10" />

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="40dp"
                    android:layout_marginBottom="30dp"
                    android:src="@drawable/floating_brush"
                    android:alpha="0.8"
                    android:rotation="25" />

                <!-- Main Logo and Title -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:orientation="vertical">

                    <!-- Animated Logo Container -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_marginBottom="20dp"
                        app:cardCornerRadius="60dp"
                        app:cardElevation="20dp"
                        app:cardBackgroundColor="@android:color/white">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="25dp"
                                android:src="@drawable/small_logo"
                                android:scaleType="centerInside" />

                            <!-- Animated Ring -->
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="5dp"
                                android:background="@drawable/animated_ring" />

                        </RelativeLayout>
                    </androidx.cardview.widget.CardView>

                    <!-- App Title with Gradient Text -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:background="@drawable/gradient_text_bg"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="8dp"
                        android:layout_marginBottom="8dp"
                        android:elevation="4dp" />

                    <!-- Subtitle with Animation -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="✨ اكتشف عالم الألوان السحري ✨"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:fontFamily="@font/blabeloo"
                        android:alpha="0.95"
                        android:background="@drawable/subtitle_bg"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="6dp" />

                </LinearLayout>

                <!-- Wave Effect at Bottom -->
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_alignParentBottom="true"
                    android:src="@drawable/wave_bottom"
                    android:scaleType="fitXY" />

            </RelativeLayout>

            <!-- Categories Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginTop="-20dp"
                android:background="@drawable/categories_section_bg"
                android:elevation="8dp">

                <!-- Section Title -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="3dp"
                        android:background="@drawable/title_line_gradient" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎨 اختر مغامرتك الملونة 🎨"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:textColor="@color/primary_text_color"
                        android:layout_marginHorizontal="16dp" />

                    <View
                        android:layout_width="40dp"
                        android:layout_height="3dp"
                        android:background="@drawable/title_line_gradient" />

                </LinearLayout>

                <!-- Modern Cards Grid -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Row 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Card 1: Flowers -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <!-- Background Image with Blur Effect -->
                                <ImageView
                                    android:id="@+id/c_1"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp1_press" />

                                <!-- Gradient Overlay -->
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <!-- Floating Category Icon -->
                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/flowers_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/flower_icon_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <!-- Content Container -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name1"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/flowers"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور جميلة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <!-- Sparkle Effects -->
                                <ImageView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_margin="12dp"
                                    android:src="@drawable/sparkle_1"
                                    android:alpha="0.7" />

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_centerVertical="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_marginStart="20dp"
                                    android:src="@drawable/sparkle_2"
                                    android:alpha="0.5" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 2: Cartoons -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_2"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp2_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/cartoons_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/cartoon_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name4"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/cartoons"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="8 صور مرحة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <ImageView
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_alignParentBottom="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    android:src="@drawable/sparkle_1"
                                    android:alpha="0.6" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Row 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Card 3: Animals -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_3"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp3_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/animals_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/animal_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name2"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/animals"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="86 صورة رائعة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 4: Foods -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_4"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp4_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/foods_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/food_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name5"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/foods"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور شهية"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Row 3 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Card 5: Transportation -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_5"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp5_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/transport_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/transport_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name3"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/transport"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور متحركة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 6: Nature -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_6"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp6_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/nature_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/nature_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name6"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/nature"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور طبيعية"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_margin="20dp"
                                    android:src="@drawable/sparkle_2"
                                    android:alpha="0.7" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        android:src="@drawable/ic_palette"
        app:backgroundTint="@color/colorAccent"
        app:tint="@android:color/white"
        app:elevation="12dp" />

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        android:elevation="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>


