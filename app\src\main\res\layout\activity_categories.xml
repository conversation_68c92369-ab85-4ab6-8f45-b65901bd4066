<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:layoutDirection="locale"
    android:orientation="vertical"
    tools:context=".Categories">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/header_gradient"
        android:elevation="8dp"
        android:orientation="vertical"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="@android:color/white"
            android:textSize="28sp"
            android:textStyle="bold"
            android:fontFamily="@font/blabeloo"
            android:layout_marginTop="10dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="اختر فئة للتلوين"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:alpha="0.9"
            android:layout_marginTop="5dp" />

    </LinearLayout>

    <!-- Categories Grid -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:padding="16dp">

        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="3"
            android:alignmentMode="alignBounds"
            android:columnOrderPreserved="false"
            android:useDefaultMargins="true">

            <!-- Card 1: Flowers -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp1_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/flowers"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <!-- Floating Icon -->
                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/flower_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Card 2: Cartoons -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp2_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name4"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/cartoons"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/cartoon_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Card 3: Animals -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_3"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp3_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/animals"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/animal_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Card 4: Foods -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_4"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp4_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name5"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/foods"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/food_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Card 5: Transportation -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_5"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp5_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name3"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/transport"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/transport_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Card 6: Nature -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_6"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp6_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <TextView
                            android:id="@+id/text_card_name6"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/nature"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="12dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="8dp"
                        android:src="@drawable/nature_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

        </GridLayout>
    </ScrollView>

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</LinearLayout>


