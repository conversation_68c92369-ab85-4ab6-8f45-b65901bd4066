<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luxury_gradient_bg"
    android:layoutDirection="locale"
    tools:context=".Categories">

    <!-- Luxury Animated Background -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.1"
        android:src="@drawable/luxury_star_animation"
        android:scaleType="centerCrop" />

    <!-- Floating Particles Background -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/luxury_particles_pattern"
        android:alpha="0.08"
        android:scaleType="centerCrop" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Luxury Hero Section -->
            <com.github.florent37.shapeofview.shapes.ArcView
                android:layout_width="match_parent"
                android:layout_height="350dp"
                android:elevation="12dp"
                app:shape_arc_height="40dp"
                app:shape_arc_position="bottom">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/luxury_hero_gradient"
                    android:clipChildren="false">

                    <!-- Luxury Animated Elements -->
                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="20dp"
                        android:alpha="0.8"
                        android:src="@drawable/luxury_star_animation"
                        android:rotation="15" />

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="30dp"
                        android:layout_marginTop="60dp"
                        android:alpha="0.7"
                        android:src="@drawable/floating_heart"
                        android:rotation="-10" />

                    <ImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_alignParentBottom="true"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="40dp"
                        android:layout_marginBottom="50dp"
                        android:alpha="0.9"
                        android:src="@drawable/floating_brush"
                        android:rotation="25" />

                    <!-- Luxury Logo Container -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:gravity="center"
                        android:orientation="vertical">

                        <!-- Premium Logo with Multiple Rings -->
                        <RelativeLayout
                            android:layout_width="160dp"
                            android:layout_height="160dp"
                            android:layout_marginBottom="24dp">

                            <!-- Outer Glow Ring -->
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:alpha="0.6"
                                android:background="@drawable/luxury_outer_ring" />

                            <!-- Middle Ring -->
                            <View
                                android:layout_width="140dp"
                                android:layout_height="140dp"
                                android:layout_centerInParent="true"
                                android:background="@drawable/luxury_middle_ring"
                                android:alpha="0.8" />

                            <!-- Main Logo Card -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="120dp"
                                android:layout_height="120dp"
                                android:layout_centerInParent="true"
                                app:cardCornerRadius="60dp"
                                app:cardElevation="24dp"
                                app:cardBackgroundColor="@android:color/white">

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent">

                                    <com.makeramen.roundedimageview.RoundedImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="20dp"
                                        android:src="@drawable/small_logo"
                                        android:scaleType="centerInside"
                                        app:riv_corner_radius="50dp" />

                                    <!-- Inner Sparkle Animation -->
                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:alpha="0.4"
                                        android:src="@drawable/sparkle_1" />

                                </RelativeLayout>
                            </androidx.cardview.widget.CardView>

                            <!-- Floating Luxury Elements -->
                            <ImageView
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:src="@drawable/luxury_diamond"
                                android:rotation="15" />

                            <ImageView
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentStart="true"
                                android:src="@drawable/luxury_crown"
                                android:rotation="-10" />

                        </RelativeLayout>

                        <!-- Luxury App Title -->
                        <com.github.florent37.shapeofview.shapes.RoundRectView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:elevation="8dp"
                            app:shape_roundRect_borderColor="@color/luxury_gold"
                            app:shape_roundRect_borderWidth="3dp"
                            app:shape_roundRect_topLeftRadius="25dp"
                            app:shape_roundRect_topRightRadius="25dp"
                            app:shape_roundRect_bottomLeftRadius="25dp"
                            app:shape_roundRect_bottomRightRadius="25dp">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/luxury_title_gradient"
                                android:orientation="vertical"
                                android:paddingHorizontal="24dp"
                                android:paddingVertical="12dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/app_name"
                                    android:textSize="34sp"
                                    android:textStyle="bold"
                                    android:fontFamily="@font/blabeloo"
                                    android:textColor="@android:color/white"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="3"
                                    android:shadowDy="3"
                                    android:shadowRadius="8"
                                    android:gravity="center" />

                            </LinearLayout>
                        </com.github.florent37.shapeofview.shapes.RoundRectView>

                        <!-- Luxury Subtitle with Animation -->
                        <com.github.florent37.shapeofview.shapes.RoundRectView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:elevation="6dp"
                            app:shape_roundRect_borderColor="@color/luxury_silver"
                            app:shape_roundRect_borderWidth="2dp"
                            app:shape_roundRect_topLeftRadius="20dp"
                            app:shape_roundRect_topRightRadius="20dp"
                            app:shape_roundRect_bottomLeftRadius="20dp"
                            app:shape_roundRect_bottomRightRadius="20dp">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/luxury_subtitle_gradient"
                                android:orientation="horizontal"
                                android:paddingHorizontal="20dp"
                                android:paddingVertical="10dp"
                                android:gravity="center">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="8dp"
                                    android:src="@drawable/luxury_magic_wand"
                                    android:rotation="15" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="اكتشف عالم الألوان السحري"
                                    android:textColor="@android:color/white"
                                    android:textSize="18sp"
                                    android:fontFamily="@font/blabeloo"
                                    android:shadowColor="#80000000"
                                    android:shadowDx="2"
                                    android:shadowDy="2"
                                    android:shadowRadius="4" />

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginStart="8dp"
                                    android:src="@drawable/luxury_magic_wand"
                                    android:rotation="-15" />

                            </LinearLayout>
                        </com.github.florent37.shapeofview.shapes.RoundRectView>

                    </LinearLayout>

                </RelativeLayout>

            </com.github.florent37.shapeofview.shapes.ArcView>

            <!-- Categories Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginTop="-20dp"
                android:background="@drawable/categories_section_bg"
                android:elevation="8dp">

                <!-- Section Title -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="3dp"
                        android:background="@drawable/title_line_gradient" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎨 اختر مغامرتك الملونة 🎨"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:textColor="@color/primary_text_color"
                        android:layout_marginHorizontal="16dp" />

                    <View
                        android:layout_width="40dp"
                        android:layout_height="3dp"
                        android:background="@drawable/title_line_gradient" />

                </LinearLayout>

                <!-- Modern Cards Grid -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Row 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Card 1: Flowers -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <!-- Background Image with Blur Effect -->
                                <ImageView
                                    android:id="@+id/c_1"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp1_press" />

                                <!-- Gradient Overlay -->
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <!-- Floating Category Icon -->
                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/flowers_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/flower_icon_modern"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <!-- Content Container -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name1"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/flowers"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور جميلة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <!-- Sparkle Effects -->
                                <ImageView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_margin="12dp"
                                    android:src="@drawable/sparkle_1"
                                    android:alpha="0.7" />

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_centerVertical="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_marginStart="20dp"
                                    android:src="@drawable/sparkle_2"
                                    android:alpha="0.5" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 2: Cartoons -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_2"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp2_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/cartoons_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/cartoon_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name4"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/cartoons"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="8 صور مرحة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <ImageView
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_alignParentBottom="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    android:src="@drawable/sparkle_1"
                                    android:alpha="0.6" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Row 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Card 3: Animals -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_3"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp3_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/animals_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/animal_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name2"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/animals"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="86 صورة رائعة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 4: Foods -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_4"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp4_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/foods_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/food_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name5"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/foods"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور شهية"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- Row 3 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Card 5: Transportation -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_5"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp5_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/transport_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/transport_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name3"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/transport"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور متحركة"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                        <!-- Card 6: Nature -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="200dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:foreground="@drawable/modern_ripple_effect"
                            android:clickable="true"
                            android:focusable="true"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="16dp"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:id="@+id/c_6"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:src="@drawable/gp6_press" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/modern_card_overlay" />

                                <androidx.cardview.widget.CardView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_margin="16dp"
                                    app:cardCornerRadius="30dp"
                                    app:cardElevation="8dp"
                                    app:cardBackgroundColor="@color/nature_color">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_margin="12dp"
                                        android:src="@drawable/nature_icon"
                                        android:tint="@android:color/white" />

                                </androidx.cardview.widget.CardView>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:background="@drawable/modern_card_text_bg"
                                    android:orientation="vertical"
                                    android:padding="20dp">

                                    <TextView
                                        android:id="@+id/text_card_name6"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="@string/nature"
                                        android:textColor="@android:color/white"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:fontFamily="@font/blabeloo"
                                        android:layout_marginBottom="4dp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:text="6 صور طبيعية"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:alpha="0.8"
                                        android:fontFamily="@font/blabeloo" />

                                </LinearLayout>

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentStart="true"
                                    android:layout_margin="20dp"
                                    android:src="@drawable/sparkle_2"
                                    android:alpha="0.7" />

                            </RelativeLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        android:src="@drawable/ic_palette"
        app:backgroundTint="@color/colorAccent"
        app:tint="@android:color/white"
        app:elevation="12dp" />

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        android:elevation="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>


