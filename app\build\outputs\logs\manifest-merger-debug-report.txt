-- Merging decision tree log ---
manifest
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.ViksaaSkool:AwesomeSplash:v1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9207e24da08392b123ac6abd6c0bbb7\transformed\jetified-AwesomeSplash-v1.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a8cebdf235588805d6f74854d945d7\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:2:1-136:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccf3ee0ba7e6f3b41b220a780a8d541\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.daimajia.androidanimations:library:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd6f440e3969fd1ea64c29acf33e3ec1\transformed\jetified-library-1.1.3\AndroidManifest.xml:2:1-12:12
MERGED from [com.daimajia.easing:library:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e52e2b0eeaab8a6c051461aab7823916\transformed\jetified-library-1.0.1\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40acce1c3a2b617941018f107a306df6\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.jorgecastilloprz:fillableloaders:1.02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\259a268cebdd3f407f50865b627e8df7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40f044205cc14802a2ef279bbc4b28\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:17:1-80:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa0cca0fed54d0ba18400ee4703e134\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:review:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-ads-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fefb8cb7a8ae548c5c52abbd4f1ddeda\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1da3bae361dc290f47a2d0a48aea293a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c889f815ba6b4bb68da109ca61431d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70f2d5d5da83795ea4989885cc6fecf5\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdcbed77f954ac3f9c1cb79f7c6baef6\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\191d99146b9fa9a76ea31acf904a3d1e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5cdb9f0d5d177fbca5979f659d31d2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b5de90bf96449462f26dd24e928401\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efeeacd2622b0424c6dbafd3cb85d574\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af73a1faa3b19b22c8c5a442dcfedb10\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d69217ac71851dbb289072f4ad33195\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6636343d70f61437cc0a971343e71743\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e320a41af982c6ca1eceeffdc69f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7e85deab252cb2f29e2144edc7b042\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0d553d8430940559446a74bfc8608d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afbefbed88d109eba46227f73f370122\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b694c762c16c7e119f1b0f856d10fae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4630b1237475cc69c83d8707fc0d9479\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1e8a0c2dddea7f338f6603dcbe79aeb\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8307035faf9d76bc341d9196dce7a796\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83e8d2c7022a6ed5feb556d26e63d21\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067354aeb3298878393456f8f86531a4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\769d2e6a1b1e231cbb00b788b27d0424\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e216803f2be921019f0d06d2f9b477\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\003b0dc602869ed7b97e762c8bc2f755\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b43baf865b6b373e6940d10c95886d67\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0e08b05adb4a06cdf6123b54d11a59\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbaf884e97c39b245ed01184cd66764\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\200ddee23b0f28269478fde3a256a091\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0e47bde83e1503581a76698fd7aff78\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941e16d4e1af434371cdefe18ef4997b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\110a371c9ae6861376c800155bf81a62\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bab28f2c598b88cedd7c47559987bf2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5ecf0bc8f49f17e93b305d08ef88b78\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a944053718e8d3190d378d6409efec1\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ba8a927c733cf611c6c264cd4bfdf44\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ec2b278a80aa9707f67a356c10ea22\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\291969ba7792f27b49f026d4914b7fbe\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0f6ded6073ce5dc403d9b4acced436\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dde31bcade14e0a8ec5ab233f207ab4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\728c977381e74529572dc19bd646163f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9b14d76205307403ed195f26fd0c15\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e508e4bc1a2f9af16e1e03824e95753\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9952e16ecb7acd7f86cfecad684881f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1e09815d89e49b69a0dfa5f3213d7a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f73ff9b3bef37492e787482db1cabc0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c23182fca5cd96e469b5cb76f079450\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b58a217afcadbd277a2d437754a5a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07890a6af069a39e98d9fd481fee4b8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07ee2317a76f38d5d67ba94885ee57eb\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.ozodrukh:CircularReveal:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\AndroidManifest.xml:2:1-12:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:17:5-67
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:17:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:8:5-76
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:16:5-77
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:16:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:8:22-74
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:9:22-76
application
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:12:5-64:19
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml:12:5-64:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:11:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:11:5-20
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:49:5-134:19
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:49:5-134:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.jorgecastilloprz:fillableloaders:1.02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\AndroidManifest.xml:9:5-10:19
MERGED from [com.github.jorgecastilloprz:fillableloaders:1.02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\AndroidManifest.xml:9:5-10:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:50:5-78:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:50:5-78:19
MERGED from [com.google.android.play:review:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:review:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-ads-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.github.ozodrukh:CircularReveal:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\AndroidManifest.xml:10:5-20
MERGED from [com.github.ozodrukh:CircularReveal:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\AndroidManifest.xml:10:5-20
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:19:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:15:9-41
	android:largeHeap
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:17:9-33
	android:icon
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:14:9-38
	android:allowBackup
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:13:9-36
	android:theme
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:18:9-40
meta-data#google_analytics_adid_collection_enabled
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:20:9-100
	android:value
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:20:76-97
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:20:20-75
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:25:13-67
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:24:13-69
property#android.adservices.AD_SERVICES_CONFIG
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:26:9-30:48
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:30:9-32:61
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:29:13-59
		REJECTED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:32:13-58
	tools:replace
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:30:13-45
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:28:13-65
activity#com.alwan.kids2025.MainActivity
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:31:9-67
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:31:19-65
activity#com.alwan.kids2025.Categories
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:32:9-41:20
	android:exported
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:34:13-56
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:33:13-57
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:36:13-40:29
action#android.intent.action.MAIN
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:37:17-69
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:37:25-66
category#android.intent.category.DEFAULT
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:39:17-76
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:39:27-73
activity#com.alwan.kids2025.CategoryItems
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:42:9-44:59
	android:theme
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:44:13-56
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:43:13-60
activity#com.alwan.kids2025.Splash
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:45:9-54:20
	android:exported
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:46:13-36
	android:theme
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:48:13-56
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:47:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:49:13-53:29
category#android.intent.category.LAUNCHER
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:52:17-77
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:52:27-74
service#com.alwan.kids2025.MyFirebaseMessagingService
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:56:9-63:19
	android:enabled
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:58:13-35
	android:exported
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:59:13-36
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:57:13-73
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:60:13-62:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:61:17-78
	android:name
		ADDED from Z:\alwan5\app\src\main\AndroidManifest.xml:61:25-75
uses-sdk
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc858f18a2a1e2b92bb4036b7b9aba\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.ViksaaSkool:AwesomeSplash:v1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9207e24da08392b123ac6abd6c0bbb7\transformed\jetified-AwesomeSplash-v1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.ViksaaSkool:AwesomeSplash:v1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9207e24da08392b123ac6abd6c0bbb7\transformed\jetified-AwesomeSplash-v1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.15] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a03e2d8ebbb816cc1056fc1d69f7bb1\transformed\jetified-colorpicker-0.0.15\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a8cebdf235588805d6f74854d945d7\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a8cebdf235588805d6f74854d945d7\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:4:5-44
MERGED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:4:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccf3ee0ba7e6f3b41b220a780a8d541\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccf3ee0ba7e6f3b41b220a780a8d541\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e31ffb1ad5782e77f0808c09672d8fb\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.daimajia.androidanimations:library:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd6f440e3969fd1ea64c29acf33e3ec1\transformed\jetified-library-1.1.3\AndroidManifest.xml:8:5-10:41
MERGED from [com.daimajia.androidanimations:library:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd6f440e3969fd1ea64c29acf33e3ec1\transformed\jetified-library-1.1.3\AndroidManifest.xml:8:5-10:41
MERGED from [com.daimajia.easing:library:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e52e2b0eeaab8a6c051461aab7823916\transformed\jetified-library-1.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.daimajia.easing:library:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e52e2b0eeaab8a6c051461aab7823916\transformed\jetified-library-1.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40acce1c3a2b617941018f107a306df6\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40acce1c3a2b617941018f107a306df6\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.jorgecastilloprz:fillableloaders:1.02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.jorgecastilloprz:fillableloaders:1.02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b068faf48cdaa9c2b51996856b6422\transformed\jetified-fillableloaders-1.02\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\259a268cebdd3f407f50865b627e8df7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\259a268cebdd3f407f50865b627e8df7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40f044205cc14802a2ef279bbc4b28\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f40f044205cc14802a2ef279bbc4b28\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-ads:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa0cca0fed54d0ba18400ee4703e134\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baa0cca0fed54d0ba18400ee4703e134\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:review:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:review:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e1c397eb241a4443f79c6d40597bc81\transformed\jetified-review-2.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d58aca08b9f7aa861741aa08f020462\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\398dacc30cdc8b839071dbb8f01d6430\transformed\jetified-play-services-ads-base-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3eb2bb9e1e563c935afdc0a7c16c7294\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17339a2695474267970087912e7d125e\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df57532705e711fdf2f44533f7c13c3d\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f7064170f9262454fbef57b7ee7d761\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aad2ea763ed3fa234df937284c241f78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3179903ecb9e77da44a3c8be9966642\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f823b21e08d5fbfc3612da71aa60aff6\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aab4143cbcd5ca7b295c1f1413bd4758\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fefb8cb7a8ae548c5c52abbd4f1ddeda\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fefb8cb7a8ae548c5c52abbd4f1ddeda\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1da3bae361dc290f47a2d0a48aea293a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1da3bae361dc290f47a2d0a48aea293a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c889f815ba6b4bb68da109ca61431d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80c889f815ba6b4bb68da109ca61431d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70f2d5d5da83795ea4989885cc6fecf5\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70f2d5d5da83795ea4989885cc6fecf5\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdcbed77f954ac3f9c1cb79f7c6baef6\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdcbed77f954ac3f9c1cb79f7c6baef6\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\191d99146b9fa9a76ea31acf904a3d1e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\191d99146b9fa9a76ea31acf904a3d1e\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5cdb9f0d5d177fbca5979f659d31d2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5cdb9f0d5d177fbca5979f659d31d2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b5de90bf96449462f26dd24e928401\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b5de90bf96449462f26dd24e928401\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efeeacd2622b0424c6dbafd3cb85d574\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efeeacd2622b0424c6dbafd3cb85d574\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af73a1faa3b19b22c8c5a442dcfedb10\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af73a1faa3b19b22c8c5a442dcfedb10\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d69217ac71851dbb289072f4ad33195\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d69217ac71851dbb289072f4ad33195\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6636343d70f61437cc0a971343e71743\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6636343d70f61437cc0a971343e71743\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e320a41af982c6ca1eceeffdc69f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e320a41af982c6ca1eceeffdc69f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7e85deab252cb2f29e2144edc7b042\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e7e85deab252cb2f29e2144edc7b042\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0d553d8430940559446a74bfc8608d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0d553d8430940559446a74bfc8608d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afbefbed88d109eba46227f73f370122\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afbefbed88d109eba46227f73f370122\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b694c762c16c7e119f1b0f856d10fae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b694c762c16c7e119f1b0f856d10fae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4630b1237475cc69c83d8707fc0d9479\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4630b1237475cc69c83d8707fc0d9479\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1e8a0c2dddea7f338f6603dcbe79aeb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1e8a0c2dddea7f338f6603dcbe79aeb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8307035faf9d76bc341d9196dce7a796\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8307035faf9d76bc341d9196dce7a796\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83e8d2c7022a6ed5feb556d26e63d21\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83e8d2c7022a6ed5feb556d26e63d21\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067354aeb3298878393456f8f86531a4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\067354aeb3298878393456f8f86531a4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\769d2e6a1b1e231cbb00b788b27d0424\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\769d2e6a1b1e231cbb00b788b27d0424\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e216803f2be921019f0d06d2f9b477\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e216803f2be921019f0d06d2f9b477\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\003b0dc602869ed7b97e762c8bc2f755\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\003b0dc602869ed7b97e762c8bc2f755\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b43baf865b6b373e6940d10c95886d67\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b43baf865b6b373e6940d10c95886d67\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0e08b05adb4a06cdf6123b54d11a59\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf0e08b05adb4a06cdf6123b54d11a59\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbaf884e97c39b245ed01184cd66764\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbaf884e97c39b245ed01184cd66764\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\200ddee23b0f28269478fde3a256a091\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\200ddee23b0f28269478fde3a256a091\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0e47bde83e1503581a76698fd7aff78\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0e47bde83e1503581a76698fd7aff78\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941e16d4e1af434371cdefe18ef4997b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\941e16d4e1af434371cdefe18ef4997b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\110a371c9ae6861376c800155bf81a62\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\110a371c9ae6861376c800155bf81a62\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bab28f2c598b88cedd7c47559987bf2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bab28f2c598b88cedd7c47559987bf2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5ecf0bc8f49f17e93b305d08ef88b78\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5ecf0bc8f49f17e93b305d08ef88b78\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af12b41581c68fb35296eaab9fe67529\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a944053718e8d3190d378d6409efec1\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a944053718e8d3190d378d6409efec1\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ba8a927c733cf611c6c264cd4bfdf44\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ba8a927c733cf611c6c264cd4bfdf44\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ec2b278a80aa9707f67a356c10ea22\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ec2b278a80aa9707f67a356c10ea22\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\291969ba7792f27b49f026d4914b7fbe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\291969ba7792f27b49f026d4914b7fbe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0f6ded6073ce5dc403d9b4acced436\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0f6ded6073ce5dc403d9b4acced436\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dde31bcade14e0a8ec5ab233f207ab4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7dde31bcade14e0a8ec5ab233f207ab4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\728c977381e74529572dc19bd646163f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\728c977381e74529572dc19bd646163f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9b14d76205307403ed195f26fd0c15\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9b14d76205307403ed195f26fd0c15\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e508e4bc1a2f9af16e1e03824e95753\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e508e4bc1a2f9af16e1e03824e95753\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9952e16ecb7acd7f86cfecad684881f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9952e16ecb7acd7f86cfecad684881f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7e5a5b7eff93b63354f20751bf734\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1e09815d89e49b69a0dfa5f3213d7a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e1e09815d89e49b69a0dfa5f3213d7a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f73ff9b3bef37492e787482db1cabc0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f73ff9b3bef37492e787482db1cabc0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c23182fca5cd96e469b5cb76f079450\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c23182fca5cd96e469b5cb76f079450\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b58a217afcadbd277a2d437754a5a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b58a217afcadbd277a2d437754a5a5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07890a6af069a39e98d9fd481fee4b8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07890a6af069a39e98d9fd481fee4b8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07ee2317a76f38d5d67ba94885ee57eb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07ee2317a76f38d5d67ba94885ee57eb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.ozodrukh:CircularReveal:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.ozodrukh:CircularReveal:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5eb4c4c5fc3565a6abf8d37cdbd5a6\transformed\jetified-CircularReveal-1.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952446e2a2fa218bc85ba4e6563abcfd\transformed\jetified-play-services-ads-22.0.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from Z:\alwan5\app\src\main\AndroidManifest.xml
permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
permission#com.alwan.kids2025.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:7:5-9:47
	android:protectionLevel
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:9:9-44
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:8:9-63
uses-permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
uses-permission#com.alwan.kids2025.permission.C2D_MESSAGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:11:22-76
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:18:22-79
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:19:22-65
uses-permission#android.permission.VIBRATE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:5-66
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:24:22-63
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\996d5b0b4e0a8266eb9afbc92160f0b7\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:25:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:5-81
REJECTED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:27:5-29:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:30:22-78
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:5-86
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:32:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:5-87
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:33:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:5-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:34:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:35:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:36:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:37:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:5-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:38:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:39:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:5-91
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:40:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:5-92
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:41:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:5-93
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:42:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:5-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:43:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:5-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:44:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:5-83
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:45:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:5-88
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:46:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:5-89
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:47:22-86
receiver#com.onesignal.FCMBroadcastReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:50:9-61:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:52:13-36
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:53:13-73
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:51:13-62
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:com.alwan.kids2025
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:13-60:29
	android:priority
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:56:28-50
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:57:25-78
category#${applicationId}
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
category#com.alwan.kids2025
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:17-61
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:59:27-58
service#com.onesignal.HmsMessageServiceOneSignal
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:63:9-69:19
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:65:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:64:13-68
intent-filter#action:name:com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:66:13-68:29
action#com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:17-81
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:67:25-78
activity#com.onesignal.NotificationOpenedActivityHMS
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:71:9-79:20
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:74:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:73:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:75:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:72:13-71
intent-filter#action:name:android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:76:13-78:29
action#android.intent.action.VIEW
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:17-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:77:25-66
service#com.onesignal.FCMIntentService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:81:9-83:40
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:83:13-37
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:82:13-58
service#com.onesignal.FCMIntentJobService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:84:9-87:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:86:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:87:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:85:13-61
service#com.onesignal.SyncService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:88:9-91:43
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:90:13-37
	android:stopWithTask
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:89:13-53
service#com.onesignal.SyncJobService
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:92:9-95:72
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:94:13-37
	android:permission
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:95:13-69
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:93:13-56
activity#com.onesignal.PermissionsActivity
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:97:9-100:75
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:99:13-37
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:100:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:98:13-61
receiver#com.onesignal.NotificationDismissReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:102:9-104:39
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:104:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:103:13-69
receiver#com.onesignal.BootUpReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:105:9-112:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:107:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:106:13-56
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:108:13-111:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:17-79
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:109:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:17-82
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:110:25-79
receiver#com.onesignal.UpgradeReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:113:9-119:20
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:115:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:114:13-57
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:116:13-118:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:17-84
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:117:25-81
activity#com.onesignal.NotificationOpenedReceiver
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:121:9-127:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:123:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:125:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:124:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:127:13-72
	android:taskAffinity
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:126:13-36
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:122:13-68
activity#com.onesignal.NotificationOpenedReceiverAndroid22AndOlder
ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:128:9-133:75
	android:excludeFromRecents
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:130:13-46
	android:noHistory
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:132:13-37
	android:exported
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:131:13-36
	android:theme
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:133:13-72
	android:name
		ADDED from [com.onesignal:OneSignal:4.8.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45fdc981b26c02b1afc1001d148fb2a9\transformed\jetified-OneSignal-4.8.6\AndroidManifest.xml:129:13-85
queries
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:32:5-48:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:35:9-41:18
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:38:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:38:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:40:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:40:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:44:9-46:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:45:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:45:21-87
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:53:9-58:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:56:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:58:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:55:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:57:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:54:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:60:9-65:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:62:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:63:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:65:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:64:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:61:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:67:9-71:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:69:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:70:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:71:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:68:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:73:9-77:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:77:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:75:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5c4b53d9440b03da89e1e0dd02a7206\transformed\jetified-play-services-ads-lite-22.0.0\AndroidManifest.xml:74:13-82
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98d6414f57ca549b7d6bd1e51ebc5185\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0610e76e304808a3d71db9aa57bbe8\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\089fbe965f78042cd7ca720ff60aa6cd\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:40:13-87
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca941b8f71d12da555cdc9f31c3008dd\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd77c4315985c5be26b68c43bc84d5ea\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:38:17-139
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28de868e92df6a446272321a9f2ec8d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2640b9fa355971a68270b9af654b19e0\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c5f32962fac6ef30638853a7175eef8\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7feb638b6d94dde9b955151585876686\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b69a3f59ea6ba71104ab72d23691d1\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14c92d843d46f26f2f7adaf6ce888732\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d4c6ec3e6d78cf749dd3dafb1f53bb\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e88b5613f2b3b0edb68479dabd57681\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46e67dff9bfa6456bd55f4f78438584d\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1273d61679a02c43f1de22c3f405d6f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755f43156db6b4ab2bd85c5e10c792d9\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbd131f1b1e0c9680dc8c1d103e011d9\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e81160bf6c797fb22e35edfb47ca327\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb1fef9846a4ca177767ead91d3f6366\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3bd9b03117c03291bfe953980a6498\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d2a2a8dcd0fe51f6974bf7ef03f497\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9169a328e04980931295dda3f79d951\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c860c5adf21788ed095535b14fdcc0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
