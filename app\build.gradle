apply plugin: 'com.android.application'

android {
    compileSdk 34
    defaultConfig {
        applicationId "com.alwan.kids2025"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 3
        versionName "3"
        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.alwan.kids2025'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_14
        targetCompatibility JavaVersion.VERSION_14
        // Removed unsupported option: allWarningsAsErrors
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:deprecation"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'org.jetbrains:annotations:26.0.2'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.6.1', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation 'androidx.appcompat:appcompat:1.7.0'
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.github.ViksaaSkool:AwesomeSplash:v1.0.0'
    implementation ('com.google.android.gms:play-services-ads:22.0.0') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation 'com.google.android.ump:user-messaging-platform:3.2.0'
    // the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:32.8.1')
    implementation ('com.google.firebase:firebase-messaging') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation ('com.google.firebase:firebase-analytics') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }

    implementation 'com.github.QuadFlask:colorpicker:0.0.15' // Corrected version
    // Removed deprecated Play Core library
    // Added recommended alternatives
    implementation 'com.google.android.play:review:2.0.1' // Reverted to a stable version
    implementation 'com.google.android.play:app-update:2.1.0' // Updated to Android 14 compatible version
    // Add androidx.preference library
    implementation 'androidx.preference:preference:1.2.0'
    // Add AndroidX libraries
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.10.1'
    implementation 'androidx.preference:preference:1.2.0'
    // Add OneSignal library
    implementation 'com.onesignal:OneSignal:4.8.6'
    // New dependencies
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.activity:activity:1.8.0'
    implementation 'androidx.preference:preference:1.2.1'
    // Removed incorrect dependency

    // 🎨 Luxury Design Libraries for Premium UI (Working versions)
    implementation 'com.airbnb.android:lottie:6.1.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.makeramen:roundedimageview:2.3.0'
}
apply plugin: 'com.google.gms.google-services'
