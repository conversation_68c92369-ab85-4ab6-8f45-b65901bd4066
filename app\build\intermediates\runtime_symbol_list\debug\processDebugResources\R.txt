int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim linear_indeterminate_line1_head_interpolator 0x7f01001d
int anim linear_indeterminate_line1_tail_interpolator 0x7f01001e
int anim linear_indeterminate_line2_head_interpolator 0x7f01001f
int anim linear_indeterminate_line2_tail_interpolator 0x7f010020
int anim m3_bottom_sheet_slide_in 0x7f010021
int anim m3_bottom_sheet_slide_out 0x7f010022
int anim m3_motion_fade_enter 0x7f010023
int anim m3_motion_fade_exit 0x7f010024
int anim m3_side_sheet_enter_from_left 0x7f010025
int anim m3_side_sheet_enter_from_right 0x7f010026
int anim m3_side_sheet_exit_to_left 0x7f010027
int anim m3_side_sheet_exit_to_right 0x7f010028
int anim mtrl_bottom_sheet_slide_in 0x7f010029
int anim mtrl_bottom_sheet_slide_out 0x7f01002a
int anim mtrl_card_lowers_interpolator 0x7f01002b
int anim onesignal_fade_in 0x7f01002c
int anim onesignal_fade_out 0x7f01002d
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator m3_appbar_state_list_animator 0x7f020009
int animator m3_btn_elevated_btn_state_list_anim 0x7f02000a
int animator m3_btn_state_list_anim 0x7f02000b
int animator m3_card_elevated_state_list_anim 0x7f02000c
int animator m3_card_state_list_anim 0x7f02000d
int animator m3_chip_state_list_anim 0x7f02000e
int animator m3_elevated_chip_state_list_anim 0x7f02000f
int animator m3_extended_fab_change_size_collapse_motion_spec 0x7f020010
int animator m3_extended_fab_change_size_expand_motion_spec 0x7f020011
int animator m3_extended_fab_hide_motion_spec 0x7f020012
int animator m3_extended_fab_show_motion_spec 0x7f020013
int animator m3_extended_fab_state_list_animator 0x7f020014
int animator mtrl_btn_state_list_anim 0x7f020015
int animator mtrl_btn_unelevated_state_list_anim 0x7f020016
int animator mtrl_card_state_list_anim 0x7f020017
int animator mtrl_chip_state_list_anim 0x7f020018
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020019
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f02001a
int animator mtrl_extended_fab_hide_motion_spec 0x7f02001b
int animator mtrl_extended_fab_show_motion_spec 0x7f02001c
int animator mtrl_extended_fab_state_list_animator 0x7f02001d
int animator mtrl_fab_hide_motion_spec 0x7f02001e
int animator mtrl_fab_show_motion_spec 0x7f02001f
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020020
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020021
int array Thumbnails1 0x7f030000
int array Thumbnails2 0x7f030001
int array Thumbnails3 0x7f030002
int array Thumbnails4 0x7f030003
int array Thumbnails5 0x7f030004
int array Thumbnails6 0x7f030005
int attr actionBarDivider 0x7f040000
int attr actionBarItemBackground 0x7f040001
int attr actionBarPopupTheme 0x7f040002
int attr actionBarSize 0x7f040003
int attr actionBarSplitStyle 0x7f040004
int attr actionBarStyle 0x7f040005
int attr actionBarTabBarStyle 0x7f040006
int attr actionBarTabStyle 0x7f040007
int attr actionBarTabTextStyle 0x7f040008
int attr actionBarTheme 0x7f040009
int attr actionBarWidgetTheme 0x7f04000a
int attr actionButtonStyle 0x7f04000b
int attr actionDropDownStyle 0x7f04000c
int attr actionLayout 0x7f04000d
int attr actionMenuTextAppearance 0x7f04000e
int attr actionMenuTextColor 0x7f04000f
int attr actionModeBackground 0x7f040010
int attr actionModeCloseButtonStyle 0x7f040011
int attr actionModeCloseContentDescription 0x7f040012
int attr actionModeCloseDrawable 0x7f040013
int attr actionModeCopyDrawable 0x7f040014
int attr actionModeCutDrawable 0x7f040015
int attr actionModeFindDrawable 0x7f040016
int attr actionModePasteDrawable 0x7f040017
int attr actionModePopupWindowStyle 0x7f040018
int attr actionModeSelectAllDrawable 0x7f040019
int attr actionModeShareDrawable 0x7f04001a
int attr actionModeSplitBackground 0x7f04001b
int attr actionModeStyle 0x7f04001c
int attr actionModeTheme 0x7f04001d
int attr actionModeWebSearchDrawable 0x7f04001e
int attr actionOverflowButtonStyle 0x7f04001f
int attr actionOverflowMenuStyle 0x7f040020
int attr actionProviderClass 0x7f040021
int attr actionTextColorAlpha 0x7f040022
int attr actionViewClass 0x7f040023
int attr activeIndicatorLabelPadding 0x7f040024
int attr activityAction 0x7f040025
int attr activityChooserViewStyle 0x7f040026
int attr activityName 0x7f040027
int attr adSize 0x7f040028
int attr adSizes 0x7f040029
int attr adUnitId 0x7f04002a
int attr addElevationShadow 0x7f04002b
int attr adjustable 0x7f04002c
int attr alertDialogButtonGroupStyle 0x7f04002d
int attr alertDialogCenterButtons 0x7f04002e
int attr alertDialogStyle 0x7f04002f
int attr alertDialogTheme 0x7f040030
int attr allowDividerAbove 0x7f040031
int attr allowDividerAfterLastItem 0x7f040032
int attr allowDividerBelow 0x7f040033
int attr allowStacking 0x7f040034
int attr alpha 0x7f040035
int attr alphaSlider 0x7f040036
int attr alphaSliderView 0x7f040037
int attr alphabeticModifiers 0x7f040038
int attr altSrc 0x7f040039
int attr alwaysExpand 0x7f04003a
int attr animateMenuItems 0x7f04003b
int attr animateNavigationIcon 0x7f04003c
int attr animate_relativeTo 0x7f04003d
int attr animationMode 0x7f04003e
int attr appBarLayoutStyle 0x7f04003f
int attr applyMotionScene 0x7f040040
int attr arcMode 0x7f040041
int attr arrowHeadLength 0x7f040042
int attr arrowShaftLength 0x7f040043
int attr attributeName 0x7f040044
int attr autoAdjustToWithinGrandparentBounds 0x7f040045
int attr autoCompleteTextViewStyle 0x7f040046
int attr autoShowKeyboard 0x7f040047
int attr autoSizeMaxTextSize 0x7f040048
int attr autoSizeMinTextSize 0x7f040049
int attr autoSizePresetSizes 0x7f04004a
int attr autoSizeStepGranularity 0x7f04004b
int attr autoSizeTextType 0x7f04004c
int attr autoTransition 0x7f04004d
int attr backHandlingEnabled 0x7f04004e
int attr background 0x7f04004f
int attr backgroundColor 0x7f040050
int attr backgroundInsetBottom 0x7f040051
int attr backgroundInsetEnd 0x7f040052
int attr backgroundInsetStart 0x7f040053
int attr backgroundInsetTop 0x7f040054
int attr backgroundOverlayColorAlpha 0x7f040055
int attr backgroundSplit 0x7f040056
int attr backgroundStacked 0x7f040057
int attr backgroundTint 0x7f040058
int attr backgroundTintMode 0x7f040059
int attr badgeGravity 0x7f04005a
int attr badgeHeight 0x7f04005b
int attr badgeRadius 0x7f04005c
int attr badgeShapeAppearance 0x7f04005d
int attr badgeShapeAppearanceOverlay 0x7f04005e
int attr badgeStyle 0x7f04005f
int attr badgeText 0x7f040060
int attr badgeTextAppearance 0x7f040061
int attr badgeTextColor 0x7f040062
int attr badgeVerticalPadding 0x7f040063
int attr badgeWidePadding 0x7f040064
int attr badgeWidth 0x7f040065
int attr badgeWithTextHeight 0x7f040066
int attr badgeWithTextRadius 0x7f040067
int attr badgeWithTextShapeAppearance 0x7f040068
int attr badgeWithTextShapeAppearanceOverlay 0x7f040069
int attr badgeWithTextWidth 0x7f04006a
int attr barLength 0x7f04006b
int attr barrierAllowsGoneWidgets 0x7f04006c
int attr barrierDirection 0x7f04006d
int attr barrierMargin 0x7f04006e
int attr behavior_autoHide 0x7f04006f
int attr behavior_autoShrink 0x7f040070
int attr behavior_draggable 0x7f040071
int attr behavior_expandedOffset 0x7f040072
int attr behavior_fitToContents 0x7f040073
int attr behavior_halfExpandedRatio 0x7f040074
int attr behavior_hideable 0x7f040075
int attr behavior_overlapTop 0x7f040076
int attr behavior_peekHeight 0x7f040077
int attr behavior_saveFlags 0x7f040078
int attr behavior_significantVelocityThreshold 0x7f040079
int attr behavior_skipCollapsed 0x7f04007a
int attr border 0x7f04007b
int attr borderWidth 0x7f04007c
int attr borderlessButtonStyle 0x7f04007d
int attr bottomAppBarStyle 0x7f04007e
int attr bottomInsetScrimEnabled 0x7f04007f
int attr bottomNavigationStyle 0x7f040080
int attr bottomSheetDialogTheme 0x7f040081
int attr bottomSheetDragHandleStyle 0x7f040082
int attr bottomSheetStyle 0x7f040083
int attr boxBackgroundColor 0x7f040084
int attr boxBackgroundMode 0x7f040085
int attr boxCollapsedPaddingTop 0x7f040086
int attr boxCornerRadiusBottomEnd 0x7f040087
int attr boxCornerRadiusBottomStart 0x7f040088
int attr boxCornerRadiusTopEnd 0x7f040089
int attr boxCornerRadiusTopStart 0x7f04008a
int attr boxStrokeColor 0x7f04008b
int attr boxStrokeErrorColor 0x7f04008c
int attr boxStrokeWidth 0x7f04008d
int attr boxStrokeWidthFocused 0x7f04008e
int attr brightness 0x7f04008f
int attr buttonBarButtonStyle 0x7f040090
int attr buttonBarNegativeButtonStyle 0x7f040091
int attr buttonBarNeutralButtonStyle 0x7f040092
int attr buttonBarPositiveButtonStyle 0x7f040093
int attr buttonBarStyle 0x7f040094
int attr buttonCompat 0x7f040095
int attr buttonGravity 0x7f040096
int attr buttonIcon 0x7f040097
int attr buttonIconDimen 0x7f040098
int attr buttonIconTint 0x7f040099
int attr buttonIconTintMode 0x7f04009a
int attr buttonPanelSideLayout 0x7f04009b
int attr buttonSize 0x7f04009c
int attr buttonStyle 0x7f04009d
int attr buttonStyleSmall 0x7f04009e
int attr buttonTint 0x7f04009f
int attr buttonTintMode 0x7f0400a0
int attr cardBackgroundColor 0x7f0400a1
int attr cardCornerRadius 0x7f0400a2
int attr cardElevation 0x7f0400a3
int attr cardForegroundColor 0x7f0400a4
int attr cardMaxElevation 0x7f0400a5
int attr cardPreventCornerOverlap 0x7f0400a6
int attr cardUseCompatPadding 0x7f0400a7
int attr cardViewStyle 0x7f0400a8
int attr carousel_alignment 0x7f0400a9
int attr centerIfNoTextEnabled 0x7f0400aa
int attr chainUseRtl 0x7f0400ab
int attr checkBoxPreferenceStyle 0x7f0400ac
int attr checkMarkCompat 0x7f0400ad
int attr checkMarkTint 0x7f0400ae
int attr checkMarkTintMode 0x7f0400af
int attr checkboxStyle 0x7f0400b0
int attr checkedButton 0x7f0400b1
int attr checkedChip 0x7f0400b2
int attr checkedIcon 0x7f0400b3
int attr checkedIconEnabled 0x7f0400b4
int attr checkedIconGravity 0x7f0400b5
int attr checkedIconMargin 0x7f0400b6
int attr checkedIconSize 0x7f0400b7
int attr checkedIconTint 0x7f0400b8
int attr checkedIconVisible 0x7f0400b9
int attr checkedState 0x7f0400ba
int attr checkedTextViewStyle 0x7f0400bb
int attr chipBackgroundColor 0x7f0400bc
int attr chipCornerRadius 0x7f0400bd
int attr chipEndPadding 0x7f0400be
int attr chipGroupStyle 0x7f0400bf
int attr chipIcon 0x7f0400c0
int attr chipIconEnabled 0x7f0400c1
int attr chipIconSize 0x7f0400c2
int attr chipIconTint 0x7f0400c3
int attr chipIconVisible 0x7f0400c4
int attr chipMinHeight 0x7f0400c5
int attr chipMinTouchTargetSize 0x7f0400c6
int attr chipSpacing 0x7f0400c7
int attr chipSpacingHorizontal 0x7f0400c8
int attr chipSpacingVertical 0x7f0400c9
int attr chipStandaloneStyle 0x7f0400ca
int attr chipStartPadding 0x7f0400cb
int attr chipStrokeColor 0x7f0400cc
int attr chipStrokeWidth 0x7f0400cd
int attr chipStyle 0x7f0400ce
int attr chipSurfaceColor 0x7f0400cf
int attr circleCrop 0x7f0400d0
int attr circleRadius 0x7f0400d1
int attr circularProgressIndicatorStyle 0x7f0400d2
int attr clearTop 0x7f0400d3
int attr clickAction 0x7f0400d4
int attr clockFaceBackgroundColor 0x7f0400d5
int attr clockHandColor 0x7f0400d6
int attr clockIcon 0x7f0400d7
int attr clockNumberTextColor 0x7f0400d8
int attr closeIcon 0x7f0400d9
int attr closeIconEnabled 0x7f0400da
int attr closeIconEndPadding 0x7f0400db
int attr closeIconSize 0x7f0400dc
int attr closeIconStartPadding 0x7f0400dd
int attr closeIconTint 0x7f0400de
int attr closeIconVisible 0x7f0400df
int attr closeItemLayout 0x7f0400e0
int attr collapseContentDescription 0x7f0400e1
int attr collapseIcon 0x7f0400e2
int attr collapsedSize 0x7f0400e3
int attr collapsedTitleGravity 0x7f0400e4
int attr collapsedTitleTextAppearance 0x7f0400e5
int attr collapsedTitleTextColor 0x7f0400e6
int attr collapsingToolbarLayoutLargeSize 0x7f0400e7
int attr collapsingToolbarLayoutLargeStyle 0x7f0400e8
int attr collapsingToolbarLayoutMediumSize 0x7f0400e9
int attr collapsingToolbarLayoutMediumStyle 0x7f0400ea
int attr collapsingToolbarLayoutStyle 0x7f0400eb
int attr color 0x7f0400ec
int attr colorAccent 0x7f0400ed
int attr colorBackgroundFloating 0x7f0400ee
int attr colorButtonNormal 0x7f0400ef
int attr colorContainer 0x7f0400f0
int attr colorControlActivated 0x7f0400f1
int attr colorControlHighlight 0x7f0400f2
int attr colorControlNormal 0x7f0400f3
int attr colorError 0x7f0400f4
int attr colorErrorContainer 0x7f0400f5
int attr colorOnBackground 0x7f0400f6
int attr colorOnContainer 0x7f0400f7
int attr colorOnContainerUnchecked 0x7f0400f8
int attr colorOnError 0x7f0400f9
int attr colorOnErrorContainer 0x7f0400fa
int attr colorOnPrimary 0x7f0400fb
int attr colorOnPrimaryContainer 0x7f0400fc
int attr colorOnPrimaryFixed 0x7f0400fd
int attr colorOnPrimaryFixedVariant 0x7f0400fe
int attr colorOnPrimarySurface 0x7f0400ff
int attr colorOnSecondary 0x7f040100
int attr colorOnSecondaryContainer 0x7f040101
int attr colorOnSecondaryFixed 0x7f040102
int attr colorOnSecondaryFixedVariant 0x7f040103
int attr colorOnSurface 0x7f040104
int attr colorOnSurfaceInverse 0x7f040105
int attr colorOnSurfaceVariant 0x7f040106
int attr colorOnTertiary 0x7f040107
int attr colorOnTertiaryContainer 0x7f040108
int attr colorOnTertiaryFixed 0x7f040109
int attr colorOnTertiaryFixedVariant 0x7f04010a
int attr colorOutline 0x7f04010b
int attr colorOutlineVariant 0x7f04010c
int attr colorPrimary 0x7f04010d
int attr colorPrimaryContainer 0x7f04010e
int attr colorPrimaryDark 0x7f04010f
int attr colorPrimaryFixed 0x7f040110
int attr colorPrimaryFixedDim 0x7f040111
int attr colorPrimaryInverse 0x7f040112
int attr colorPrimarySurface 0x7f040113
int attr colorPrimaryVariant 0x7f040114
int attr colorScheme 0x7f040115
int attr colorSecondary 0x7f040116
int attr colorSecondaryContainer 0x7f040117
int attr colorSecondaryFixed 0x7f040118
int attr colorSecondaryFixedDim 0x7f040119
int attr colorSecondaryVariant 0x7f04011a
int attr colorSurface 0x7f04011b
int attr colorSurfaceBright 0x7f04011c
int attr colorSurfaceContainer 0x7f04011d
int attr colorSurfaceContainerHigh 0x7f04011e
int attr colorSurfaceContainerHighest 0x7f04011f
int attr colorSurfaceContainerLow 0x7f040120
int attr colorSurfaceContainerLowest 0x7f040121
int attr colorSurfaceDim 0x7f040122
int attr colorSurfaceInverse 0x7f040123
int attr colorSurfaceVariant 0x7f040124
int attr colorSwitchThumbNormal 0x7f040125
int attr colorTertiary 0x7f040126
int attr colorTertiaryContainer 0x7f040127
int attr colorTertiaryFixed 0x7f040128
int attr colorTertiaryFixedDim 0x7f040129
int attr commitIcon 0x7f04012a
int attr compatShadowEnabled 0x7f04012b
int attr constraintSet 0x7f04012c
int attr constraintSetEnd 0x7f04012d
int attr constraintSetStart 0x7f04012e
int attr constraint_referenced_ids 0x7f04012f
int attr constraints 0x7f040130
int attr content 0x7f040131
int attr contentDescription 0x7f040132
int attr contentInsetEnd 0x7f040133
int attr contentInsetEndWithActions 0x7f040134
int attr contentInsetLeft 0x7f040135
int attr contentInsetRight 0x7f040136
int attr contentInsetStart 0x7f040137
int attr contentInsetStartWithNavigation 0x7f040138
int attr contentPadding 0x7f040139
int attr contentPaddingBottom 0x7f04013a
int attr contentPaddingEnd 0x7f04013b
int attr contentPaddingLeft 0x7f04013c
int attr contentPaddingRight 0x7f04013d
int attr contentPaddingStart 0x7f04013e
int attr contentPaddingTop 0x7f04013f
int attr contentScrim 0x7f040140
int attr contrast 0x7f040141
int attr controlBackground 0x7f040142
int attr coordinatorLayoutStyle 0x7f040143
int attr coplanarSiblingViewId 0x7f040144
int attr cornerFamily 0x7f040145
int attr cornerFamilyBottomLeft 0x7f040146
int attr cornerFamilyBottomRight 0x7f040147
int attr cornerFamilyTopLeft 0x7f040148
int attr cornerFamilyTopRight 0x7f040149
int attr cornerRadius 0x7f04014a
int attr cornerSize 0x7f04014b
int attr cornerSizeBottomLeft 0x7f04014c
int attr cornerSizeBottomRight 0x7f04014d
int attr cornerSizeTopLeft 0x7f04014e
int attr cornerSizeTopRight 0x7f04014f
int attr counterEnabled 0x7f040150
int attr counterMaxLength 0x7f040151
int attr counterOverflowTextAppearance 0x7f040152
int attr counterOverflowTextColor 0x7f040153
int attr counterTextAppearance 0x7f040154
int attr counterTextColor 0x7f040155
int attr crossfade 0x7f040156
int attr currentState 0x7f040157
int attr cursorColor 0x7f040158
int attr cursorErrorColor 0x7f040159
int attr curveFit 0x7f04015a
int attr customBoolean 0x7f04015b
int attr customColorDrawableValue 0x7f04015c
int attr customColorValue 0x7f04015d
int attr customDimension 0x7f04015e
int attr customFloatValue 0x7f04015f
int attr customIntegerValue 0x7f040160
int attr customNavigationLayout 0x7f040161
int attr customPixelDimension 0x7f040162
int attr customStringValue 0x7f040163
int attr dayInvalidStyle 0x7f040164
int attr daySelectedStyle 0x7f040165
int attr dayStyle 0x7f040166
int attr dayTodayStyle 0x7f040167
int attr defaultDuration 0x7f040168
int attr defaultMarginsEnabled 0x7f040169
int attr defaultQueryHint 0x7f04016a
int attr defaultScrollFlagsEnabled 0x7f04016b
int attr defaultState 0x7f04016c
int attr defaultValue 0x7f04016d
int attr deltaPolarAngle 0x7f04016e
int attr deltaPolarRadius 0x7f04016f
int attr density 0x7f040170
int attr dependency 0x7f040171
int attr deriveConstraintsFrom 0x7f040172
int attr dialogCornerRadius 0x7f040173
int attr dialogIcon 0x7f040174
int attr dialogLayout 0x7f040175
int attr dialogMessage 0x7f040176
int attr dialogPreferenceStyle 0x7f040177
int attr dialogPreferredPadding 0x7f040178
int attr dialogTheme 0x7f040179
int attr dialogTitle 0x7f04017a
int attr disableDependentsState 0x7f04017b
int attr displayOptions 0x7f04017c
int attr divider 0x7f04017d
int attr dividerColor 0x7f04017e
int attr dividerHorizontal 0x7f04017f
int attr dividerInsetEnd 0x7f040180
int attr dividerInsetStart 0x7f040181
int attr dividerPadding 0x7f040182
int attr dividerThickness 0x7f040183
int attr dividerVertical 0x7f040184
int attr dragDirection 0x7f040185
int attr dragScale 0x7f040186
int attr dragThreshold 0x7f040187
int attr drawPath 0x7f040188
int attr drawableBottomCompat 0x7f040189
int attr drawableEndCompat 0x7f04018a
int attr drawableLeftCompat 0x7f04018b
int attr drawableRightCompat 0x7f04018c
int attr drawableSize 0x7f04018d
int attr drawableStartCompat 0x7f04018e
int attr drawableTint 0x7f04018f
int attr drawableTintMode 0x7f040190
int attr drawableTopCompat 0x7f040191
int attr drawerArrowStyle 0x7f040192
int attr drawerLayoutCornerSize 0x7f040193
int attr drawerLayoutStyle 0x7f040194
int attr dropDownBackgroundTint 0x7f040195
int attr dropDownListViewStyle 0x7f040196
int attr dropdownListPreferredItemHeight 0x7f040197
int attr dropdownPreferenceStyle 0x7f040198
int attr duration 0x7f040199
int attr dynamicColorThemeOverlay 0x7f04019a
int attr editTextBackground 0x7f04019b
int attr editTextColor 0x7f04019c
int attr editTextPreferenceStyle 0x7f04019d
int attr editTextStyle 0x7f04019e
int attr elevation 0x7f04019f
int attr elevationOverlayAccentColor 0x7f0401a0
int attr elevationOverlayColor 0x7f0401a1
int attr elevationOverlayEnabled 0x7f0401a2
int attr emojiCompatEnabled 0x7f0401a3
int attr enableCopying 0x7f0401a4
int attr enableEdgeToEdge 0x7f0401a5
int attr enabled 0x7f0401a6
int attr endIconCheckable 0x7f0401a7
int attr endIconContentDescription 0x7f0401a8
int attr endIconDrawable 0x7f0401a9
int attr endIconMinSize 0x7f0401aa
int attr endIconMode 0x7f0401ab
int attr endIconScaleType 0x7f0401ac
int attr endIconTint 0x7f0401ad
int attr endIconTintMode 0x7f0401ae
int attr enforceMaterialTheme 0x7f0401af
int attr enforceTextAppearance 0x7f0401b0
int attr ensureMinTouchTargetSize 0x7f0401b1
int attr entries 0x7f0401b2
int attr entryValues 0x7f0401b3
int attr errorAccessibilityLabel 0x7f0401b4
int attr errorAccessibilityLiveRegion 0x7f0401b5
int attr errorContentDescription 0x7f0401b6
int attr errorEnabled 0x7f0401b7
int attr errorIconDrawable 0x7f0401b8
int attr errorIconTint 0x7f0401b9
int attr errorIconTintMode 0x7f0401ba
int attr errorShown 0x7f0401bb
int attr errorTextAppearance 0x7f0401bc
int attr errorTextColor 0x7f0401bd
int attr expandActivityOverflowButtonDrawable 0x7f0401be
int attr expanded 0x7f0401bf
int attr expandedHintEnabled 0x7f0401c0
int attr expandedTitleGravity 0x7f0401c1
int attr expandedTitleMargin 0x7f0401c2
int attr expandedTitleMarginBottom 0x7f0401c3
int attr expandedTitleMarginEnd 0x7f0401c4
int attr expandedTitleMarginStart 0x7f0401c5
int attr expandedTitleMarginTop 0x7f0401c6
int attr expandedTitleTextAppearance 0x7f0401c7
int attr expandedTitleTextColor 0x7f0401c8
int attr extendMotionSpec 0x7f0401c9
int attr extendStrategy 0x7f0401ca
int attr extendedFloatingActionButtonPrimaryStyle 0x7f0401cb
int attr extendedFloatingActionButtonSecondaryStyle 0x7f0401cc
int attr extendedFloatingActionButtonStyle 0x7f0401cd
int attr extendedFloatingActionButtonSurfaceStyle 0x7f0401ce
int attr extendedFloatingActionButtonTertiaryStyle 0x7f0401cf
int attr extraMultilineHeightEnabled 0x7f0401d0
int attr fabAlignmentMode 0x7f0401d1
int attr fabAlignmentModeEndMargin 0x7f0401d2
int attr fabAnchorMode 0x7f0401d3
int attr fabAnimationMode 0x7f0401d4
int attr fabCradleMargin 0x7f0401d5
int attr fabCradleRoundedCornerRadius 0x7f0401d6
int attr fabCradleVerticalOffset 0x7f0401d7
int attr fabCustomSize 0x7f0401d8
int attr fabSize 0x7f0401d9
int attr fastScrollEnabled 0x7f0401da
int attr fastScrollHorizontalThumbDrawable 0x7f0401db
int attr fastScrollHorizontalTrackDrawable 0x7f0401dc
int attr fastScrollVerticalThumbDrawable 0x7f0401dd
int attr fastScrollVerticalTrackDrawable 0x7f0401de
int attr finishPrimaryWithSecondary 0x7f0401df
int attr finishSecondaryWithPrimary 0x7f0401e0
int attr firstBaselineToTopHeight 0x7f0401e1
int attr fl_clippingTransform 0x7f0401e2
int attr fl_fillColor 0x7f0401e3
int attr fl_fillDuration 0x7f0401e4
int attr fl_originalHeight 0x7f0401e5
int attr fl_originalWidth 0x7f0401e6
int attr fl_strokeColor 0x7f0401e7
int attr fl_strokeDrawingDuration 0x7f0401e8
int attr fl_strokeWidth 0x7f0401e9
int attr floatingActionButtonLargePrimaryStyle 0x7f0401ea
int attr floatingActionButtonLargeSecondaryStyle 0x7f0401eb
int attr floatingActionButtonLargeStyle 0x7f0401ec
int attr floatingActionButtonLargeSurfaceStyle 0x7f0401ed
int attr floatingActionButtonLargeTertiaryStyle 0x7f0401ee
int attr floatingActionButtonPrimaryStyle 0x7f0401ef
int attr floatingActionButtonSecondaryStyle 0x7f0401f0
int attr floatingActionButtonSmallPrimaryStyle 0x7f0401f1
int attr floatingActionButtonSmallSecondaryStyle 0x7f0401f2
int attr floatingActionButtonSmallStyle 0x7f0401f3
int attr floatingActionButtonSmallSurfaceStyle 0x7f0401f4
int attr floatingActionButtonSmallTertiaryStyle 0x7f0401f5
int attr floatingActionButtonStyle 0x7f0401f6
int attr floatingActionButtonSurfaceStyle 0x7f0401f7
int attr floatingActionButtonTertiaryStyle 0x7f0401f8
int attr flow_firstHorizontalBias 0x7f0401f9
int attr flow_firstHorizontalStyle 0x7f0401fa
int attr flow_firstVerticalBias 0x7f0401fb
int attr flow_firstVerticalStyle 0x7f0401fc
int attr flow_horizontalAlign 0x7f0401fd
int attr flow_horizontalBias 0x7f0401fe
int attr flow_horizontalGap 0x7f0401ff
int attr flow_horizontalStyle 0x7f040200
int attr flow_lastHorizontalBias 0x7f040201
int attr flow_lastHorizontalStyle 0x7f040202
int attr flow_lastVerticalBias 0x7f040203
int attr flow_lastVerticalStyle 0x7f040204
int attr flow_maxElementsWrap 0x7f040205
int attr flow_padding 0x7f040206
int attr flow_verticalAlign 0x7f040207
int attr flow_verticalBias 0x7f040208
int attr flow_verticalGap 0x7f040209
int attr flow_verticalStyle 0x7f04020a
int attr flow_wrapMode 0x7f04020b
int attr font 0x7f04020c
int attr fontFamily 0x7f04020d
int attr fontProviderAuthority 0x7f04020e
int attr fontProviderCerts 0x7f04020f
int attr fontProviderFetchStrategy 0x7f040210
int attr fontProviderFetchTimeout 0x7f040211
int attr fontProviderPackage 0x7f040212
int attr fontProviderQuery 0x7f040213
int attr fontProviderSystemFontFamily 0x7f040214
int attr fontStyle 0x7f040215
int attr fontVariationSettings 0x7f040216
int attr fontWeight 0x7f040217
int attr forceApplySystemWindowInsetTop 0x7f040218
int attr forceDefaultNavigationOnClickListener 0x7f040219
int attr foregroundInsidePadding 0x7f04021a
int attr fragment 0x7f04021b
int attr framePosition 0x7f04021c
int attr gapBetweenBars 0x7f04021d
int attr gestureInsetBottomIgnored 0x7f04021e
int attr goIcon 0x7f04021f
int attr haloColor 0x7f040220
int attr haloRadius 0x7f040221
int attr headerLayout 0x7f040222
int attr height 0x7f040223
int attr helperText 0x7f040224
int attr helperTextEnabled 0x7f040225
int attr helperTextTextAppearance 0x7f040226
int attr helperTextTextColor 0x7f040227
int attr hideAnimationBehavior 0x7f040228
int attr hideMotionSpec 0x7f040229
int attr hideNavigationIcon 0x7f04022a
int attr hideOnContentScroll 0x7f04022b
int attr hideOnScroll 0x7f04022c
int attr hintAnimationEnabled 0x7f04022d
int attr hintEnabled 0x7f04022e
int attr hintTextAppearance 0x7f04022f
int attr hintTextColor 0x7f040230
int attr homeAsUpIndicator 0x7f040231
int attr homeLayout 0x7f040232
int attr horizontalOffset 0x7f040233
int attr horizontalOffsetWithText 0x7f040234
int attr hoveredFocusedTranslationZ 0x7f040235
int attr icon 0x7f040236
int attr iconEndPadding 0x7f040237
int attr iconGravity 0x7f040238
int attr iconPadding 0x7f040239
int attr iconSize 0x7f04023a
int attr iconSpaceReserved 0x7f04023b
int attr iconStartPadding 0x7f04023c
int attr iconTint 0x7f04023d
int attr iconTintMode 0x7f04023e
int attr iconifiedByDefault 0x7f04023f
int attr imageAspectRatio 0x7f040240
int attr imageAspectRatioAdjust 0x7f040241
int attr imageButtonStyle 0x7f040242
int attr inVerticalOrientation 0x7f040243
int attr indeterminateAnimationType 0x7f040244
int attr indeterminateProgressStyle 0x7f040245
int attr indicatorColor 0x7f040246
int attr indicatorDirectionCircular 0x7f040247
int attr indicatorDirectionLinear 0x7f040248
int attr indicatorInset 0x7f040249
int attr indicatorSize 0x7f04024a
int attr indicatorTrackGapSize 0x7f04024b
int attr initialActivityCount 0x7f04024c
int attr initialColor 0x7f04024d
int attr initialExpandedChildrenCount 0x7f04024e
int attr insetForeground 0x7f04024f
int attr isLightTheme 0x7f040250
int attr isMaterial3DynamicColorApplied 0x7f040251
int attr isMaterial3Theme 0x7f040252
int attr isMaterialTheme 0x7f040253
int attr isPreferenceVisible 0x7f040254
int attr itemActiveIndicatorStyle 0x7f040255
int attr itemBackground 0x7f040256
int attr itemFillColor 0x7f040257
int attr itemHorizontalPadding 0x7f040258
int attr itemHorizontalTranslationEnabled 0x7f040259
int attr itemIconPadding 0x7f04025a
int attr itemIconSize 0x7f04025b
int attr itemIconTint 0x7f04025c
int attr itemMaxLines 0x7f04025d
int attr itemMinHeight 0x7f04025e
int attr itemPadding 0x7f04025f
int attr itemPaddingBottom 0x7f040260
int attr itemPaddingTop 0x7f040261
int attr itemRippleColor 0x7f040262
int attr itemShapeAppearance 0x7f040263
int attr itemShapeAppearanceOverlay 0x7f040264
int attr itemShapeFillColor 0x7f040265
int attr itemShapeInsetBottom 0x7f040266
int attr itemShapeInsetEnd 0x7f040267
int attr itemShapeInsetStart 0x7f040268
int attr itemShapeInsetTop 0x7f040269
int attr itemSpacing 0x7f04026a
int attr itemStrokeColor 0x7f04026b
int attr itemStrokeWidth 0x7f04026c
int attr itemTextAppearance 0x7f04026d
int attr itemTextAppearanceActive 0x7f04026e
int attr itemTextAppearanceActiveBoldEnabled 0x7f04026f
int attr itemTextAppearanceInactive 0x7f040270
int attr itemTextColor 0x7f040271
int attr itemVerticalPadding 0x7f040272
int attr key 0x7f040273
int attr keyPositionType 0x7f040274
int attr keyboardIcon 0x7f040275
int attr keylines 0x7f040276
int attr lStar 0x7f040277
int attr labelBehavior 0x7f040278
int attr labelStyle 0x7f040279
int attr labelVisibilityMode 0x7f04027a
int attr largeFontVerticalOffsetAdjustment 0x7f04027b
int attr lastBaselineToBottomHeight 0x7f04027c
int attr lastItemDecorated 0x7f04027d
int attr layout 0x7f04027e
int attr layoutDescription 0x7f04027f
int attr layoutDuringTransition 0x7f040280
int attr layoutManager 0x7f040281
int attr layout_anchor 0x7f040282
int attr layout_anchorGravity 0x7f040283
int attr layout_behavior 0x7f040284
int attr layout_collapseMode 0x7f040285
int attr layout_collapseParallaxMultiplier 0x7f040286
int attr layout_constrainedHeight 0x7f040287
int attr layout_constrainedWidth 0x7f040288
int attr layout_constraintBaseline_creator 0x7f040289
int attr layout_constraintBaseline_toBaselineOf 0x7f04028a
int attr layout_constraintBottom_creator 0x7f04028b
int attr layout_constraintBottom_toBottomOf 0x7f04028c
int attr layout_constraintBottom_toTopOf 0x7f04028d
int attr layout_constraintCircle 0x7f04028e
int attr layout_constraintCircleAngle 0x7f04028f
int attr layout_constraintCircleRadius 0x7f040290
int attr layout_constraintDimensionRatio 0x7f040291
int attr layout_constraintEnd_toEndOf 0x7f040292
int attr layout_constraintEnd_toStartOf 0x7f040293
int attr layout_constraintGuide_begin 0x7f040294
int attr layout_constraintGuide_end 0x7f040295
int attr layout_constraintGuide_percent 0x7f040296
int attr layout_constraintHeight_default 0x7f040297
int attr layout_constraintHeight_max 0x7f040298
int attr layout_constraintHeight_min 0x7f040299
int attr layout_constraintHeight_percent 0x7f04029a
int attr layout_constraintHorizontal_bias 0x7f04029b
int attr layout_constraintHorizontal_chainStyle 0x7f04029c
int attr layout_constraintHorizontal_weight 0x7f04029d
int attr layout_constraintLeft_creator 0x7f04029e
int attr layout_constraintLeft_toLeftOf 0x7f04029f
int attr layout_constraintLeft_toRightOf 0x7f0402a0
int attr layout_constraintRight_creator 0x7f0402a1
int attr layout_constraintRight_toLeftOf 0x7f0402a2
int attr layout_constraintRight_toRightOf 0x7f0402a3
int attr layout_constraintStart_toEndOf 0x7f0402a4
int attr layout_constraintStart_toStartOf 0x7f0402a5
int attr layout_constraintTag 0x7f0402a6
int attr layout_constraintTop_creator 0x7f0402a7
int attr layout_constraintTop_toBottomOf 0x7f0402a8
int attr layout_constraintTop_toTopOf 0x7f0402a9
int attr layout_constraintVertical_bias 0x7f0402aa
int attr layout_constraintVertical_chainStyle 0x7f0402ab
int attr layout_constraintVertical_weight 0x7f0402ac
int attr layout_constraintWidth_default 0x7f0402ad
int attr layout_constraintWidth_max 0x7f0402ae
int attr layout_constraintWidth_min 0x7f0402af
int attr layout_constraintWidth_percent 0x7f0402b0
int attr layout_dodgeInsetEdges 0x7f0402b1
int attr layout_editor_absoluteX 0x7f0402b2
int attr layout_editor_absoluteY 0x7f0402b3
int attr layout_goneMarginBottom 0x7f0402b4
int attr layout_goneMarginEnd 0x7f0402b5
int attr layout_goneMarginLeft 0x7f0402b6
int attr layout_goneMarginRight 0x7f0402b7
int attr layout_goneMarginStart 0x7f0402b8
int attr layout_goneMarginTop 0x7f0402b9
int attr layout_insetEdge 0x7f0402ba
int attr layout_keyline 0x7f0402bb
int attr layout_optimizationLevel 0x7f0402bc
int attr layout_scrollEffect 0x7f0402bd
int attr layout_scrollFlags 0x7f0402be
int attr layout_scrollInterpolator 0x7f0402bf
int attr liftOnScroll 0x7f0402c0
int attr liftOnScrollColor 0x7f0402c1
int attr liftOnScrollTargetViewId 0x7f0402c2
int attr lightnessSlider 0x7f0402c3
int attr lightnessSliderView 0x7f0402c4
int attr limitBoundsTo 0x7f0402c5
int attr lineHeight 0x7f0402c6
int attr lineSpacing 0x7f0402c7
int attr linearProgressIndicatorStyle 0x7f0402c8
int attr listChoiceBackgroundIndicator 0x7f0402c9
int attr listChoiceIndicatorMultipleAnimated 0x7f0402ca
int attr listChoiceIndicatorSingleAnimated 0x7f0402cb
int attr listDividerAlertDialog 0x7f0402cc
int attr listItemLayout 0x7f0402cd
int attr listLayout 0x7f0402ce
int attr listMenuViewStyle 0x7f0402cf
int attr listPopupWindowStyle 0x7f0402d0
int attr listPreferredItemHeight 0x7f0402d1
int attr listPreferredItemHeightLarge 0x7f0402d2
int attr listPreferredItemHeightSmall 0x7f0402d3
int attr listPreferredItemPaddingEnd 0x7f0402d4
int attr listPreferredItemPaddingLeft 0x7f0402d5
int attr listPreferredItemPaddingRight 0x7f0402d6
int attr listPreferredItemPaddingStart 0x7f0402d7
int attr logo 0x7f0402d8
int attr logoAdjustViewBounds 0x7f0402d9
int attr logoDescription 0x7f0402da
int attr logoScaleType 0x7f0402db
int attr marginHorizontal 0x7f0402dc
int attr marginLeftSystemWindowInsets 0x7f0402dd
int attr marginRightSystemWindowInsets 0x7f0402de
int attr marginTopSystemWindowInsets 0x7f0402df
int attr materialAlertDialogBodyTextStyle 0x7f0402e0
int attr materialAlertDialogButtonSpacerVisibility 0x7f0402e1
int attr materialAlertDialogTheme 0x7f0402e2
int attr materialAlertDialogTitleIconStyle 0x7f0402e3
int attr materialAlertDialogTitlePanelStyle 0x7f0402e4
int attr materialAlertDialogTitleTextStyle 0x7f0402e5
int attr materialButtonOutlinedStyle 0x7f0402e6
int attr materialButtonStyle 0x7f0402e7
int attr materialButtonToggleGroupStyle 0x7f0402e8
int attr materialCalendarDay 0x7f0402e9
int attr materialCalendarDayOfWeekLabel 0x7f0402ea
int attr materialCalendarFullscreenTheme 0x7f0402eb
int attr materialCalendarHeaderCancelButton 0x7f0402ec
int attr materialCalendarHeaderConfirmButton 0x7f0402ed
int attr materialCalendarHeaderDivider 0x7f0402ee
int attr materialCalendarHeaderLayout 0x7f0402ef
int attr materialCalendarHeaderSelection 0x7f0402f0
int attr materialCalendarHeaderTitle 0x7f0402f1
int attr materialCalendarHeaderToggleButton 0x7f0402f2
int attr materialCalendarMonth 0x7f0402f3
int attr materialCalendarMonthNavigationButton 0x7f0402f4
int attr materialCalendarStyle 0x7f0402f5
int attr materialCalendarTheme 0x7f0402f6
int attr materialCalendarYearNavigationButton 0x7f0402f7
int attr materialCardViewElevatedStyle 0x7f0402f8
int attr materialCardViewFilledStyle 0x7f0402f9
int attr materialCardViewOutlinedStyle 0x7f0402fa
int attr materialCardViewStyle 0x7f0402fb
int attr materialCircleRadius 0x7f0402fc
int attr materialClockStyle 0x7f0402fd
int attr materialDisplayDividerStyle 0x7f0402fe
int attr materialDividerHeavyStyle 0x7f0402ff
int attr materialDividerStyle 0x7f040300
int attr materialIconButtonFilledStyle 0x7f040301
int attr materialIconButtonFilledTonalStyle 0x7f040302
int attr materialIconButtonOutlinedStyle 0x7f040303
int attr materialIconButtonStyle 0x7f040304
int attr materialSearchBarStyle 0x7f040305
int attr materialSearchViewPrefixStyle 0x7f040306
int attr materialSearchViewStyle 0x7f040307
int attr materialSearchViewToolbarHeight 0x7f040308
int attr materialSearchViewToolbarStyle 0x7f040309
int attr materialSwitchStyle 0x7f04030a
int attr materialThemeOverlay 0x7f04030b
int attr materialTimePickerStyle 0x7f04030c
int attr materialTimePickerTheme 0x7f04030d
int attr materialTimePickerTitleStyle 0x7f04030e
int attr maxAcceleration 0x7f04030f
int attr maxActionInlineWidth 0x7f040310
int attr maxButtonHeight 0x7f040311
int attr maxCharacterCount 0x7f040312
int attr maxHeight 0x7f040313
int attr maxImageSize 0x7f040314
int attr maxLines 0x7f040315
int attr maxNumber 0x7f040316
int attr maxVelocity 0x7f040317
int attr maxWidth 0x7f040318
int attr measureWithLargestChild 0x7f040319
int attr menu 0x7f04031a
int attr menuAlignmentMode 0x7f04031b
int attr menuGravity 0x7f04031c
int attr min 0x7f04031d
int attr minHeight 0x7f04031e
int attr minHideDelay 0x7f04031f
int attr minSeparation 0x7f040320
int attr minTouchTargetSize 0x7f040321
int attr minWidth 0x7f040322
int attr mock_diagonalsColor 0x7f040323
int attr mock_label 0x7f040324
int attr mock_labelBackgroundColor 0x7f040325
int attr mock_labelColor 0x7f040326
int attr mock_showDiagonals 0x7f040327
int attr mock_showLabel 0x7f040328
int attr motionDebug 0x7f040329
int attr motionDurationExtraLong1 0x7f04032a
int attr motionDurationExtraLong2 0x7f04032b
int attr motionDurationExtraLong3 0x7f04032c
int attr motionDurationExtraLong4 0x7f04032d
int attr motionDurationLong1 0x7f04032e
int attr motionDurationLong2 0x7f04032f
int attr motionDurationLong3 0x7f040330
int attr motionDurationLong4 0x7f040331
int attr motionDurationMedium1 0x7f040332
int attr motionDurationMedium2 0x7f040333
int attr motionDurationMedium3 0x7f040334
int attr motionDurationMedium4 0x7f040335
int attr motionDurationShort1 0x7f040336
int attr motionDurationShort2 0x7f040337
int attr motionDurationShort3 0x7f040338
int attr motionDurationShort4 0x7f040339
int attr motionEasingAccelerated 0x7f04033a
int attr motionEasingDecelerated 0x7f04033b
int attr motionEasingEmphasized 0x7f04033c
int attr motionEasingEmphasizedAccelerateInterpolator 0x7f04033d
int attr motionEasingEmphasizedDecelerateInterpolator 0x7f04033e
int attr motionEasingEmphasizedInterpolator 0x7f04033f
int attr motionEasingLinear 0x7f040340
int attr motionEasingLinearInterpolator 0x7f040341
int attr motionEasingStandard 0x7f040342
int attr motionEasingStandardAccelerateInterpolator 0x7f040343
int attr motionEasingStandardDecelerateInterpolator 0x7f040344
int attr motionEasingStandardInterpolator 0x7f040345
int attr motionInterpolator 0x7f040346
int attr motionPath 0x7f040347
int attr motionPathRotate 0x7f040348
int attr motionProgress 0x7f040349
int attr motionStagger 0x7f04034a
int attr motionTarget 0x7f04034b
int attr motion_postLayoutCollision 0x7f04034c
int attr motion_triggerOnCollision 0x7f04034d
int attr moveWhenScrollAtTop 0x7f04034e
int attr multiChoiceItemLayout 0x7f04034f
int attr navigationContentDescription 0x7f040350
int attr navigationIcon 0x7f040351
int attr navigationIconTint 0x7f040352
int attr navigationMode 0x7f040353
int attr navigationRailStyle 0x7f040354
int attr navigationViewStyle 0x7f040355
int attr negativeButtonText 0x7f040356
int attr nestedScrollFlags 0x7f040357
int attr nestedScrollViewStyle 0x7f040358
int attr nestedScrollable 0x7f040359
int attr number 0x7f04035a
int attr numericModifiers 0x7f04035b
int attr offsetAlignmentMode 0x7f04035c
int attr onCross 0x7f04035d
int attr onHide 0x7f04035e
int attr onNegativeCross 0x7f04035f
int attr onPositiveCross 0x7f040360
int attr onShow 0x7f040361
int attr onTouchUp 0x7f040362
int attr order 0x7f040363
int attr orderingFromXml 0x7f040364
int attr overlapAnchor 0x7f040365
int attr overlay 0x7f040366
int attr paddingBottomNoButtons 0x7f040367
int attr paddingBottomSystemWindowInsets 0x7f040368
int attr paddingEnd 0x7f040369
int attr paddingLeftSystemWindowInsets 0x7f04036a
int attr paddingRightSystemWindowInsets 0x7f04036b
int attr paddingStart 0x7f04036c
int attr paddingStartSystemWindowInsets 0x7f04036d
int attr paddingTopNoTitle 0x7f04036e
int attr paddingTopSystemWindowInsets 0x7f04036f
int attr panelBackground 0x7f040370
int attr panelMenuListTheme 0x7f040371
int attr panelMenuListWidth 0x7f040372
int attr passwordToggleContentDescription 0x7f040373
int attr passwordToggleDrawable 0x7f040374
int attr passwordToggleEnabled 0x7f040375
int attr passwordToggleTint 0x7f040376
int attr passwordToggleTintMode 0x7f040377
int attr pathMotionArc 0x7f040378
int attr path_percent 0x7f040379
int attr percentHeight 0x7f04037a
int attr percentWidth 0x7f04037b
int attr percentX 0x7f04037c
int attr percentY 0x7f04037d
int attr perpendicularPath_percent 0x7f04037e
int attr persistent 0x7f04037f
int attr pickerButtonCancel 0x7f040380
int attr pickerButtonOk 0x7f040381
int attr pickerColorEdit 0x7f040382
int attr pickerColorEditTextColor 0x7f040383
int attr pickerTitle 0x7f040384
int attr pivotAnchor 0x7f040385
int attr placeholderActivityName 0x7f040386
int attr placeholderText 0x7f040387
int attr placeholderTextAppearance 0x7f040388
int attr placeholderTextColor 0x7f040389
int attr placeholder_emptyVisibility 0x7f04038a
int attr popupMenuBackground 0x7f04038b
int attr popupMenuStyle 0x7f04038c
int attr popupTheme 0x7f04038d
int attr popupWindowStyle 0x7f04038e
int attr positiveButtonText 0x7f04038f
int attr preferenceCategoryStyle 0x7f040390
int attr preferenceCategoryTitleTextAppearance 0x7f040391
int attr preferenceCategoryTitleTextColor 0x7f040392
int attr preferenceFragmentCompatStyle 0x7f040393
int attr preferenceFragmentListStyle 0x7f040394
int attr preferenceFragmentStyle 0x7f040395
int attr preferenceInformationStyle 0x7f040396
int attr preferenceScreenStyle 0x7f040397
int attr preferenceStyle 0x7f040398
int attr preferenceTheme 0x7f040399
int attr prefixText 0x7f04039a
int attr prefixTextAppearance 0x7f04039b
int attr prefixTextColor 0x7f04039c
int attr preserveIconSpacing 0x7f04039d
int attr pressedTranslationZ 0x7f04039e
int attr primaryActivityName 0x7f04039f
int attr progressBarPadding 0x7f0403a0
int attr progressBarStyle 0x7f0403a1
int attr queryBackground 0x7f0403a2
int attr queryHint 0x7f0403a3
int attr queryPatterns 0x7f0403a4
int attr radioButtonStyle 0x7f0403a5
int attr rangeFillColor 0x7f0403a6
int attr ratingBarStyle 0x7f0403a7
int attr ratingBarStyleIndicator 0x7f0403a8
int attr ratingBarStyleSmall 0x7f0403a9
int attr recyclerViewStyle 0x7f0403aa
int attr region_heightLessThan 0x7f0403ab
int attr region_heightMoreThan 0x7f0403ac
int attr region_widthLessThan 0x7f0403ad
int attr region_widthMoreThan 0x7f0403ae
int attr removeEmbeddedFabElevation 0x7f0403af
int attr reverseLayout 0x7f0403b0
int attr rippleColor 0x7f0403b1
int attr round 0x7f0403b2
int attr roundPercent 0x7f0403b3
int attr saturation 0x7f0403b4
int attr scopeUris 0x7f0403b5
int attr scrimAnimationDuration 0x7f0403b6
int attr scrimBackground 0x7f0403b7
int attr scrimVisibleHeightTrigger 0x7f0403b8
int attr searchHintIcon 0x7f0403b9
int attr searchIcon 0x7f0403ba
int attr searchPrefixText 0x7f0403bb
int attr searchViewStyle 0x7f0403bc
int attr secondaryActivityAction 0x7f0403bd
int attr secondaryActivityName 0x7f0403be
int attr seekBarIncrement 0x7f0403bf
int attr seekBarPreferenceStyle 0x7f0403c0
int attr seekBarStyle 0x7f0403c1
int attr selectable 0x7f0403c2
int attr selectableItemBackground 0x7f0403c3
int attr selectableItemBackgroundBorderless 0x7f0403c4
int attr selectionRequired 0x7f0403c5
int attr selectorSize 0x7f0403c6
int attr shapeAppearance 0x7f0403c7
int attr shapeAppearanceCornerExtraLarge 0x7f0403c8
int attr shapeAppearanceCornerExtraSmall 0x7f0403c9
int attr shapeAppearanceCornerLarge 0x7f0403ca
int attr shapeAppearanceCornerMedium 0x7f0403cb
int attr shapeAppearanceCornerSmall 0x7f0403cc
int attr shapeAppearanceLargeComponent 0x7f0403cd
int attr shapeAppearanceMediumComponent 0x7f0403ce
int attr shapeAppearanceOverlay 0x7f0403cf
int attr shapeAppearanceSmallComponent 0x7f0403d0
int attr shapeCornerFamily 0x7f0403d1
int attr shortcutMatchRequired 0x7f0403d2
int attr shouldDisableView 0x7f0403d3
int attr shouldRemoveExpandedCorners 0x7f0403d4
int attr showAnimationBehavior 0x7f0403d5
int attr showAsAction 0x7f0403d6
int attr showDelay 0x7f0403d7
int attr showDividers 0x7f0403d8
int attr showMarker 0x7f0403d9
int attr showMotionSpec 0x7f0403da
int attr showPaths 0x7f0403db
int attr showSeekBarValue 0x7f0403dc
int attr showText 0x7f0403dd
int attr showTitle 0x7f0403de
int attr shrinkMotionSpec 0x7f0403df
int attr sideSheetDialogTheme 0x7f0403e0
int attr sideSheetModalStyle 0x7f0403e1
int attr simpleItemLayout 0x7f0403e2
int attr simpleItemSelectedColor 0x7f0403e3
int attr simpleItemSelectedRippleColor 0x7f0403e4
int attr simpleItems 0x7f0403e5
int attr singleChoiceItemLayout 0x7f0403e6
int attr singleLine 0x7f0403e7
int attr singleLineTitle 0x7f0403e8
int attr singleSelection 0x7f0403e9
int attr sizePercent 0x7f0403ea
int attr sliderStyle 0x7f0403eb
int attr snackbarButtonStyle 0x7f0403ec
int attr snackbarStyle 0x7f0403ed
int attr snackbarTextViewStyle 0x7f0403ee
int attr spanCount 0x7f0403ef
int attr spinBars 0x7f0403f0
int attr spinnerDropDownItemStyle 0x7f0403f1
int attr spinnerStyle 0x7f0403f2
int attr splitLayoutDirection 0x7f0403f3
int attr splitMinSmallestWidth 0x7f0403f4
int attr splitMinWidth 0x7f0403f5
int attr splitRatio 0x7f0403f6
int attr splitTrack 0x7f0403f7
int attr srcCompat 0x7f0403f8
int attr stackFromEnd 0x7f0403f9
int attr staggered 0x7f0403fa
int attr startIconCheckable 0x7f0403fb
int attr startIconContentDescription 0x7f0403fc
int attr startIconDrawable 0x7f0403fd
int attr startIconMinSize 0x7f0403fe
int attr startIconScaleType 0x7f0403ff
int attr startIconTint 0x7f040400
int attr startIconTintMode 0x7f040401
int attr state_above_anchor 0x7f040402
int attr state_collapsed 0x7f040403
int attr state_collapsible 0x7f040404
int attr state_dragged 0x7f040405
int attr state_error 0x7f040406
int attr state_indeterminate 0x7f040407
int attr state_liftable 0x7f040408
int attr state_lifted 0x7f040409
int attr state_with_icon 0x7f04040a
int attr statusBarBackground 0x7f04040b
int attr statusBarForeground 0x7f04040c
int attr statusBarScrim 0x7f04040d
int attr strokeColor 0x7f04040e
int attr strokeWidth 0x7f04040f
int attr subMenuArrow 0x7f040410
int attr subheaderColor 0x7f040411
int attr subheaderInsetEnd 0x7f040412
int attr subheaderInsetStart 0x7f040413
int attr subheaderTextAppearance 0x7f040414
int attr submitBackground 0x7f040415
int attr subtitle 0x7f040416
int attr subtitleCentered 0x7f040417
int attr subtitleTextAppearance 0x7f040418
int attr subtitleTextColor 0x7f040419
int attr subtitleTextStyle 0x7f04041a
int attr suffixText 0x7f04041b
int attr suffixTextAppearance 0x7f04041c
int attr suffixTextColor 0x7f04041d
int attr suggestionRowLayout 0x7f04041e
int attr summary 0x7f04041f
int attr summaryOff 0x7f040420
int attr summaryOn 0x7f040421
int attr switchMinWidth 0x7f040422
int attr switchPadding 0x7f040423
int attr switchPreferenceCompatStyle 0x7f040424
int attr switchPreferenceStyle 0x7f040425
int attr switchStyle 0x7f040426
int attr switchTextAppearance 0x7f040427
int attr switchTextOff 0x7f040428
int attr switchTextOn 0x7f040429
int attr tabBackground 0x7f04042a
int attr tabContentStart 0x7f04042b
int attr tabGravity 0x7f04042c
int attr tabIconTint 0x7f04042d
int attr tabIconTintMode 0x7f04042e
int attr tabIndicator 0x7f04042f
int attr tabIndicatorAnimationDuration 0x7f040430
int attr tabIndicatorAnimationMode 0x7f040431
int attr tabIndicatorColor 0x7f040432
int attr tabIndicatorFullWidth 0x7f040433
int attr tabIndicatorGravity 0x7f040434
int attr tabIndicatorHeight 0x7f040435
int attr tabInlineLabel 0x7f040436
int attr tabMaxWidth 0x7f040437
int attr tabMinWidth 0x7f040438
int attr tabMode 0x7f040439
int attr tabPadding 0x7f04043a
int attr tabPaddingBottom 0x7f04043b
int attr tabPaddingEnd 0x7f04043c
int attr tabPaddingStart 0x7f04043d
int attr tabPaddingTop 0x7f04043e
int attr tabRippleColor 0x7f04043f
int attr tabSecondaryStyle 0x7f040440
int attr tabSelectedTextAppearance 0x7f040441
int attr tabSelectedTextColor 0x7f040442
int attr tabStyle 0x7f040443
int attr tabTextAppearance 0x7f040444
int attr tabTextColor 0x7f040445
int attr tabUnboundedRipple 0x7f040446
int attr targetId 0x7f040447
int attr telltales_tailColor 0x7f040448
int attr telltales_tailScale 0x7f040449
int attr telltales_velocityMode 0x7f04044a
int attr textAllCaps 0x7f04044b
int attr textAppearanceBody1 0x7f04044c
int attr textAppearanceBody2 0x7f04044d
int attr textAppearanceBodyLarge 0x7f04044e
int attr textAppearanceBodyMedium 0x7f04044f
int attr textAppearanceBodySmall 0x7f040450
int attr textAppearanceButton 0x7f040451
int attr textAppearanceCaption 0x7f040452
int attr textAppearanceDisplayLarge 0x7f040453
int attr textAppearanceDisplayMedium 0x7f040454
int attr textAppearanceDisplaySmall 0x7f040455
int attr textAppearanceHeadline1 0x7f040456
int attr textAppearanceHeadline2 0x7f040457
int attr textAppearanceHeadline3 0x7f040458
int attr textAppearanceHeadline4 0x7f040459
int attr textAppearanceHeadline5 0x7f04045a
int attr textAppearanceHeadline6 0x7f04045b
int attr textAppearanceHeadlineLarge 0x7f04045c
int attr textAppearanceHeadlineMedium 0x7f04045d
int attr textAppearanceHeadlineSmall 0x7f04045e
int attr textAppearanceLabelLarge 0x7f04045f
int attr textAppearanceLabelMedium 0x7f040460
int attr textAppearanceLabelSmall 0x7f040461
int attr textAppearanceLargePopupMenu 0x7f040462
int attr textAppearanceLineHeightEnabled 0x7f040463
int attr textAppearanceListItem 0x7f040464
int attr textAppearanceListItemSecondary 0x7f040465
int attr textAppearanceListItemSmall 0x7f040466
int attr textAppearanceOverline 0x7f040467
int attr textAppearancePopupMenuHeader 0x7f040468
int attr textAppearanceSearchResultSubtitle 0x7f040469
int attr textAppearanceSearchResultTitle 0x7f04046a
int attr textAppearanceSmallPopupMenu 0x7f04046b
int attr textAppearanceSubtitle1 0x7f04046c
int attr textAppearanceSubtitle2 0x7f04046d
int attr textAppearanceTitleLarge 0x7f04046e
int attr textAppearanceTitleMedium 0x7f04046f
int attr textAppearanceTitleSmall 0x7f040470
int attr textColorAlertDialogListItem 0x7f040471
int attr textColorSearchUrl 0x7f040472
int attr textEndPadding 0x7f040473
int attr textInputFilledDenseStyle 0x7f040474
int attr textInputFilledExposedDropdownMenuStyle 0x7f040475
int attr textInputFilledStyle 0x7f040476
int attr textInputLayoutFocusedRectEnabled 0x7f040477
int attr textInputOutlinedDenseStyle 0x7f040478
int attr textInputOutlinedExposedDropdownMenuStyle 0x7f040479
int attr textInputOutlinedStyle 0x7f04047a
int attr textInputStyle 0x7f04047b
int attr textLocale 0x7f04047c
int attr textStartPadding 0x7f04047d
int attr theme 0x7f04047e
int attr thickness 0x7f04047f
int attr thumbColor 0x7f040480
int attr thumbElevation 0x7f040481
int attr thumbHeight 0x7f040482
int attr thumbIcon 0x7f040483
int attr thumbIconSize 0x7f040484
int attr thumbIconTint 0x7f040485
int attr thumbIconTintMode 0x7f040486
int attr thumbRadius 0x7f040487
int attr thumbStrokeColor 0x7f040488
int attr thumbStrokeWidth 0x7f040489
int attr thumbTextPadding 0x7f04048a
int attr thumbTint 0x7f04048b
int attr thumbTintMode 0x7f04048c
int attr thumbTrackGapSize 0x7f04048d
int attr thumbWidth 0x7f04048e
int attr tickColor 0x7f04048f
int attr tickColorActive 0x7f040490
int attr tickColorInactive 0x7f040491
int attr tickMark 0x7f040492
int attr tickMarkTint 0x7f040493
int attr tickMarkTintMode 0x7f040494
int attr tickRadiusActive 0x7f040495
int attr tickRadiusInactive 0x7f040496
int attr tickVisible 0x7f040497
int attr tint 0x7f040498
int attr tintMode 0x7f040499
int attr tintNavigationIcon 0x7f04049a
int attr title 0x7f04049b
int attr titleCentered 0x7f04049c
int attr titleCollapseMode 0x7f04049d
int attr titleEnabled 0x7f04049e
int attr titleMargin 0x7f04049f
int attr titleMarginBottom 0x7f0404a0
int attr titleMarginEnd 0x7f0404a1
int attr titleMarginStart 0x7f0404a2
int attr titleMarginTop 0x7f0404a3
int attr titleMargins 0x7f0404a4
int attr titlePositionInterpolator 0x7f0404a5
int attr titleTextAppearance 0x7f0404a6
int attr titleTextColor 0x7f0404a7
int attr titleTextEllipsize 0x7f0404a8
int attr titleTextStyle 0x7f0404a9
int attr toggleCheckedStateOnClick 0x7f0404aa
int attr toolbarId 0x7f0404ab
int attr toolbarNavigationButtonStyle 0x7f0404ac
int attr toolbarStyle 0x7f0404ad
int attr toolbarSurfaceStyle 0x7f0404ae
int attr tooltipForegroundColor 0x7f0404af
int attr tooltipFrameBackground 0x7f0404b0
int attr tooltipStyle 0x7f0404b1
int attr tooltipText 0x7f0404b2
int attr topInsetScrimEnabled 0x7f0404b3
int attr touchAnchorId 0x7f0404b4
int attr touchAnchorSide 0x7f0404b5
int attr touchRegionId 0x7f0404b6
int attr track 0x7f0404b7
int attr trackColor 0x7f0404b8
int attr trackColorActive 0x7f0404b9
int attr trackColorInactive 0x7f0404ba
int attr trackCornerRadius 0x7f0404bb
int attr trackDecoration 0x7f0404bc
int attr trackDecorationTint 0x7f0404bd
int attr trackDecorationTintMode 0x7f0404be
int attr trackHeight 0x7f0404bf
int attr trackInsideCornerSize 0x7f0404c0
int attr trackStopIndicatorSize 0x7f0404c1
int attr trackThickness 0x7f0404c2
int attr trackTint 0x7f0404c3
int attr trackTintMode 0x7f0404c4
int attr transitionDisable 0x7f0404c5
int attr transitionEasing 0x7f0404c6
int attr transitionFlags 0x7f0404c7
int attr transitionPathRotate 0x7f0404c8
int attr transitionShapeAppearance 0x7f0404c9
int attr triggerId 0x7f0404ca
int attr triggerReceiver 0x7f0404cb
int attr triggerSlack 0x7f0404cc
int attr ttcIndex 0x7f0404cd
int attr updatesContinuously 0x7f0404ce
int attr useCompatPadding 0x7f0404cf
int attr useDrawerArrowDrawable 0x7f0404d0
int attr useMaterialThemeColors 0x7f0404d1
int attr useSimpleSummaryProvider 0x7f0404d2
int attr values 0x7f0404d3
int attr verticalOffset 0x7f0404d4
int attr verticalOffsetWithText 0x7f0404d5
int attr viewInflaterClass 0x7f0404d6
int attr visibilityMode 0x7f0404d7
int attr voiceIcon 0x7f0404d8
int attr warmth 0x7f0404d9
int attr waveDecay 0x7f0404da
int attr waveOffset 0x7f0404db
int attr wavePeriod 0x7f0404dc
int attr waveShape 0x7f0404dd
int attr waveVariesBy 0x7f0404de
int attr wheelType 0x7f0404df
int attr widgetLayout 0x7f0404e0
int attr windowActionBar 0x7f0404e1
int attr windowActionBarOverlay 0x7f0404e2
int attr windowActionModeOverlay 0x7f0404e3
int attr windowFixedHeightMajor 0x7f0404e4
int attr windowFixedHeightMinor 0x7f0404e5
int attr windowFixedWidthMajor 0x7f0404e6
int attr windowFixedWidthMinor 0x7f0404e7
int attr windowMinWidthMajor 0x7f0404e8
int attr windowMinWidthMinor 0x7f0404e9
int attr windowNoTitle 0x7f0404ea
int attr yearSelectedStyle 0x7f0404eb
int attr yearStyle 0x7f0404ec
int attr yearTodayStyle 0x7f0404ed
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_config_actionMenuItemAllCaps 0x7f050001
int bool config_materialPreferenceIconSpaceReserved 0x7f050002
int bool enable_system_alarm_service_default 0x7f050003
int bool enable_system_foreground_service_default 0x7f050004
int bool enable_system_job_service_default 0x7f050005
int bool mtrl_btn_textappearance_all_caps 0x7f050006
int bool workmanager_test_configuration 0x7f050007
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color animals_color 0x7f06001d
int color background 0x7f06001e
int color background_floating_material_dark 0x7f06001f
int color background_floating_material_light 0x7f060020
int color background_material_dark 0x7f060021
int color background_material_light 0x7f060022
int color bckg 0x7f060023
int color black 0x7f060024
int color bright_foreground_disabled_material_dark 0x7f060025
int color bright_foreground_disabled_material_light 0x7f060026
int color bright_foreground_inverse_material_dark 0x7f060027
int color bright_foreground_inverse_material_light 0x7f060028
int color bright_foreground_material_dark 0x7f060029
int color bright_foreground_material_light 0x7f06002a
int color brown 0x7f06002b
int color browser_actions_bg_grey 0x7f06002c
int color browser_actions_divider_color 0x7f06002d
int color browser_actions_text_color 0x7f06002e
int color browser_actions_title_color 0x7f06002f
int color button_material_dark 0x7f060030
int color button_material_light 0x7f060031
int color call_notification_answer_color 0x7f060032
int color call_notification_decline_color 0x7f060033
int color cardview_dark_background 0x7f060034
int color cardview_light_background 0x7f060035
int color cardview_shadow_end_color 0x7f060036
int color cardview_shadow_start_color 0x7f060037
int color cartoons_color 0x7f060038
int color colorAccent 0x7f060039
int color colorPrimary 0x7f06003a
int color colorPrimaryDark 0x7f06003b
int color common_google_signin_btn_text_dark 0x7f06003c
int color common_google_signin_btn_text_dark_default 0x7f06003d
int color common_google_signin_btn_text_dark_disabled 0x7f06003e
int color common_google_signin_btn_text_dark_focused 0x7f06003f
int color common_google_signin_btn_text_dark_pressed 0x7f060040
int color common_google_signin_btn_text_light 0x7f060041
int color common_google_signin_btn_text_light_default 0x7f060042
int color common_google_signin_btn_text_light_disabled 0x7f060043
int color common_google_signin_btn_text_light_focused 0x7f060044
int color common_google_signin_btn_text_light_pressed 0x7f060045
int color common_google_signin_btn_tint 0x7f060046
int color deep_blue 0x7f060047
int color deep_green 0x7f060048
int color deep_orange 0x7f060049
int color deep_pink 0x7f06004a
int color deep_purple 0x7f06004b
int color design_bottom_navigation_shadow_color 0x7f06004c
int color design_box_stroke_color 0x7f06004d
int color design_dark_default_color_background 0x7f06004e
int color design_dark_default_color_error 0x7f06004f
int color design_dark_default_color_on_background 0x7f060050
int color design_dark_default_color_on_error 0x7f060051
int color design_dark_default_color_on_primary 0x7f060052
int color design_dark_default_color_on_secondary 0x7f060053
int color design_dark_default_color_on_surface 0x7f060054
int color design_dark_default_color_primary 0x7f060055
int color design_dark_default_color_primary_dark 0x7f060056
int color design_dark_default_color_primary_variant 0x7f060057
int color design_dark_default_color_secondary 0x7f060058
int color design_dark_default_color_secondary_variant 0x7f060059
int color design_dark_default_color_surface 0x7f06005a
int color design_default_color_background 0x7f06005b
int color design_default_color_error 0x7f06005c
int color design_default_color_on_background 0x7f06005d
int color design_default_color_on_error 0x7f06005e
int color design_default_color_on_primary 0x7f06005f
int color design_default_color_on_secondary 0x7f060060
int color design_default_color_on_surface 0x7f060061
int color design_default_color_primary 0x7f060062
int color design_default_color_primary_dark 0x7f060063
int color design_default_color_primary_variant 0x7f060064
int color design_default_color_secondary 0x7f060065
int color design_default_color_secondary_variant 0x7f060066
int color design_default_color_surface 0x7f060067
int color design_error 0x7f060068
int color design_fab_shadow_end_color 0x7f060069
int color design_fab_shadow_mid_color 0x7f06006a
int color design_fab_shadow_start_color 0x7f06006b
int color design_fab_stroke_end_inner_color 0x7f06006c
int color design_fab_stroke_end_outer_color 0x7f06006d
int color design_fab_stroke_top_inner_color 0x7f06006e
int color design_fab_stroke_top_outer_color 0x7f06006f
int color design_icon_tint 0x7f060070
int color design_snackbar_background_color 0x7f060071
int color dim_foreground_disabled_material_dark 0x7f060072
int color dim_foreground_disabled_material_light 0x7f060073
int color dim_foreground_material_dark 0x7f060074
int color dim_foreground_material_light 0x7f060075
int color divider 0x7f060076
int color error_color_material_dark 0x7f060077
int color error_color_material_light 0x7f060078
int color fillColor 0x7f060079
int color filn 0x7f06007a
int color flowers_color 0x7f06007b
int color foods_color 0x7f06007c
int color foreground_material_dark 0x7f06007d
int color foreground_material_light 0x7f06007e
int color gradient_center 0x7f06007f
int color gradient_end 0x7f060080
int color gradient_start 0x7f060081
int color gray 0x7f060082
int color highlighted_text_material_dark 0x7f060083
int color highlighted_text_material_light 0x7f060084
int color light 0x7f060085
int color light_blue 0x7f060086
int color light_green 0x7f060087
int color light_orange 0x7f060088
int color light_pink 0x7f060089
int color light_purple 0x7f06008a
int color m3_appbar_overlay_color 0x7f06008b
int color m3_assist_chip_icon_tint_color 0x7f06008c
int color m3_assist_chip_stroke_color 0x7f06008d
int color m3_bottom_sheet_drag_handle_color 0x7f06008e
int color m3_button_background_color_selector 0x7f06008f
int color m3_button_foreground_color_selector 0x7f060090
int color m3_button_outline_color_selector 0x7f060091
int color m3_button_ripple_color 0x7f060092
int color m3_button_ripple_color_selector 0x7f060093
int color m3_calendar_item_disabled_text 0x7f060094
int color m3_calendar_item_stroke_color 0x7f060095
int color m3_card_foreground_color 0x7f060096
int color m3_card_ripple_color 0x7f060097
int color m3_card_stroke_color 0x7f060098
int color m3_checkbox_button_icon_tint 0x7f060099
int color m3_checkbox_button_tint 0x7f06009a
int color m3_chip_assist_text_color 0x7f06009b
int color m3_chip_background_color 0x7f06009c
int color m3_chip_ripple_color 0x7f06009d
int color m3_chip_stroke_color 0x7f06009e
int color m3_chip_text_color 0x7f06009f
int color m3_dark_default_color_primary_text 0x7f0600a0
int color m3_dark_default_color_secondary_text 0x7f0600a1
int color m3_dark_highlighted_text 0x7f0600a2
int color m3_dark_hint_foreground 0x7f0600a3
int color m3_dark_primary_text_disable_only 0x7f0600a4
int color m3_default_color_primary_text 0x7f0600a5
int color m3_default_color_secondary_text 0x7f0600a6
int color m3_dynamic_dark_default_color_primary_text 0x7f0600a7
int color m3_dynamic_dark_default_color_secondary_text 0x7f0600a8
int color m3_dynamic_dark_highlighted_text 0x7f0600a9
int color m3_dynamic_dark_hint_foreground 0x7f0600aa
int color m3_dynamic_dark_primary_text_disable_only 0x7f0600ab
int color m3_dynamic_default_color_primary_text 0x7f0600ac
int color m3_dynamic_default_color_secondary_text 0x7f0600ad
int color m3_dynamic_highlighted_text 0x7f0600ae
int color m3_dynamic_hint_foreground 0x7f0600af
int color m3_dynamic_primary_text_disable_only 0x7f0600b0
int color m3_efab_ripple_color_selector 0x7f0600b1
int color m3_elevated_chip_background_color 0x7f0600b2
int color m3_fab_efab_background_color_selector 0x7f0600b3
int color m3_fab_efab_foreground_color_selector 0x7f0600b4
int color m3_fab_ripple_color_selector 0x7f0600b5
int color m3_filled_icon_button_container_color_selector 0x7f0600b6
int color m3_highlighted_text 0x7f0600b7
int color m3_hint_foreground 0x7f0600b8
int color m3_icon_button_icon_color_selector 0x7f0600b9
int color m3_navigation_bar_item_with_indicator_icon_tint 0x7f0600ba
int color m3_navigation_bar_item_with_indicator_label_tint 0x7f0600bb
int color m3_navigation_bar_ripple_color_selector 0x7f0600bc
int color m3_navigation_item_background_color 0x7f0600bd
int color m3_navigation_item_icon_tint 0x7f0600be
int color m3_navigation_item_ripple_color 0x7f0600bf
int color m3_navigation_item_text_color 0x7f0600c0
int color m3_navigation_rail_item_with_indicator_icon_tint 0x7f0600c1
int color m3_navigation_rail_item_with_indicator_label_tint 0x7f0600c2
int color m3_navigation_rail_ripple_color_selector 0x7f0600c3
int color m3_popupmenu_overlay_color 0x7f0600c4
int color m3_primary_text_disable_only 0x7f0600c5
int color m3_radiobutton_button_tint 0x7f0600c6
int color m3_radiobutton_ripple_tint 0x7f0600c7
int color m3_ref_palette_black 0x7f0600c8
int color m3_ref_palette_dynamic_neutral0 0x7f0600c9
int color m3_ref_palette_dynamic_neutral10 0x7f0600ca
int color m3_ref_palette_dynamic_neutral100 0x7f0600cb
int color m3_ref_palette_dynamic_neutral12 0x7f0600cc
int color m3_ref_palette_dynamic_neutral17 0x7f0600cd
int color m3_ref_palette_dynamic_neutral20 0x7f0600ce
int color m3_ref_palette_dynamic_neutral22 0x7f0600cf
int color m3_ref_palette_dynamic_neutral24 0x7f0600d0
int color m3_ref_palette_dynamic_neutral30 0x7f0600d1
int color m3_ref_palette_dynamic_neutral4 0x7f0600d2
int color m3_ref_palette_dynamic_neutral40 0x7f0600d3
int color m3_ref_palette_dynamic_neutral50 0x7f0600d4
int color m3_ref_palette_dynamic_neutral6 0x7f0600d5
int color m3_ref_palette_dynamic_neutral60 0x7f0600d6
int color m3_ref_palette_dynamic_neutral70 0x7f0600d7
int color m3_ref_palette_dynamic_neutral80 0x7f0600d8
int color m3_ref_palette_dynamic_neutral87 0x7f0600d9
int color m3_ref_palette_dynamic_neutral90 0x7f0600da
int color m3_ref_palette_dynamic_neutral92 0x7f0600db
int color m3_ref_palette_dynamic_neutral94 0x7f0600dc
int color m3_ref_palette_dynamic_neutral95 0x7f0600dd
int color m3_ref_palette_dynamic_neutral96 0x7f0600de
int color m3_ref_palette_dynamic_neutral98 0x7f0600df
int color m3_ref_palette_dynamic_neutral99 0x7f0600e0
int color m3_ref_palette_dynamic_neutral_variant0 0x7f0600e1
int color m3_ref_palette_dynamic_neutral_variant10 0x7f0600e2
int color m3_ref_palette_dynamic_neutral_variant100 0x7f0600e3
int color m3_ref_palette_dynamic_neutral_variant12 0x7f0600e4
int color m3_ref_palette_dynamic_neutral_variant17 0x7f0600e5
int color m3_ref_palette_dynamic_neutral_variant20 0x7f0600e6
int color m3_ref_palette_dynamic_neutral_variant22 0x7f0600e7
int color m3_ref_palette_dynamic_neutral_variant24 0x7f0600e8
int color m3_ref_palette_dynamic_neutral_variant30 0x7f0600e9
int color m3_ref_palette_dynamic_neutral_variant4 0x7f0600ea
int color m3_ref_palette_dynamic_neutral_variant40 0x7f0600eb
int color m3_ref_palette_dynamic_neutral_variant50 0x7f0600ec
int color m3_ref_palette_dynamic_neutral_variant6 0x7f0600ed
int color m3_ref_palette_dynamic_neutral_variant60 0x7f0600ee
int color m3_ref_palette_dynamic_neutral_variant70 0x7f0600ef
int color m3_ref_palette_dynamic_neutral_variant80 0x7f0600f0
int color m3_ref_palette_dynamic_neutral_variant87 0x7f0600f1
int color m3_ref_palette_dynamic_neutral_variant90 0x7f0600f2
int color m3_ref_palette_dynamic_neutral_variant92 0x7f0600f3
int color m3_ref_palette_dynamic_neutral_variant94 0x7f0600f4
int color m3_ref_palette_dynamic_neutral_variant95 0x7f0600f5
int color m3_ref_palette_dynamic_neutral_variant96 0x7f0600f6
int color m3_ref_palette_dynamic_neutral_variant98 0x7f0600f7
int color m3_ref_palette_dynamic_neutral_variant99 0x7f0600f8
int color m3_ref_palette_dynamic_primary0 0x7f0600f9
int color m3_ref_palette_dynamic_primary10 0x7f0600fa
int color m3_ref_palette_dynamic_primary100 0x7f0600fb
int color m3_ref_palette_dynamic_primary20 0x7f0600fc
int color m3_ref_palette_dynamic_primary30 0x7f0600fd
int color m3_ref_palette_dynamic_primary40 0x7f0600fe
int color m3_ref_palette_dynamic_primary50 0x7f0600ff
int color m3_ref_palette_dynamic_primary60 0x7f060100
int color m3_ref_palette_dynamic_primary70 0x7f060101
int color m3_ref_palette_dynamic_primary80 0x7f060102
int color m3_ref_palette_dynamic_primary90 0x7f060103
int color m3_ref_palette_dynamic_primary95 0x7f060104
int color m3_ref_palette_dynamic_primary99 0x7f060105
int color m3_ref_palette_dynamic_secondary0 0x7f060106
int color m3_ref_palette_dynamic_secondary10 0x7f060107
int color m3_ref_palette_dynamic_secondary100 0x7f060108
int color m3_ref_palette_dynamic_secondary20 0x7f060109
int color m3_ref_palette_dynamic_secondary30 0x7f06010a
int color m3_ref_palette_dynamic_secondary40 0x7f06010b
int color m3_ref_palette_dynamic_secondary50 0x7f06010c
int color m3_ref_palette_dynamic_secondary60 0x7f06010d
int color m3_ref_palette_dynamic_secondary70 0x7f06010e
int color m3_ref_palette_dynamic_secondary80 0x7f06010f
int color m3_ref_palette_dynamic_secondary90 0x7f060110
int color m3_ref_palette_dynamic_secondary95 0x7f060111
int color m3_ref_palette_dynamic_secondary99 0x7f060112
int color m3_ref_palette_dynamic_tertiary0 0x7f060113
int color m3_ref_palette_dynamic_tertiary10 0x7f060114
int color m3_ref_palette_dynamic_tertiary100 0x7f060115
int color m3_ref_palette_dynamic_tertiary20 0x7f060116
int color m3_ref_palette_dynamic_tertiary30 0x7f060117
int color m3_ref_palette_dynamic_tertiary40 0x7f060118
int color m3_ref_palette_dynamic_tertiary50 0x7f060119
int color m3_ref_palette_dynamic_tertiary60 0x7f06011a
int color m3_ref_palette_dynamic_tertiary70 0x7f06011b
int color m3_ref_palette_dynamic_tertiary80 0x7f06011c
int color m3_ref_palette_dynamic_tertiary90 0x7f06011d
int color m3_ref_palette_dynamic_tertiary95 0x7f06011e
int color m3_ref_palette_dynamic_tertiary99 0x7f06011f
int color m3_ref_palette_error0 0x7f060120
int color m3_ref_palette_error10 0x7f060121
int color m3_ref_palette_error100 0x7f060122
int color m3_ref_palette_error20 0x7f060123
int color m3_ref_palette_error30 0x7f060124
int color m3_ref_palette_error40 0x7f060125
int color m3_ref_palette_error50 0x7f060126
int color m3_ref_palette_error60 0x7f060127
int color m3_ref_palette_error70 0x7f060128
int color m3_ref_palette_error80 0x7f060129
int color m3_ref_palette_error90 0x7f06012a
int color m3_ref_palette_error95 0x7f06012b
int color m3_ref_palette_error99 0x7f06012c
int color m3_ref_palette_neutral0 0x7f06012d
int color m3_ref_palette_neutral10 0x7f06012e
int color m3_ref_palette_neutral100 0x7f06012f
int color m3_ref_palette_neutral12 0x7f060130
int color m3_ref_palette_neutral17 0x7f060131
int color m3_ref_palette_neutral20 0x7f060132
int color m3_ref_palette_neutral22 0x7f060133
int color m3_ref_palette_neutral24 0x7f060134
int color m3_ref_palette_neutral30 0x7f060135
int color m3_ref_palette_neutral4 0x7f060136
int color m3_ref_palette_neutral40 0x7f060137
int color m3_ref_palette_neutral50 0x7f060138
int color m3_ref_palette_neutral6 0x7f060139
int color m3_ref_palette_neutral60 0x7f06013a
int color m3_ref_palette_neutral70 0x7f06013b
int color m3_ref_palette_neutral80 0x7f06013c
int color m3_ref_palette_neutral87 0x7f06013d
int color m3_ref_palette_neutral90 0x7f06013e
int color m3_ref_palette_neutral92 0x7f06013f
int color m3_ref_palette_neutral94 0x7f060140
int color m3_ref_palette_neutral95 0x7f060141
int color m3_ref_palette_neutral96 0x7f060142
int color m3_ref_palette_neutral98 0x7f060143
int color m3_ref_palette_neutral99 0x7f060144
int color m3_ref_palette_neutral_variant0 0x7f060145
int color m3_ref_palette_neutral_variant10 0x7f060146
int color m3_ref_palette_neutral_variant100 0x7f060147
int color m3_ref_palette_neutral_variant20 0x7f060148
int color m3_ref_palette_neutral_variant30 0x7f060149
int color m3_ref_palette_neutral_variant40 0x7f06014a
int color m3_ref_palette_neutral_variant50 0x7f06014b
int color m3_ref_palette_neutral_variant60 0x7f06014c
int color m3_ref_palette_neutral_variant70 0x7f06014d
int color m3_ref_palette_neutral_variant80 0x7f06014e
int color m3_ref_palette_neutral_variant90 0x7f06014f
int color m3_ref_palette_neutral_variant95 0x7f060150
int color m3_ref_palette_neutral_variant99 0x7f060151
int color m3_ref_palette_primary0 0x7f060152
int color m3_ref_palette_primary10 0x7f060153
int color m3_ref_palette_primary100 0x7f060154
int color m3_ref_palette_primary20 0x7f060155
int color m3_ref_palette_primary30 0x7f060156
int color m3_ref_palette_primary40 0x7f060157
int color m3_ref_palette_primary50 0x7f060158
int color m3_ref_palette_primary60 0x7f060159
int color m3_ref_palette_primary70 0x7f06015a
int color m3_ref_palette_primary80 0x7f06015b
int color m3_ref_palette_primary90 0x7f06015c
int color m3_ref_palette_primary95 0x7f06015d
int color m3_ref_palette_primary99 0x7f06015e
int color m3_ref_palette_secondary0 0x7f06015f
int color m3_ref_palette_secondary10 0x7f060160
int color m3_ref_palette_secondary100 0x7f060161
int color m3_ref_palette_secondary20 0x7f060162
int color m3_ref_palette_secondary30 0x7f060163
int color m3_ref_palette_secondary40 0x7f060164
int color m3_ref_palette_secondary50 0x7f060165
int color m3_ref_palette_secondary60 0x7f060166
int color m3_ref_palette_secondary70 0x7f060167
int color m3_ref_palette_secondary80 0x7f060168
int color m3_ref_palette_secondary90 0x7f060169
int color m3_ref_palette_secondary95 0x7f06016a
int color m3_ref_palette_secondary99 0x7f06016b
int color m3_ref_palette_tertiary0 0x7f06016c
int color m3_ref_palette_tertiary10 0x7f06016d
int color m3_ref_palette_tertiary100 0x7f06016e
int color m3_ref_palette_tertiary20 0x7f06016f
int color m3_ref_palette_tertiary30 0x7f060170
int color m3_ref_palette_tertiary40 0x7f060171
int color m3_ref_palette_tertiary50 0x7f060172
int color m3_ref_palette_tertiary60 0x7f060173
int color m3_ref_palette_tertiary70 0x7f060174
int color m3_ref_palette_tertiary80 0x7f060175
int color m3_ref_palette_tertiary90 0x7f060176
int color m3_ref_palette_tertiary95 0x7f060177
int color m3_ref_palette_tertiary99 0x7f060178
int color m3_ref_palette_white 0x7f060179
int color m3_selection_control_ripple_color_selector 0x7f06017a
int color m3_simple_item_ripple_color 0x7f06017b
int color m3_slider_active_track_color 0x7f06017c
int color m3_slider_active_track_color_legacy 0x7f06017d
int color m3_slider_halo_color_legacy 0x7f06017e
int color m3_slider_inactive_track_color 0x7f06017f
int color m3_slider_inactive_track_color_legacy 0x7f060180
int color m3_slider_thumb_color 0x7f060181
int color m3_slider_thumb_color_legacy 0x7f060182
int color m3_switch_thumb_tint 0x7f060183
int color m3_switch_track_tint 0x7f060184
int color m3_sys_color_dark_background 0x7f060185
int color m3_sys_color_dark_error 0x7f060186
int color m3_sys_color_dark_error_container 0x7f060187
int color m3_sys_color_dark_inverse_on_surface 0x7f060188
int color m3_sys_color_dark_inverse_primary 0x7f060189
int color m3_sys_color_dark_inverse_surface 0x7f06018a
int color m3_sys_color_dark_on_background 0x7f06018b
int color m3_sys_color_dark_on_error 0x7f06018c
int color m3_sys_color_dark_on_error_container 0x7f06018d
int color m3_sys_color_dark_on_primary 0x7f06018e
int color m3_sys_color_dark_on_primary_container 0x7f06018f
int color m3_sys_color_dark_on_secondary 0x7f060190
int color m3_sys_color_dark_on_secondary_container 0x7f060191
int color m3_sys_color_dark_on_surface 0x7f060192
int color m3_sys_color_dark_on_surface_variant 0x7f060193
int color m3_sys_color_dark_on_tertiary 0x7f060194
int color m3_sys_color_dark_on_tertiary_container 0x7f060195
int color m3_sys_color_dark_outline 0x7f060196
int color m3_sys_color_dark_outline_variant 0x7f060197
int color m3_sys_color_dark_primary 0x7f060198
int color m3_sys_color_dark_primary_container 0x7f060199
int color m3_sys_color_dark_secondary 0x7f06019a
int color m3_sys_color_dark_secondary_container 0x7f06019b
int color m3_sys_color_dark_surface 0x7f06019c
int color m3_sys_color_dark_surface_bright 0x7f06019d
int color m3_sys_color_dark_surface_container 0x7f06019e
int color m3_sys_color_dark_surface_container_high 0x7f06019f
int color m3_sys_color_dark_surface_container_highest 0x7f0601a0
int color m3_sys_color_dark_surface_container_low 0x7f0601a1
int color m3_sys_color_dark_surface_container_lowest 0x7f0601a2
int color m3_sys_color_dark_surface_dim 0x7f0601a3
int color m3_sys_color_dark_surface_variant 0x7f0601a4
int color m3_sys_color_dark_tertiary 0x7f0601a5
int color m3_sys_color_dark_tertiary_container 0x7f0601a6
int color m3_sys_color_dynamic_dark_background 0x7f0601a7
int color m3_sys_color_dynamic_dark_error 0x7f0601a8
int color m3_sys_color_dynamic_dark_error_container 0x7f0601a9
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x7f0601aa
int color m3_sys_color_dynamic_dark_inverse_primary 0x7f0601ab
int color m3_sys_color_dynamic_dark_inverse_surface 0x7f0601ac
int color m3_sys_color_dynamic_dark_on_background 0x7f0601ad
int color m3_sys_color_dynamic_dark_on_error 0x7f0601ae
int color m3_sys_color_dynamic_dark_on_error_container 0x7f0601af
int color m3_sys_color_dynamic_dark_on_primary 0x7f0601b0
int color m3_sys_color_dynamic_dark_on_primary_container 0x7f0601b1
int color m3_sys_color_dynamic_dark_on_secondary 0x7f0601b2
int color m3_sys_color_dynamic_dark_on_secondary_container 0x7f0601b3
int color m3_sys_color_dynamic_dark_on_surface 0x7f0601b4
int color m3_sys_color_dynamic_dark_on_surface_variant 0x7f0601b5
int color m3_sys_color_dynamic_dark_on_tertiary 0x7f0601b6
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x7f0601b7
int color m3_sys_color_dynamic_dark_outline 0x7f0601b8
int color m3_sys_color_dynamic_dark_outline_variant 0x7f0601b9
int color m3_sys_color_dynamic_dark_primary 0x7f0601ba
int color m3_sys_color_dynamic_dark_primary_container 0x7f0601bb
int color m3_sys_color_dynamic_dark_secondary 0x7f0601bc
int color m3_sys_color_dynamic_dark_secondary_container 0x7f0601bd
int color m3_sys_color_dynamic_dark_surface 0x7f0601be
int color m3_sys_color_dynamic_dark_surface_bright 0x7f0601bf
int color m3_sys_color_dynamic_dark_surface_container 0x7f0601c0
int color m3_sys_color_dynamic_dark_surface_container_high 0x7f0601c1
int color m3_sys_color_dynamic_dark_surface_container_highest 0x7f0601c2
int color m3_sys_color_dynamic_dark_surface_container_low 0x7f0601c3
int color m3_sys_color_dynamic_dark_surface_container_lowest 0x7f0601c4
int color m3_sys_color_dynamic_dark_surface_dim 0x7f0601c5
int color m3_sys_color_dynamic_dark_surface_variant 0x7f0601c6
int color m3_sys_color_dynamic_dark_tertiary 0x7f0601c7
int color m3_sys_color_dynamic_dark_tertiary_container 0x7f0601c8
int color m3_sys_color_dynamic_light_background 0x7f0601c9
int color m3_sys_color_dynamic_light_error 0x7f0601ca
int color m3_sys_color_dynamic_light_error_container 0x7f0601cb
int color m3_sys_color_dynamic_light_inverse_on_surface 0x7f0601cc
int color m3_sys_color_dynamic_light_inverse_primary 0x7f0601cd
int color m3_sys_color_dynamic_light_inverse_surface 0x7f0601ce
int color m3_sys_color_dynamic_light_on_background 0x7f0601cf
int color m3_sys_color_dynamic_light_on_error 0x7f0601d0
int color m3_sys_color_dynamic_light_on_error_container 0x7f0601d1
int color m3_sys_color_dynamic_light_on_primary 0x7f0601d2
int color m3_sys_color_dynamic_light_on_primary_container 0x7f0601d3
int color m3_sys_color_dynamic_light_on_secondary 0x7f0601d4
int color m3_sys_color_dynamic_light_on_secondary_container 0x7f0601d5
int color m3_sys_color_dynamic_light_on_surface 0x7f0601d6
int color m3_sys_color_dynamic_light_on_surface_variant 0x7f0601d7
int color m3_sys_color_dynamic_light_on_tertiary 0x7f0601d8
int color m3_sys_color_dynamic_light_on_tertiary_container 0x7f0601d9
int color m3_sys_color_dynamic_light_outline 0x7f0601da
int color m3_sys_color_dynamic_light_outline_variant 0x7f0601db
int color m3_sys_color_dynamic_light_primary 0x7f0601dc
int color m3_sys_color_dynamic_light_primary_container 0x7f0601dd
int color m3_sys_color_dynamic_light_secondary 0x7f0601de
int color m3_sys_color_dynamic_light_secondary_container 0x7f0601df
int color m3_sys_color_dynamic_light_surface 0x7f0601e0
int color m3_sys_color_dynamic_light_surface_bright 0x7f0601e1
int color m3_sys_color_dynamic_light_surface_container 0x7f0601e2
int color m3_sys_color_dynamic_light_surface_container_high 0x7f0601e3
int color m3_sys_color_dynamic_light_surface_container_highest 0x7f0601e4
int color m3_sys_color_dynamic_light_surface_container_low 0x7f0601e5
int color m3_sys_color_dynamic_light_surface_container_lowest 0x7f0601e6
int color m3_sys_color_dynamic_light_surface_dim 0x7f0601e7
int color m3_sys_color_dynamic_light_surface_variant 0x7f0601e8
int color m3_sys_color_dynamic_light_tertiary 0x7f0601e9
int color m3_sys_color_dynamic_light_tertiary_container 0x7f0601ea
int color m3_sys_color_dynamic_on_primary_fixed 0x7f0601eb
int color m3_sys_color_dynamic_on_primary_fixed_variant 0x7f0601ec
int color m3_sys_color_dynamic_on_secondary_fixed 0x7f0601ed
int color m3_sys_color_dynamic_on_secondary_fixed_variant 0x7f0601ee
int color m3_sys_color_dynamic_on_tertiary_fixed 0x7f0601ef
int color m3_sys_color_dynamic_on_tertiary_fixed_variant 0x7f0601f0
int color m3_sys_color_dynamic_primary_fixed 0x7f0601f1
int color m3_sys_color_dynamic_primary_fixed_dim 0x7f0601f2
int color m3_sys_color_dynamic_secondary_fixed 0x7f0601f3
int color m3_sys_color_dynamic_secondary_fixed_dim 0x7f0601f4
int color m3_sys_color_dynamic_tertiary_fixed 0x7f0601f5
int color m3_sys_color_dynamic_tertiary_fixed_dim 0x7f0601f6
int color m3_sys_color_light_background 0x7f0601f7
int color m3_sys_color_light_error 0x7f0601f8
int color m3_sys_color_light_error_container 0x7f0601f9
int color m3_sys_color_light_inverse_on_surface 0x7f0601fa
int color m3_sys_color_light_inverse_primary 0x7f0601fb
int color m3_sys_color_light_inverse_surface 0x7f0601fc
int color m3_sys_color_light_on_background 0x7f0601fd
int color m3_sys_color_light_on_error 0x7f0601fe
int color m3_sys_color_light_on_error_container 0x7f0601ff
int color m3_sys_color_light_on_primary 0x7f060200
int color m3_sys_color_light_on_primary_container 0x7f060201
int color m3_sys_color_light_on_secondary 0x7f060202
int color m3_sys_color_light_on_secondary_container 0x7f060203
int color m3_sys_color_light_on_surface 0x7f060204
int color m3_sys_color_light_on_surface_variant 0x7f060205
int color m3_sys_color_light_on_tertiary 0x7f060206
int color m3_sys_color_light_on_tertiary_container 0x7f060207
int color m3_sys_color_light_outline 0x7f060208
int color m3_sys_color_light_outline_variant 0x7f060209
int color m3_sys_color_light_primary 0x7f06020a
int color m3_sys_color_light_primary_container 0x7f06020b
int color m3_sys_color_light_secondary 0x7f06020c
int color m3_sys_color_light_secondary_container 0x7f06020d
int color m3_sys_color_light_surface 0x7f06020e
int color m3_sys_color_light_surface_bright 0x7f06020f
int color m3_sys_color_light_surface_container 0x7f060210
int color m3_sys_color_light_surface_container_high 0x7f060211
int color m3_sys_color_light_surface_container_highest 0x7f060212
int color m3_sys_color_light_surface_container_low 0x7f060213
int color m3_sys_color_light_surface_container_lowest 0x7f060214
int color m3_sys_color_light_surface_dim 0x7f060215
int color m3_sys_color_light_surface_variant 0x7f060216
int color m3_sys_color_light_tertiary 0x7f060217
int color m3_sys_color_light_tertiary_container 0x7f060218
int color m3_sys_color_on_primary_fixed 0x7f060219
int color m3_sys_color_on_primary_fixed_variant 0x7f06021a
int color m3_sys_color_on_secondary_fixed 0x7f06021b
int color m3_sys_color_on_secondary_fixed_variant 0x7f06021c
int color m3_sys_color_on_tertiary_fixed 0x7f06021d
int color m3_sys_color_on_tertiary_fixed_variant 0x7f06021e
int color m3_sys_color_primary_fixed 0x7f06021f
int color m3_sys_color_primary_fixed_dim 0x7f060220
int color m3_sys_color_secondary_fixed 0x7f060221
int color m3_sys_color_secondary_fixed_dim 0x7f060222
int color m3_sys_color_tertiary_fixed 0x7f060223
int color m3_sys_color_tertiary_fixed_dim 0x7f060224
int color m3_tabs_icon_color 0x7f060225
int color m3_tabs_icon_color_secondary 0x7f060226
int color m3_tabs_ripple_color 0x7f060227
int color m3_tabs_ripple_color_secondary 0x7f060228
int color m3_tabs_text_color 0x7f060229
int color m3_tabs_text_color_secondary 0x7f06022a
int color m3_text_button_background_color_selector 0x7f06022b
int color m3_text_button_foreground_color_selector 0x7f06022c
int color m3_text_button_ripple_color_selector 0x7f06022d
int color m3_textfield_filled_background_color 0x7f06022e
int color m3_textfield_indicator_text_color 0x7f06022f
int color m3_textfield_input_text_color 0x7f060230
int color m3_textfield_label_color 0x7f060231
int color m3_textfield_stroke_color 0x7f060232
int color m3_timepicker_button_background_color 0x7f060233
int color m3_timepicker_button_ripple_color 0x7f060234
int color m3_timepicker_button_text_color 0x7f060235
int color m3_timepicker_clock_text_color 0x7f060236
int color m3_timepicker_display_background_color 0x7f060237
int color m3_timepicker_display_ripple_color 0x7f060238
int color m3_timepicker_display_text_color 0x7f060239
int color m3_timepicker_secondary_text_button_ripple_color 0x7f06023a
int color m3_timepicker_secondary_text_button_text_color 0x7f06023b
int color m3_timepicker_time_input_stroke_color 0x7f06023c
int color m3_tonal_button_ripple_color_selector 0x7f06023d
int color material_blue_grey_800 0x7f06023e
int color material_blue_grey_900 0x7f06023f
int color material_blue_grey_950 0x7f060240
int color material_cursor_color 0x7f060241
int color material_deep_teal_200 0x7f060242
int color material_deep_teal_500 0x7f060243
int color material_divider_color 0x7f060244
int color material_dynamic_color_dark_error 0x7f060245
int color material_dynamic_color_dark_error_container 0x7f060246
int color material_dynamic_color_dark_on_error 0x7f060247
int color material_dynamic_color_dark_on_error_container 0x7f060248
int color material_dynamic_color_light_error 0x7f060249
int color material_dynamic_color_light_error_container 0x7f06024a
int color material_dynamic_color_light_on_error 0x7f06024b
int color material_dynamic_color_light_on_error_container 0x7f06024c
int color material_dynamic_neutral0 0x7f06024d
int color material_dynamic_neutral10 0x7f06024e
int color material_dynamic_neutral100 0x7f06024f
int color material_dynamic_neutral20 0x7f060250
int color material_dynamic_neutral30 0x7f060251
int color material_dynamic_neutral40 0x7f060252
int color material_dynamic_neutral50 0x7f060253
int color material_dynamic_neutral60 0x7f060254
int color material_dynamic_neutral70 0x7f060255
int color material_dynamic_neutral80 0x7f060256
int color material_dynamic_neutral90 0x7f060257
int color material_dynamic_neutral95 0x7f060258
int color material_dynamic_neutral99 0x7f060259
int color material_dynamic_neutral_variant0 0x7f06025a
int color material_dynamic_neutral_variant10 0x7f06025b
int color material_dynamic_neutral_variant100 0x7f06025c
int color material_dynamic_neutral_variant20 0x7f06025d
int color material_dynamic_neutral_variant30 0x7f06025e
int color material_dynamic_neutral_variant40 0x7f06025f
int color material_dynamic_neutral_variant50 0x7f060260
int color material_dynamic_neutral_variant60 0x7f060261
int color material_dynamic_neutral_variant70 0x7f060262
int color material_dynamic_neutral_variant80 0x7f060263
int color material_dynamic_neutral_variant90 0x7f060264
int color material_dynamic_neutral_variant95 0x7f060265
int color material_dynamic_neutral_variant99 0x7f060266
int color material_dynamic_primary0 0x7f060267
int color material_dynamic_primary10 0x7f060268
int color material_dynamic_primary100 0x7f060269
int color material_dynamic_primary20 0x7f06026a
int color material_dynamic_primary30 0x7f06026b
int color material_dynamic_primary40 0x7f06026c
int color material_dynamic_primary50 0x7f06026d
int color material_dynamic_primary60 0x7f06026e
int color material_dynamic_primary70 0x7f06026f
int color material_dynamic_primary80 0x7f060270
int color material_dynamic_primary90 0x7f060271
int color material_dynamic_primary95 0x7f060272
int color material_dynamic_primary99 0x7f060273
int color material_dynamic_secondary0 0x7f060274
int color material_dynamic_secondary10 0x7f060275
int color material_dynamic_secondary100 0x7f060276
int color material_dynamic_secondary20 0x7f060277
int color material_dynamic_secondary30 0x7f060278
int color material_dynamic_secondary40 0x7f060279
int color material_dynamic_secondary50 0x7f06027a
int color material_dynamic_secondary60 0x7f06027b
int color material_dynamic_secondary70 0x7f06027c
int color material_dynamic_secondary80 0x7f06027d
int color material_dynamic_secondary90 0x7f06027e
int color material_dynamic_secondary95 0x7f06027f
int color material_dynamic_secondary99 0x7f060280
int color material_dynamic_tertiary0 0x7f060281
int color material_dynamic_tertiary10 0x7f060282
int color material_dynamic_tertiary100 0x7f060283
int color material_dynamic_tertiary20 0x7f060284
int color material_dynamic_tertiary30 0x7f060285
int color material_dynamic_tertiary40 0x7f060286
int color material_dynamic_tertiary50 0x7f060287
int color material_dynamic_tertiary60 0x7f060288
int color material_dynamic_tertiary70 0x7f060289
int color material_dynamic_tertiary80 0x7f06028a
int color material_dynamic_tertiary90 0x7f06028b
int color material_dynamic_tertiary95 0x7f06028c
int color material_dynamic_tertiary99 0x7f06028d
int color material_grey_100 0x7f06028e
int color material_grey_300 0x7f06028f
int color material_grey_50 0x7f060290
int color material_grey_600 0x7f060291
int color material_grey_800 0x7f060292
int color material_grey_850 0x7f060293
int color material_grey_900 0x7f060294
int color material_harmonized_color_error 0x7f060295
int color material_harmonized_color_error_container 0x7f060296
int color material_harmonized_color_on_error 0x7f060297
int color material_harmonized_color_on_error_container 0x7f060298
int color material_on_background_disabled 0x7f060299
int color material_on_background_emphasis_high_type 0x7f06029a
int color material_on_background_emphasis_medium 0x7f06029b
int color material_on_primary_disabled 0x7f06029c
int color material_on_primary_emphasis_high_type 0x7f06029d
int color material_on_primary_emphasis_medium 0x7f06029e
int color material_on_surface_disabled 0x7f06029f
int color material_on_surface_emphasis_high_type 0x7f0602a0
int color material_on_surface_emphasis_medium 0x7f0602a1
int color material_on_surface_stroke 0x7f0602a2
int color material_personalized__highlighted_text 0x7f0602a3
int color material_personalized__highlighted_text_inverse 0x7f0602a4
int color material_personalized_color_background 0x7f0602a5
int color material_personalized_color_control_activated 0x7f0602a6
int color material_personalized_color_control_highlight 0x7f0602a7
int color material_personalized_color_control_normal 0x7f0602a8
int color material_personalized_color_error 0x7f0602a9
int color material_personalized_color_error_container 0x7f0602aa
int color material_personalized_color_on_background 0x7f0602ab
int color material_personalized_color_on_error 0x7f0602ac
int color material_personalized_color_on_error_container 0x7f0602ad
int color material_personalized_color_on_primary 0x7f0602ae
int color material_personalized_color_on_primary_container 0x7f0602af
int color material_personalized_color_on_secondary 0x7f0602b0
int color material_personalized_color_on_secondary_container 0x7f0602b1
int color material_personalized_color_on_surface 0x7f0602b2
int color material_personalized_color_on_surface_inverse 0x7f0602b3
int color material_personalized_color_on_surface_variant 0x7f0602b4
int color material_personalized_color_on_tertiary 0x7f0602b5
int color material_personalized_color_on_tertiary_container 0x7f0602b6
int color material_personalized_color_outline 0x7f0602b7
int color material_personalized_color_outline_variant 0x7f0602b8
int color material_personalized_color_primary 0x7f0602b9
int color material_personalized_color_primary_container 0x7f0602ba
int color material_personalized_color_primary_inverse 0x7f0602bb
int color material_personalized_color_primary_text 0x7f0602bc
int color material_personalized_color_primary_text_inverse 0x7f0602bd
int color material_personalized_color_secondary 0x7f0602be
int color material_personalized_color_secondary_container 0x7f0602bf
int color material_personalized_color_secondary_text 0x7f0602c0
int color material_personalized_color_secondary_text_inverse 0x7f0602c1
int color material_personalized_color_surface 0x7f0602c2
int color material_personalized_color_surface_bright 0x7f0602c3
int color material_personalized_color_surface_container 0x7f0602c4
int color material_personalized_color_surface_container_high 0x7f0602c5
int color material_personalized_color_surface_container_highest 0x7f0602c6
int color material_personalized_color_surface_container_low 0x7f0602c7
int color material_personalized_color_surface_container_lowest 0x7f0602c8
int color material_personalized_color_surface_dim 0x7f0602c9
int color material_personalized_color_surface_inverse 0x7f0602ca
int color material_personalized_color_surface_variant 0x7f0602cb
int color material_personalized_color_tertiary 0x7f0602cc
int color material_personalized_color_tertiary_container 0x7f0602cd
int color material_personalized_color_text_hint_foreground_inverse 0x7f0602ce
int color material_personalized_color_text_primary_inverse 0x7f0602cf
int color material_personalized_color_text_primary_inverse_disable_only 0x7f0602d0
int color material_personalized_color_text_secondary_and_tertiary_inverse 0x7f0602d1
int color material_personalized_color_text_secondary_and_tertiary_inverse_disabled 0x7f0602d2
int color material_personalized_hint_foreground 0x7f0602d3
int color material_personalized_hint_foreground_inverse 0x7f0602d4
int color material_personalized_primary_inverse_text_disable_only 0x7f0602d5
int color material_personalized_primary_text_disable_only 0x7f0602d6
int color material_slider_active_tick_marks_color 0x7f0602d7
int color material_slider_active_track_color 0x7f0602d8
int color material_slider_halo_color 0x7f0602d9
int color material_slider_inactive_tick_marks_color 0x7f0602da
int color material_slider_inactive_track_color 0x7f0602db
int color material_slider_thumb_color 0x7f0602dc
int color material_timepicker_button_background 0x7f0602dd
int color material_timepicker_button_stroke 0x7f0602de
int color material_timepicker_clock_text_color 0x7f0602df
int color material_timepicker_clockface 0x7f0602e0
int color material_timepicker_modebutton_tint 0x7f0602e1
int color modern_accent 0x7f0602e2
int color modern_background 0x7f0602e3
int color modern_card_bg 0x7f0602e4
int color modern_primary 0x7f0602e5
int color modern_primary_dark 0x7f0602e6
int color modern_surface 0x7f0602e7
int color mtrl_btn_bg_color_selector 0x7f0602e8
int color mtrl_btn_ripple_color 0x7f0602e9
int color mtrl_btn_stroke_color_selector 0x7f0602ea
int color mtrl_btn_text_btn_bg_color_selector 0x7f0602eb
int color mtrl_btn_text_btn_ripple_color 0x7f0602ec
int color mtrl_btn_text_color_disabled 0x7f0602ed
int color mtrl_btn_text_color_selector 0x7f0602ee
int color mtrl_btn_transparent_bg_color 0x7f0602ef
int color mtrl_calendar_item_stroke_color 0x7f0602f0
int color mtrl_calendar_selected_range 0x7f0602f1
int color mtrl_card_view_foreground 0x7f0602f2
int color mtrl_card_view_ripple 0x7f0602f3
int color mtrl_chip_background_color 0x7f0602f4
int color mtrl_chip_close_icon_tint 0x7f0602f5
int color mtrl_chip_surface_color 0x7f0602f6
int color mtrl_chip_text_color 0x7f0602f7
int color mtrl_choice_chip_background_color 0x7f0602f8
int color mtrl_choice_chip_ripple_color 0x7f0602f9
int color mtrl_choice_chip_text_color 0x7f0602fa
int color mtrl_error 0x7f0602fb
int color mtrl_fab_bg_color_selector 0x7f0602fc
int color mtrl_fab_icon_text_color_selector 0x7f0602fd
int color mtrl_fab_ripple_color 0x7f0602fe
int color mtrl_filled_background_color 0x7f0602ff
int color mtrl_filled_icon_tint 0x7f060300
int color mtrl_filled_stroke_color 0x7f060301
int color mtrl_indicator_text_color 0x7f060302
int color mtrl_navigation_bar_colored_item_tint 0x7f060303
int color mtrl_navigation_bar_colored_ripple_color 0x7f060304
int color mtrl_navigation_bar_item_tint 0x7f060305
int color mtrl_navigation_bar_ripple_color 0x7f060306
int color mtrl_navigation_item_background_color 0x7f060307
int color mtrl_navigation_item_icon_tint 0x7f060308
int color mtrl_navigation_item_text_color 0x7f060309
int color mtrl_on_primary_text_btn_text_color_selector 0x7f06030a
int color mtrl_on_surface_ripple_color 0x7f06030b
int color mtrl_outlined_icon_tint 0x7f06030c
int color mtrl_outlined_stroke_color 0x7f06030d
int color mtrl_popupmenu_overlay_color 0x7f06030e
int color mtrl_scrim_color 0x7f06030f
int color mtrl_switch_thumb_icon_tint 0x7f060310
int color mtrl_switch_thumb_tint 0x7f060311
int color mtrl_switch_track_decoration_tint 0x7f060312
int color mtrl_switch_track_tint 0x7f060313
int color mtrl_tabs_colored_ripple_color 0x7f060314
int color mtrl_tabs_icon_color_selector 0x7f060315
int color mtrl_tabs_icon_color_selector_colored 0x7f060316
int color mtrl_tabs_legacy_text_color_selector 0x7f060317
int color mtrl_tabs_ripple_color 0x7f060318
int color mtrl_text_btn_text_color_selector 0x7f060319
int color mtrl_textinput_default_box_stroke_color 0x7f06031a
int color mtrl_textinput_disabled_color 0x7f06031b
int color mtrl_textinput_filled_box_default_background_color 0x7f06031c
int color mtrl_textinput_focused_box_stroke_color 0x7f06031d
int color mtrl_textinput_hovered_box_stroke_color 0x7f06031e
int color nature_color 0x7f06031f
int color notification_action_color_filter 0x7f060320
int color notification_icon_bg_color 0x7f060321
int color notification_material_background_media_default_color 0x7f060322
int color preference_fallback_accent_color 0x7f060323
int color primary_dark_material_dark 0x7f060324
int color primary_dark_material_light 0x7f060325
int color primary_material_dark 0x7f060326
int color primary_material_light 0x7f060327
int color primary_text 0x7f060328
int color primary_text_default_material_dark 0x7f060329
int color primary_text_default_material_light 0x7f06032a
int color primary_text_disabled_material_dark 0x7f06032b
int color primary_text_disabled_material_light 0x7f06032c
int color red 0x7f06032d
int color ripple_material_dark 0x7f06032e
int color ripple_material_light 0x7f06032f
int color secondary_text 0x7f060330
int color secondary_text_default_material_dark 0x7f060331
int color secondary_text_default_material_light 0x7f060332
int color secondary_text_disabled_material_dark 0x7f060333
int color secondary_text_disabled_material_light 0x7f060334
int color shadow_dark 0x7f060335
int color shadow_light 0x7f060336
int color shadow_medium 0x7f060337
int color strokeColor 0x7f060338
int color switch_thumb_disabled_material_dark 0x7f060339
int color switch_thumb_disabled_material_light 0x7f06033a
int color switch_thumb_material_dark 0x7f06033b
int color switch_thumb_material_light 0x7f06033c
int color switch_thumb_normal_material_dark 0x7f06033d
int color switch_thumb_normal_material_light 0x7f06033e
int color textPrimary 0x7f06033f
int color textSecondary 0x7f060340
int color text_on_primary 0x7f060341
int color text_primary 0x7f060342
int color text_secondary 0x7f060343
int color tooltip_background_dark 0x7f060344
int color tooltip_background_light 0x7f060345
int color transport_color 0x7f060346
int color white 0x7f060347
int color yellow 0x7f060348
int dimen abc_action_bar_content_inset_material 0x7f070000
int dimen abc_action_bar_content_inset_with_nav 0x7f070001
int dimen abc_action_bar_default_height_material 0x7f070002
int dimen abc_action_bar_default_padding_end_material 0x7f070003
int dimen abc_action_bar_default_padding_start_material 0x7f070004
int dimen abc_action_bar_elevation_material 0x7f070005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070006
int dimen abc_action_bar_overflow_padding_end_material 0x7f070007
int dimen abc_action_bar_overflow_padding_start_material 0x7f070008
int dimen abc_action_bar_stacked_max_height 0x7f070009
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000c
int dimen abc_action_button_min_height_material 0x7f07000d
int dimen abc_action_button_min_width_material 0x7f07000e
int dimen abc_action_button_min_width_overflow_material 0x7f07000f
int dimen abc_alert_dialog_button_bar_height 0x7f070010
int dimen abc_alert_dialog_button_dimen 0x7f070011
int dimen abc_button_inset_horizontal_material 0x7f070012
int dimen abc_button_inset_vertical_material 0x7f070013
int dimen abc_button_padding_horizontal_material 0x7f070014
int dimen abc_button_padding_vertical_material 0x7f070015
int dimen abc_cascading_menus_min_smallest_width 0x7f070016
int dimen abc_config_prefDialogWidth 0x7f070017
int dimen abc_control_corner_material 0x7f070018
int dimen abc_control_inset_material 0x7f070019
int dimen abc_control_padding_material 0x7f07001a
int dimen abc_dialog_corner_radius_material 0x7f07001b
int dimen abc_dialog_fixed_height_major 0x7f07001c
int dimen abc_dialog_fixed_height_minor 0x7f07001d
int dimen abc_dialog_fixed_width_major 0x7f07001e
int dimen abc_dialog_fixed_width_minor 0x7f07001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070020
int dimen abc_dialog_list_padding_top_no_title 0x7f070021
int dimen abc_dialog_min_width_major 0x7f070022
int dimen abc_dialog_min_width_minor 0x7f070023
int dimen abc_dialog_padding_material 0x7f070024
int dimen abc_dialog_padding_top_material 0x7f070025
int dimen abc_dialog_title_divider_material 0x7f070026
int dimen abc_disabled_alpha_material_dark 0x7f070027
int dimen abc_disabled_alpha_material_light 0x7f070028
int dimen abc_dropdownitem_icon_width 0x7f070029
int dimen abc_dropdownitem_text_padding_left 0x7f07002a
int dimen abc_dropdownitem_text_padding_right 0x7f07002b
int dimen abc_edit_text_inset_bottom_material 0x7f07002c
int dimen abc_edit_text_inset_horizontal_material 0x7f07002d
int dimen abc_edit_text_inset_top_material 0x7f07002e
int dimen abc_floating_window_z 0x7f07002f
int dimen abc_list_item_height_large_material 0x7f070030
int dimen abc_list_item_height_material 0x7f070031
int dimen abc_list_item_height_small_material 0x7f070032
int dimen abc_list_item_padding_horizontal_material 0x7f070033
int dimen abc_panel_menu_list_width 0x7f070034
int dimen abc_progress_bar_height_material 0x7f070035
int dimen abc_search_view_preferred_height 0x7f070036
int dimen abc_search_view_preferred_width 0x7f070037
int dimen abc_seekbar_track_background_height_material 0x7f070038
int dimen abc_seekbar_track_progress_height_material 0x7f070039
int dimen abc_select_dialog_padding_start_material 0x7f07003a
int dimen abc_star_big 0x7f07003b
int dimen abc_star_medium 0x7f07003c
int dimen abc_star_small 0x7f07003d
int dimen abc_switch_padding 0x7f07003e
int dimen abc_text_size_body_1_material 0x7f07003f
int dimen abc_text_size_body_2_material 0x7f070040
int dimen abc_text_size_button_material 0x7f070041
int dimen abc_text_size_caption_material 0x7f070042
int dimen abc_text_size_display_1_material 0x7f070043
int dimen abc_text_size_display_2_material 0x7f070044
int dimen abc_text_size_display_3_material 0x7f070045
int dimen abc_text_size_display_4_material 0x7f070046
int dimen abc_text_size_headline_material 0x7f070047
int dimen abc_text_size_large_material 0x7f070048
int dimen abc_text_size_medium_material 0x7f070049
int dimen abc_text_size_menu_header_material 0x7f07004a
int dimen abc_text_size_menu_material 0x7f07004b
int dimen abc_text_size_small_material 0x7f07004c
int dimen abc_text_size_subhead_material 0x7f07004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004e
int dimen abc_text_size_title_material 0x7f07004f
int dimen abc_text_size_title_material_toolbar 0x7f070050
int dimen activity_horizontal_margin 0x7f070051
int dimen activity_vertical_margin 0x7f070052
int dimen appcompat_dialog_background_inset 0x7f070053
int dimen browser_actions_context_menu_max_width 0x7f070054
int dimen browser_actions_context_menu_min_padding 0x7f070055
int dimen cardview_compat_inset_shadow 0x7f070056
int dimen cardview_default_elevation 0x7f070057
int dimen cardview_default_radius 0x7f070058
int dimen clock_face_margin_start 0x7f070059
int dimen compat_button_inset_horizontal_material 0x7f07005a
int dimen compat_button_inset_vertical_material 0x7f07005b
int dimen compat_button_padding_horizontal_material 0x7f07005c
int dimen compat_button_padding_vertical_material 0x7f07005d
int dimen compat_control_corner_material 0x7f07005e
int dimen compat_notification_large_icon_max_height 0x7f07005f
int dimen compat_notification_large_icon_max_width 0x7f070060
int dimen def_drawer_elevation 0x7f070061
int dimen default_margin_top 0x7f070062
int dimen default_padding_side 0x7f070063
int dimen default_preview_height 0x7f070064
int dimen default_preview_image_height 0x7f070065
int dimen default_slider_bar_height 0x7f070066
int dimen default_slider_handler_radius 0x7f070067
int dimen default_slider_height 0x7f070068
int dimen default_slider_margin 0x7f070069
int dimen design_appbar_elevation 0x7f07006a
int dimen design_bottom_navigation_active_item_max_width 0x7f07006b
int dimen design_bottom_navigation_active_item_min_width 0x7f07006c
int dimen design_bottom_navigation_active_text_size 0x7f07006d
int dimen design_bottom_navigation_elevation 0x7f07006e
int dimen design_bottom_navigation_height 0x7f07006f
int dimen design_bottom_navigation_icon_size 0x7f070070
int dimen design_bottom_navigation_item_max_width 0x7f070071
int dimen design_bottom_navigation_item_min_width 0x7f070072
int dimen design_bottom_navigation_label_padding 0x7f070073
int dimen design_bottom_navigation_margin 0x7f070074
int dimen design_bottom_navigation_shadow_height 0x7f070075
int dimen design_bottom_navigation_text_size 0x7f070076
int dimen design_bottom_sheet_elevation 0x7f070077
int dimen design_bottom_sheet_modal_elevation 0x7f070078
int dimen design_bottom_sheet_peek_height_min 0x7f070079
int dimen design_fab_border_width 0x7f07007a
int dimen design_fab_elevation 0x7f07007b
int dimen design_fab_image_size 0x7f07007c
int dimen design_fab_size_mini 0x7f07007d
int dimen design_fab_size_normal 0x7f07007e
int dimen design_fab_translation_z_hovered_focused 0x7f07007f
int dimen design_fab_translation_z_pressed 0x7f070080
int dimen design_navigation_elevation 0x7f070081
int dimen design_navigation_icon_padding 0x7f070082
int dimen design_navigation_icon_size 0x7f070083
int dimen design_navigation_item_horizontal_padding 0x7f070084
int dimen design_navigation_item_icon_padding 0x7f070085
int dimen design_navigation_item_vertical_padding 0x7f070086
int dimen design_navigation_max_width 0x7f070087
int dimen design_navigation_padding_bottom 0x7f070088
int dimen design_navigation_separator_vertical_padding 0x7f070089
int dimen design_snackbar_action_inline_max_width 0x7f07008a
int dimen design_snackbar_action_text_color_alpha 0x7f07008b
int dimen design_snackbar_background_corner_radius 0x7f07008c
int dimen design_snackbar_elevation 0x7f07008d
int dimen design_snackbar_extra_spacing_horizontal 0x7f07008e
int dimen design_snackbar_max_width 0x7f07008f
int dimen design_snackbar_min_width 0x7f070090
int dimen design_snackbar_padding_horizontal 0x7f070091
int dimen design_snackbar_padding_vertical 0x7f070092
int dimen design_snackbar_padding_vertical_2lines 0x7f070093
int dimen design_snackbar_text_size 0x7f070094
int dimen design_tab_max_width 0x7f070095
int dimen design_tab_scrollable_min_width 0x7f070096
int dimen design_tab_text_size 0x7f070097
int dimen design_tab_text_size_2line 0x7f070098
int dimen design_textinput_caption_translate_y 0x7f070099
int dimen disabled_alpha_material_dark 0x7f07009a
int dimen disabled_alpha_material_light 0x7f07009b
int dimen fastscroll_default_thickness 0x7f07009c
int dimen fastscroll_margin 0x7f07009d
int dimen fastscroll_minimum_range 0x7f07009e
int dimen fourthSampleStrokeWidth 0x7f07009f
int dimen fourthSampleViewSize 0x7f0700a0
int dimen highlight_alpha_material_colored 0x7f0700a1
int dimen highlight_alpha_material_dark 0x7f0700a2
int dimen highlight_alpha_material_light 0x7f0700a3
int dimen hint_alpha_material_dark 0x7f0700a4
int dimen hint_alpha_material_light 0x7f0700a5
int dimen hint_pressed_alpha_material_dark 0x7f0700a6
int dimen hint_pressed_alpha_material_light 0x7f0700a7
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f0700a8
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f0700a9
int dimen item_touch_helper_swipe_escape_velocity 0x7f0700aa
int dimen m3_alert_dialog_action_bottom_padding 0x7f0700ab
int dimen m3_alert_dialog_action_top_padding 0x7f0700ac
int dimen m3_alert_dialog_corner_size 0x7f0700ad
int dimen m3_alert_dialog_elevation 0x7f0700ae
int dimen m3_alert_dialog_icon_margin 0x7f0700af
int dimen m3_alert_dialog_icon_size 0x7f0700b0
int dimen m3_alert_dialog_title_bottom_margin 0x7f0700b1
int dimen m3_appbar_expanded_title_margin_bottom 0x7f0700b2
int dimen m3_appbar_expanded_title_margin_horizontal 0x7f0700b3
int dimen m3_appbar_scrim_height_trigger 0x7f0700b4
int dimen m3_appbar_scrim_height_trigger_large 0x7f0700b5
int dimen m3_appbar_scrim_height_trigger_medium 0x7f0700b6
int dimen m3_appbar_size_compact 0x7f0700b7
int dimen m3_appbar_size_large 0x7f0700b8
int dimen m3_appbar_size_medium 0x7f0700b9
int dimen m3_back_progress_bottom_container_max_scale_x_distance 0x7f0700ba
int dimen m3_back_progress_bottom_container_max_scale_y_distance 0x7f0700bb
int dimen m3_back_progress_main_container_max_translation_y 0x7f0700bc
int dimen m3_back_progress_main_container_min_edge_gap 0x7f0700bd
int dimen m3_back_progress_side_container_max_scale_x_distance_grow 0x7f0700be
int dimen m3_back_progress_side_container_max_scale_x_distance_shrink 0x7f0700bf
int dimen m3_back_progress_side_container_max_scale_y_distance 0x7f0700c0
int dimen m3_badge_horizontal_offset 0x7f0700c1
int dimen m3_badge_offset 0x7f0700c2
int dimen m3_badge_size 0x7f0700c3
int dimen m3_badge_vertical_offset 0x7f0700c4
int dimen m3_badge_with_text_horizontal_offset 0x7f0700c5
int dimen m3_badge_with_text_offset 0x7f0700c6
int dimen m3_badge_with_text_size 0x7f0700c7
int dimen m3_badge_with_text_vertical_offset 0x7f0700c8
int dimen m3_badge_with_text_vertical_padding 0x7f0700c9
int dimen m3_bottom_nav_item_active_indicator_height 0x7f0700ca
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x7f0700cb
int dimen m3_bottom_nav_item_active_indicator_width 0x7f0700cc
int dimen m3_bottom_nav_item_padding_bottom 0x7f0700cd
int dimen m3_bottom_nav_item_padding_top 0x7f0700ce
int dimen m3_bottom_nav_min_height 0x7f0700cf
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x7f0700d0
int dimen m3_bottom_sheet_elevation 0x7f0700d1
int dimen m3_bottom_sheet_modal_elevation 0x7f0700d2
int dimen m3_bottomappbar_fab_cradle_margin 0x7f0700d3
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0700d4
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x7f0700d5
int dimen m3_bottomappbar_fab_end_margin 0x7f0700d6
int dimen m3_bottomappbar_height 0x7f0700d7
int dimen m3_bottomappbar_horizontal_padding 0x7f0700d8
int dimen m3_btn_dialog_btn_min_width 0x7f0700d9
int dimen m3_btn_dialog_btn_spacing 0x7f0700da
int dimen m3_btn_disabled_elevation 0x7f0700db
int dimen m3_btn_disabled_translation_z 0x7f0700dc
int dimen m3_btn_elevated_btn_elevation 0x7f0700dd
int dimen m3_btn_elevation 0x7f0700de
int dimen m3_btn_icon_btn_padding_left 0x7f0700df
int dimen m3_btn_icon_btn_padding_right 0x7f0700e0
int dimen m3_btn_icon_only_default_padding 0x7f0700e1
int dimen m3_btn_icon_only_default_size 0x7f0700e2
int dimen m3_btn_icon_only_icon_padding 0x7f0700e3
int dimen m3_btn_icon_only_min_width 0x7f0700e4
int dimen m3_btn_inset 0x7f0700e5
int dimen m3_btn_max_width 0x7f0700e6
int dimen m3_btn_padding_bottom 0x7f0700e7
int dimen m3_btn_padding_left 0x7f0700e8
int dimen m3_btn_padding_right 0x7f0700e9
int dimen m3_btn_padding_top 0x7f0700ea
int dimen m3_btn_stroke_size 0x7f0700eb
int dimen m3_btn_text_btn_icon_padding_left 0x7f0700ec
int dimen m3_btn_text_btn_icon_padding_right 0x7f0700ed
int dimen m3_btn_text_btn_padding_left 0x7f0700ee
int dimen m3_btn_text_btn_padding_right 0x7f0700ef
int dimen m3_btn_translation_z_base 0x7f0700f0
int dimen m3_btn_translation_z_hovered 0x7f0700f1
int dimen m3_card_disabled_z 0x7f0700f2
int dimen m3_card_dragged_z 0x7f0700f3
int dimen m3_card_elevated_disabled_z 0x7f0700f4
int dimen m3_card_elevated_dragged_z 0x7f0700f5
int dimen m3_card_elevated_elevation 0x7f0700f6
int dimen m3_card_elevated_hovered_z 0x7f0700f7
int dimen m3_card_elevation 0x7f0700f8
int dimen m3_card_hovered_z 0x7f0700f9
int dimen m3_card_stroke_width 0x7f0700fa
int dimen m3_carousel_debug_keyline_width 0x7f0700fb
int dimen m3_carousel_extra_small_item_size 0x7f0700fc
int dimen m3_carousel_gone_size 0x7f0700fd
int dimen m3_carousel_small_item_default_corner_size 0x7f0700fe
int dimen m3_carousel_small_item_size_max 0x7f0700ff
int dimen m3_carousel_small_item_size_min 0x7f070100
int dimen m3_chip_checked_hovered_translation_z 0x7f070101
int dimen m3_chip_corner_size 0x7f070102
int dimen m3_chip_disabled_translation_z 0x7f070103
int dimen m3_chip_dragged_translation_z 0x7f070104
int dimen m3_chip_elevated_elevation 0x7f070105
int dimen m3_chip_hovered_translation_z 0x7f070106
int dimen m3_chip_icon_size 0x7f070107
int dimen m3_comp_assist_chip_container_height 0x7f070108
int dimen m3_comp_assist_chip_elevated_container_elevation 0x7f070109
int dimen m3_comp_assist_chip_flat_container_elevation 0x7f07010a
int dimen m3_comp_assist_chip_flat_outline_width 0x7f07010b
int dimen m3_comp_assist_chip_with_icon_icon_size 0x7f07010c
int dimen m3_comp_badge_large_size 0x7f07010d
int dimen m3_comp_badge_size 0x7f07010e
int dimen m3_comp_bottom_app_bar_container_elevation 0x7f07010f
int dimen m3_comp_bottom_app_bar_container_height 0x7f070110
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x7f070111
int dimen m3_comp_date_picker_modal_date_today_container_outline_width 0x7f070112
int dimen m3_comp_date_picker_modal_header_container_height 0x7f070113
int dimen m3_comp_date_picker_modal_range_selection_header_container_height 0x7f070114
int dimen m3_comp_divider_thickness 0x7f070115
int dimen m3_comp_elevated_button_container_elevation 0x7f070116
int dimen m3_comp_elevated_button_disabled_container_elevation 0x7f070117
int dimen m3_comp_elevated_card_container_elevation 0x7f070118
int dimen m3_comp_elevated_card_icon_size 0x7f070119
int dimen m3_comp_extended_fab_primary_container_elevation 0x7f07011a
int dimen m3_comp_extended_fab_primary_container_height 0x7f07011b
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x7f07011c
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x7f07011d
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x7f07011e
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x7f07011f
int dimen m3_comp_extended_fab_primary_icon_size 0x7f070120
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x7f070121
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x7f070122
int dimen m3_comp_fab_primary_container_elevation 0x7f070123
int dimen m3_comp_fab_primary_container_height 0x7f070124
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x7f070125
int dimen m3_comp_fab_primary_hover_container_elevation 0x7f070126
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x7f070127
int dimen m3_comp_fab_primary_icon_size 0x7f070128
int dimen m3_comp_fab_primary_large_container_height 0x7f070129
int dimen m3_comp_fab_primary_large_icon_size 0x7f07012a
int dimen m3_comp_fab_primary_pressed_container_elevation 0x7f07012b
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x7f07012c
int dimen m3_comp_fab_primary_small_container_height 0x7f07012d
int dimen m3_comp_fab_primary_small_icon_size 0x7f07012e
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x7f07012f
int dimen m3_comp_filled_button_container_elevation 0x7f070130
int dimen m3_comp_filled_button_with_icon_icon_size 0x7f070131
int dimen m3_comp_filled_card_container_elevation 0x7f070132
int dimen m3_comp_filled_card_dragged_state_layer_opacity 0x7f070133
int dimen m3_comp_filled_card_focus_state_layer_opacity 0x7f070134
int dimen m3_comp_filled_card_hover_state_layer_opacity 0x7f070135
int dimen m3_comp_filled_card_icon_size 0x7f070136
int dimen m3_comp_filled_card_pressed_state_layer_opacity 0x7f070137
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x7f070138
int dimen m3_comp_filter_chip_container_height 0x7f070139
int dimen m3_comp_filter_chip_elevated_container_elevation 0x7f07013a
int dimen m3_comp_filter_chip_flat_container_elevation 0x7f07013b
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x7f07013c
int dimen m3_comp_filter_chip_with_icon_icon_size 0x7f07013d
int dimen m3_comp_input_chip_container_elevation 0x7f07013e
int dimen m3_comp_input_chip_container_height 0x7f07013f
int dimen m3_comp_input_chip_unselected_outline_width 0x7f070140
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x7f070141
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x7f070142
int dimen m3_comp_menu_container_elevation 0x7f070143
int dimen m3_comp_navigation_bar_active_indicator_height 0x7f070144
int dimen m3_comp_navigation_bar_active_indicator_width 0x7f070145
int dimen m3_comp_navigation_bar_container_elevation 0x7f070146
int dimen m3_comp_navigation_bar_container_height 0x7f070147
int dimen m3_comp_navigation_bar_focus_state_layer_opacity 0x7f070148
int dimen m3_comp_navigation_bar_hover_state_layer_opacity 0x7f070149
int dimen m3_comp_navigation_bar_icon_size 0x7f07014a
int dimen m3_comp_navigation_bar_pressed_state_layer_opacity 0x7f07014b
int dimen m3_comp_navigation_drawer_container_width 0x7f07014c
int dimen m3_comp_navigation_drawer_focus_state_layer_opacity 0x7f07014d
int dimen m3_comp_navigation_drawer_hover_state_layer_opacity 0x7f07014e
int dimen m3_comp_navigation_drawer_icon_size 0x7f07014f
int dimen m3_comp_navigation_drawer_modal_container_elevation 0x7f070150
int dimen m3_comp_navigation_drawer_pressed_state_layer_opacity 0x7f070151
int dimen m3_comp_navigation_drawer_standard_container_elevation 0x7f070152
int dimen m3_comp_navigation_rail_active_indicator_height 0x7f070153
int dimen m3_comp_navigation_rail_active_indicator_width 0x7f070154
int dimen m3_comp_navigation_rail_container_elevation 0x7f070155
int dimen m3_comp_navigation_rail_container_width 0x7f070156
int dimen m3_comp_navigation_rail_focus_state_layer_opacity 0x7f070157
int dimen m3_comp_navigation_rail_hover_state_layer_opacity 0x7f070158
int dimen m3_comp_navigation_rail_icon_size 0x7f070159
int dimen m3_comp_navigation_rail_pressed_state_layer_opacity 0x7f07015a
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x7f07015b
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x7f07015c
int dimen m3_comp_outlined_button_outline_width 0x7f07015d
int dimen m3_comp_outlined_card_container_elevation 0x7f07015e
int dimen m3_comp_outlined_card_disabled_outline_opacity 0x7f07015f
int dimen m3_comp_outlined_card_icon_size 0x7f070160
int dimen m3_comp_outlined_card_outline_width 0x7f070161
int dimen m3_comp_outlined_icon_button_unselected_outline_width 0x7f070162
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x7f070163
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x7f070164
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x7f070165
int dimen m3_comp_outlined_text_field_focus_outline_width 0x7f070166
int dimen m3_comp_outlined_text_field_outline_width 0x7f070167
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x7f070168
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x7f070169
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x7f07016a
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x7f07016b
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x7f07016c
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x7f07016d
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x7f07016e
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x7f07016f
int dimen m3_comp_progress_indicator_active_indicator_track_space 0x7f070170
int dimen m3_comp_progress_indicator_stop_indicator_size 0x7f070171
int dimen m3_comp_progress_indicator_track_thickness 0x7f070172
int dimen m3_comp_radio_button_disabled_selected_icon_opacity 0x7f070173
int dimen m3_comp_radio_button_disabled_unselected_icon_opacity 0x7f070174
int dimen m3_comp_radio_button_selected_focus_state_layer_opacity 0x7f070175
int dimen m3_comp_radio_button_selected_hover_state_layer_opacity 0x7f070176
int dimen m3_comp_radio_button_selected_pressed_state_layer_opacity 0x7f070177
int dimen m3_comp_radio_button_unselected_focus_state_layer_opacity 0x7f070178
int dimen m3_comp_radio_button_unselected_hover_state_layer_opacity 0x7f070179
int dimen m3_comp_radio_button_unselected_pressed_state_layer_opacity 0x7f07017a
int dimen m3_comp_scrim_container_opacity 0x7f07017b
int dimen m3_comp_search_bar_avatar_size 0x7f07017c
int dimen m3_comp_search_bar_container_elevation 0x7f07017d
int dimen m3_comp_search_bar_container_height 0x7f07017e
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x7f07017f
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x7f070180
int dimen m3_comp_search_view_container_elevation 0x7f070181
int dimen m3_comp_search_view_docked_header_container_height 0x7f070182
int dimen m3_comp_search_view_full_screen_header_container_height 0x7f070183
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x7f070184
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x7f070185
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x7f070186
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x7f070187
int dimen m3_comp_sheet_bottom_docked_drag_handle_height 0x7f070188
int dimen m3_comp_sheet_bottom_docked_drag_handle_width 0x7f070189
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x7f07018a
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x7f07018b
int dimen m3_comp_sheet_side_docked_container_width 0x7f07018c
int dimen m3_comp_sheet_side_docked_modal_container_elevation 0x7f07018d
int dimen m3_comp_sheet_side_docked_standard_container_elevation 0x7f07018e
int dimen m3_comp_slider_active_handle_height 0x7f07018f
int dimen m3_comp_slider_active_handle_leading_space 0x7f070190
int dimen m3_comp_slider_active_handle_width 0x7f070191
int dimen m3_comp_slider_disabled_active_track_opacity 0x7f070192
int dimen m3_comp_slider_disabled_handle_opacity 0x7f070193
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x7f070194
int dimen m3_comp_slider_inactive_track_height 0x7f070195
int dimen m3_comp_slider_stop_indicator_size 0x7f070196
int dimen m3_comp_snackbar_container_elevation 0x7f070197
int dimen m3_comp_suggestion_chip_container_height 0x7f070198
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x7f070199
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x7f07019a
int dimen m3_comp_suggestion_chip_flat_outline_width 0x7f07019b
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x7f07019c
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x7f07019d
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x7f07019e
int dimen m3_comp_switch_disabled_track_opacity 0x7f07019f
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x7f0701a0
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x7f0701a1
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x7f0701a2
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x7f0701a3
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x7f0701a4
int dimen m3_comp_switch_track_height 0x7f0701a5
int dimen m3_comp_switch_track_width 0x7f0701a6
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x7f0701a7
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x7f0701a8
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x7f0701a9
int dimen m3_comp_text_button_focus_state_layer_opacity 0x7f0701aa
int dimen m3_comp_text_button_hover_state_layer_opacity 0x7f0701ab
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x7f0701ac
int dimen m3_comp_time_input_time_input_field_focus_outline_width 0x7f0701ad
int dimen m3_comp_time_picker_container_elevation 0x7f0701ae
int dimen m3_comp_time_picker_period_selector_focus_state_layer_opacity 0x7f0701af
int dimen m3_comp_time_picker_period_selector_hover_state_layer_opacity 0x7f0701b0
int dimen m3_comp_time_picker_period_selector_outline_width 0x7f0701b1
int dimen m3_comp_time_picker_period_selector_pressed_state_layer_opacity 0x7f0701b2
int dimen m3_comp_time_picker_time_selector_focus_state_layer_opacity 0x7f0701b3
int dimen m3_comp_time_picker_time_selector_hover_state_layer_opacity 0x7f0701b4
int dimen m3_comp_time_picker_time_selector_pressed_state_layer_opacity 0x7f0701b5
int dimen m3_comp_top_app_bar_large_container_height 0x7f0701b6
int dimen m3_comp_top_app_bar_medium_container_height 0x7f0701b7
int dimen m3_comp_top_app_bar_small_container_elevation 0x7f0701b8
int dimen m3_comp_top_app_bar_small_container_height 0x7f0701b9
int dimen m3_comp_top_app_bar_small_on_scroll_container_elevation 0x7f0701ba
int dimen m3_datepicker_elevation 0x7f0701bb
int dimen m3_divider_heavy_thickness 0x7f0701bc
int dimen m3_extended_fab_bottom_padding 0x7f0701bd
int dimen m3_extended_fab_end_padding 0x7f0701be
int dimen m3_extended_fab_icon_padding 0x7f0701bf
int dimen m3_extended_fab_min_height 0x7f0701c0
int dimen m3_extended_fab_start_padding 0x7f0701c1
int dimen m3_extended_fab_top_padding 0x7f0701c2
int dimen m3_fab_border_width 0x7f0701c3
int dimen m3_fab_corner_size 0x7f0701c4
int dimen m3_fab_translation_z_hovered_focused 0x7f0701c5
int dimen m3_fab_translation_z_pressed 0x7f0701c6
int dimen m3_large_fab_max_image_size 0x7f0701c7
int dimen m3_large_fab_size 0x7f0701c8
int dimen m3_large_text_vertical_offset_adjustment 0x7f0701c9
int dimen m3_menu_elevation 0x7f0701ca
int dimen m3_nav_badge_with_text_vertical_offset 0x7f0701cb
int dimen m3_navigation_drawer_layout_corner_size 0x7f0701cc
int dimen m3_navigation_item_active_indicator_label_padding 0x7f0701cd
int dimen m3_navigation_item_horizontal_padding 0x7f0701ce
int dimen m3_navigation_item_icon_padding 0x7f0701cf
int dimen m3_navigation_item_shape_inset_bottom 0x7f0701d0
int dimen m3_navigation_item_shape_inset_end 0x7f0701d1
int dimen m3_navigation_item_shape_inset_start 0x7f0701d2
int dimen m3_navigation_item_shape_inset_top 0x7f0701d3
int dimen m3_navigation_item_vertical_padding 0x7f0701d4
int dimen m3_navigation_menu_divider_horizontal_padding 0x7f0701d5
int dimen m3_navigation_menu_headline_horizontal_padding 0x7f0701d6
int dimen m3_navigation_rail_default_width 0x7f0701d7
int dimen m3_navigation_rail_elevation 0x7f0701d8
int dimen m3_navigation_rail_icon_size 0x7f0701d9
int dimen m3_navigation_rail_item_active_indicator_height 0x7f0701da
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x7f0701db
int dimen m3_navigation_rail_item_active_indicator_width 0x7f0701dc
int dimen m3_navigation_rail_item_min_height 0x7f0701dd
int dimen m3_navigation_rail_item_padding_bottom 0x7f0701de
int dimen m3_navigation_rail_item_padding_bottom_with_large_font 0x7f0701df
int dimen m3_navigation_rail_item_padding_top 0x7f0701e0
int dimen m3_navigation_rail_item_padding_top_with_large_font 0x7f0701e1
int dimen m3_navigation_rail_label_padding_horizontal 0x7f0701e2
int dimen m3_ripple_default_alpha 0x7f0701e3
int dimen m3_ripple_focused_alpha 0x7f0701e4
int dimen m3_ripple_hovered_alpha 0x7f0701e5
int dimen m3_ripple_pressed_alpha 0x7f0701e6
int dimen m3_ripple_selectable_pressed_alpha 0x7f0701e7
int dimen m3_searchbar_elevation 0x7f0701e8
int dimen m3_searchbar_height 0x7f0701e9
int dimen m3_searchbar_margin_horizontal 0x7f0701ea
int dimen m3_searchbar_margin_vertical 0x7f0701eb
int dimen m3_searchbar_outlined_stroke_width 0x7f0701ec
int dimen m3_searchbar_padding_start 0x7f0701ed
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x7f0701ee
int dimen m3_searchbar_text_size 0x7f0701ef
int dimen m3_searchview_divider_size 0x7f0701f0
int dimen m3_searchview_elevation 0x7f0701f1
int dimen m3_searchview_height 0x7f0701f2
int dimen m3_side_sheet_margin_detached 0x7f0701f3
int dimen m3_side_sheet_modal_elevation 0x7f0701f4
int dimen m3_side_sheet_standard_elevation 0x7f0701f5
int dimen m3_side_sheet_width 0x7f0701f6
int dimen m3_simple_item_color_hovered_alpha 0x7f0701f7
int dimen m3_simple_item_color_selected_alpha 0x7f0701f8
int dimen m3_slider_thumb_elevation 0x7f0701f9
int dimen m3_small_fab_max_image_size 0x7f0701fa
int dimen m3_small_fab_size 0x7f0701fb
int dimen m3_snackbar_action_text_color_alpha 0x7f0701fc
int dimen m3_snackbar_margin 0x7f0701fd
int dimen m3_sys_elevation_level0 0x7f0701fe
int dimen m3_sys_elevation_level1 0x7f0701ff
int dimen m3_sys_elevation_level2 0x7f070200
int dimen m3_sys_elevation_level3 0x7f070201
int dimen m3_sys_elevation_level4 0x7f070202
int dimen m3_sys_elevation_level5 0x7f070203
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x7f070204
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x7f070205
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x7f070206
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x7f070207
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x7f070208
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x7f070209
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x7f07020a
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x7f07020b
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x7f07020c
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x7f07020d
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x7f07020e
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x7f07020f
int dimen m3_sys_motion_easing_legacy_control_x1 0x7f070210
int dimen m3_sys_motion_easing_legacy_control_x2 0x7f070211
int dimen m3_sys_motion_easing_legacy_control_y1 0x7f070212
int dimen m3_sys_motion_easing_legacy_control_y2 0x7f070213
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x7f070214
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x7f070215
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x7f070216
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x7f070217
int dimen m3_sys_motion_easing_linear_control_x1 0x7f070218
int dimen m3_sys_motion_easing_linear_control_x2 0x7f070219
int dimen m3_sys_motion_easing_linear_control_y1 0x7f07021a
int dimen m3_sys_motion_easing_linear_control_y2 0x7f07021b
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x7f07021c
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x7f07021d
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x7f07021e
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x7f07021f
int dimen m3_sys_motion_easing_standard_control_x1 0x7f070220
int dimen m3_sys_motion_easing_standard_control_x2 0x7f070221
int dimen m3_sys_motion_easing_standard_control_y1 0x7f070222
int dimen m3_sys_motion_easing_standard_control_y2 0x7f070223
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x7f070224
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x7f070225
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x7f070226
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x7f070227
int dimen m3_sys_state_dragged_state_layer_opacity 0x7f070228
int dimen m3_sys_state_focus_state_layer_opacity 0x7f070229
int dimen m3_sys_state_hover_state_layer_opacity 0x7f07022a
int dimen m3_sys_state_pressed_state_layer_opacity 0x7f07022b
int dimen m3_timepicker_display_stroke_width 0x7f07022c
int dimen m3_timepicker_window_elevation 0x7f07022d
int dimen m3_toolbar_text_size_title 0x7f07022e
int dimen material_bottom_sheet_max_width 0x7f07022f
int dimen material_clock_display_height 0x7f070230
int dimen material_clock_display_padding 0x7f070231
int dimen material_clock_display_width 0x7f070232
int dimen material_clock_face_margin_bottom 0x7f070233
int dimen material_clock_face_margin_top 0x7f070234
int dimen material_clock_hand_center_dot_radius 0x7f070235
int dimen material_clock_hand_padding 0x7f070236
int dimen material_clock_hand_stroke_width 0x7f070237
int dimen material_clock_number_text_size 0x7f070238
int dimen material_clock_period_toggle_height 0x7f070239
int dimen material_clock_period_toggle_horizontal_gap 0x7f07023a
int dimen material_clock_period_toggle_vertical_gap 0x7f07023b
int dimen material_clock_period_toggle_width 0x7f07023c
int dimen material_clock_size 0x7f07023d
int dimen material_cursor_inset 0x7f07023e
int dimen material_cursor_width 0x7f07023f
int dimen material_divider_thickness 0x7f070240
int dimen material_emphasis_disabled 0x7f070241
int dimen material_emphasis_disabled_background 0x7f070242
int dimen material_emphasis_high_type 0x7f070243
int dimen material_emphasis_medium 0x7f070244
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f070245
int dimen material_filled_edittext_font_1_3_padding_top 0x7f070246
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f070247
int dimen material_filled_edittext_font_2_0_padding_top 0x7f070248
int dimen material_font_1_3_box_collapsed_padding_top 0x7f070249
int dimen material_font_2_0_box_collapsed_padding_top 0x7f07024a
int dimen material_helper_text_default_padding_top 0x7f07024b
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f07024c
int dimen material_helper_text_font_1_3_padding_top 0x7f07024d
int dimen material_input_text_to_prefix_suffix_padding 0x7f07024e
int dimen material_textinput_default_width 0x7f07024f
int dimen material_textinput_max_width 0x7f070250
int dimen material_textinput_min_width 0x7f070251
int dimen material_time_picker_minimum_screen_height 0x7f070252
int dimen material_time_picker_minimum_screen_width 0x7f070253
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f070254
int dimen mtrl_alert_dialog_background_inset_end 0x7f070255
int dimen mtrl_alert_dialog_background_inset_start 0x7f070256
int dimen mtrl_alert_dialog_background_inset_top 0x7f070257
int dimen mtrl_alert_dialog_picker_background_inset 0x7f070258
int dimen mtrl_badge_horizontal_edge_offset 0x7f070259
int dimen mtrl_badge_long_text_horizontal_padding 0x7f07025a
int dimen mtrl_badge_size 0x7f07025b
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f07025c
int dimen mtrl_badge_text_size 0x7f07025d
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f07025e
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f07025f
int dimen mtrl_badge_with_text_size 0x7f070260
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f070261
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f070262
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f070263
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f070264
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f070265
int dimen mtrl_bottomappbar_height 0x7f070266
int dimen mtrl_btn_corner_radius 0x7f070267
int dimen mtrl_btn_dialog_btn_min_width 0x7f070268
int dimen mtrl_btn_disabled_elevation 0x7f070269
int dimen mtrl_btn_disabled_z 0x7f07026a
int dimen mtrl_btn_elevation 0x7f07026b
int dimen mtrl_btn_focused_z 0x7f07026c
int dimen mtrl_btn_hovered_z 0x7f07026d
int dimen mtrl_btn_icon_btn_padding_left 0x7f07026e
int dimen mtrl_btn_icon_padding 0x7f07026f
int dimen mtrl_btn_inset 0x7f070270
int dimen mtrl_btn_letter_spacing 0x7f070271
int dimen mtrl_btn_max_width 0x7f070272
int dimen mtrl_btn_padding_bottom 0x7f070273
int dimen mtrl_btn_padding_left 0x7f070274
int dimen mtrl_btn_padding_right 0x7f070275
int dimen mtrl_btn_padding_top 0x7f070276
int dimen mtrl_btn_pressed_z 0x7f070277
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f070278
int dimen mtrl_btn_stroke_size 0x7f070279
int dimen mtrl_btn_text_btn_icon_padding 0x7f07027a
int dimen mtrl_btn_text_btn_padding_left 0x7f07027b
int dimen mtrl_btn_text_btn_padding_right 0x7f07027c
int dimen mtrl_btn_text_size 0x7f07027d
int dimen mtrl_btn_z 0x7f07027e
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f07027f
int dimen mtrl_calendar_action_height 0x7f070280
int dimen mtrl_calendar_action_padding 0x7f070281
int dimen mtrl_calendar_bottom_padding 0x7f070282
int dimen mtrl_calendar_content_padding 0x7f070283
int dimen mtrl_calendar_day_corner 0x7f070284
int dimen mtrl_calendar_day_height 0x7f070285
int dimen mtrl_calendar_day_horizontal_padding 0x7f070286
int dimen mtrl_calendar_day_today_stroke 0x7f070287
int dimen mtrl_calendar_day_vertical_padding 0x7f070288
int dimen mtrl_calendar_day_width 0x7f070289
int dimen mtrl_calendar_days_of_week_height 0x7f07028a
int dimen mtrl_calendar_dialog_background_inset 0x7f07028b
int dimen mtrl_calendar_header_content_padding 0x7f07028c
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f07028d
int dimen mtrl_calendar_header_divider_thickness 0x7f07028e
int dimen mtrl_calendar_header_height 0x7f07028f
int dimen mtrl_calendar_header_height_fullscreen 0x7f070290
int dimen mtrl_calendar_header_selection_line_height 0x7f070291
int dimen mtrl_calendar_header_text_padding 0x7f070292
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f070293
int dimen mtrl_calendar_header_toggle_margin_top 0x7f070294
int dimen mtrl_calendar_landscape_header_width 0x7f070295
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f070296
int dimen mtrl_calendar_month_horizontal_padding 0x7f070297
int dimen mtrl_calendar_month_vertical_padding 0x7f070298
int dimen mtrl_calendar_navigation_bottom_padding 0x7f070299
int dimen mtrl_calendar_navigation_height 0x7f07029a
int dimen mtrl_calendar_navigation_top_padding 0x7f07029b
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f07029c
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f07029d
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f07029e
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f07029f
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f0702a0
int dimen mtrl_calendar_text_input_padding_top 0x7f0702a1
int dimen mtrl_calendar_title_baseline_to_top 0x7f0702a2
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f0702a3
int dimen mtrl_calendar_year_corner 0x7f0702a4
int dimen mtrl_calendar_year_height 0x7f0702a5
int dimen mtrl_calendar_year_horizontal_padding 0x7f0702a6
int dimen mtrl_calendar_year_vertical_padding 0x7f0702a7
int dimen mtrl_calendar_year_width 0x7f0702a8
int dimen mtrl_card_checked_icon_margin 0x7f0702a9
int dimen mtrl_card_checked_icon_size 0x7f0702aa
int dimen mtrl_card_corner_radius 0x7f0702ab
int dimen mtrl_card_dragged_z 0x7f0702ac
int dimen mtrl_card_elevation 0x7f0702ad
int dimen mtrl_card_spacing 0x7f0702ae
int dimen mtrl_chip_pressed_translation_z 0x7f0702af
int dimen mtrl_chip_text_size 0x7f0702b0
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f0702b1
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f0702b2
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f0702b3
int dimen mtrl_extended_fab_bottom_padding 0x7f0702b4
int dimen mtrl_extended_fab_disabled_elevation 0x7f0702b5
int dimen mtrl_extended_fab_disabled_translation_z 0x7f0702b6
int dimen mtrl_extended_fab_elevation 0x7f0702b7
int dimen mtrl_extended_fab_end_padding 0x7f0702b8
int dimen mtrl_extended_fab_end_padding_icon 0x7f0702b9
int dimen mtrl_extended_fab_icon_size 0x7f0702ba
int dimen mtrl_extended_fab_icon_text_spacing 0x7f0702bb
int dimen mtrl_extended_fab_min_height 0x7f0702bc
int dimen mtrl_extended_fab_min_width 0x7f0702bd
int dimen mtrl_extended_fab_start_padding 0x7f0702be
int dimen mtrl_extended_fab_start_padding_icon 0x7f0702bf
int dimen mtrl_extended_fab_top_padding 0x7f0702c0
int dimen mtrl_extended_fab_translation_z_base 0x7f0702c1
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f0702c2
int dimen mtrl_extended_fab_translation_z_pressed 0x7f0702c3
int dimen mtrl_fab_elevation 0x7f0702c4
int dimen mtrl_fab_min_touch_target 0x7f0702c5
int dimen mtrl_fab_translation_z_hovered_focused 0x7f0702c6
int dimen mtrl_fab_translation_z_pressed 0x7f0702c7
int dimen mtrl_high_ripple_default_alpha 0x7f0702c8
int dimen mtrl_high_ripple_focused_alpha 0x7f0702c9
int dimen mtrl_high_ripple_hovered_alpha 0x7f0702ca
int dimen mtrl_high_ripple_pressed_alpha 0x7f0702cb
int dimen mtrl_low_ripple_default_alpha 0x7f0702cc
int dimen mtrl_low_ripple_focused_alpha 0x7f0702cd
int dimen mtrl_low_ripple_hovered_alpha 0x7f0702ce
int dimen mtrl_low_ripple_pressed_alpha 0x7f0702cf
int dimen mtrl_min_touch_target_size 0x7f0702d0
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f0702d1
int dimen mtrl_navigation_bar_item_default_margin 0x7f0702d2
int dimen mtrl_navigation_elevation 0x7f0702d3
int dimen mtrl_navigation_item_horizontal_padding 0x7f0702d4
int dimen mtrl_navigation_item_icon_padding 0x7f0702d5
int dimen mtrl_navigation_item_icon_size 0x7f0702d6
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f0702d7
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f0702d8
int dimen mtrl_navigation_rail_active_text_size 0x7f0702d9
int dimen mtrl_navigation_rail_compact_width 0x7f0702da
int dimen mtrl_navigation_rail_default_width 0x7f0702db
int dimen mtrl_navigation_rail_elevation 0x7f0702dc
int dimen mtrl_navigation_rail_icon_margin 0x7f0702dd
int dimen mtrl_navigation_rail_icon_size 0x7f0702de
int dimen mtrl_navigation_rail_margin 0x7f0702df
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f0702e0
int dimen mtrl_navigation_rail_text_size 0x7f0702e1
int dimen mtrl_progress_circular_inset 0x7f0702e2
int dimen mtrl_progress_circular_inset_extra_small 0x7f0702e3
int dimen mtrl_progress_circular_inset_medium 0x7f0702e4
int dimen mtrl_progress_circular_inset_small 0x7f0702e5
int dimen mtrl_progress_circular_radius 0x7f0702e6
int dimen mtrl_progress_circular_size 0x7f0702e7
int dimen mtrl_progress_circular_size_extra_small 0x7f0702e8
int dimen mtrl_progress_circular_size_medium 0x7f0702e9
int dimen mtrl_progress_circular_size_small 0x7f0702ea
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f0702eb
int dimen mtrl_progress_circular_track_thickness_medium 0x7f0702ec
int dimen mtrl_progress_circular_track_thickness_small 0x7f0702ed
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f0702ee
int dimen mtrl_progress_track_thickness 0x7f0702ef
int dimen mtrl_shape_corner_size_large_component 0x7f0702f0
int dimen mtrl_shape_corner_size_medium_component 0x7f0702f1
int dimen mtrl_shape_corner_size_small_component 0x7f0702f2
int dimen mtrl_slider_halo_radius 0x7f0702f3
int dimen mtrl_slider_label_padding 0x7f0702f4
int dimen mtrl_slider_label_radius 0x7f0702f5
int dimen mtrl_slider_label_square_side 0x7f0702f6
int dimen mtrl_slider_thumb_elevation 0x7f0702f7
int dimen mtrl_slider_thumb_radius 0x7f0702f8
int dimen mtrl_slider_tick_min_spacing 0x7f0702f9
int dimen mtrl_slider_tick_radius 0x7f0702fa
int dimen mtrl_slider_track_height 0x7f0702fb
int dimen mtrl_slider_track_side_padding 0x7f0702fc
int dimen mtrl_slider_widget_height 0x7f0702fd
int dimen mtrl_snackbar_action_text_color_alpha 0x7f0702fe
int dimen mtrl_snackbar_background_corner_radius 0x7f0702ff
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f070300
int dimen mtrl_snackbar_margin 0x7f070301
int dimen mtrl_snackbar_message_margin_horizontal 0x7f070302
int dimen mtrl_snackbar_padding_horizontal 0x7f070303
int dimen mtrl_switch_text_padding 0x7f070304
int dimen mtrl_switch_thumb_elevation 0x7f070305
int dimen mtrl_switch_thumb_icon_size 0x7f070306
int dimen mtrl_switch_thumb_size 0x7f070307
int dimen mtrl_switch_track_height 0x7f070308
int dimen mtrl_switch_track_width 0x7f070309
int dimen mtrl_textinput_box_corner_radius_medium 0x7f07030a
int dimen mtrl_textinput_box_corner_radius_small 0x7f07030b
int dimen mtrl_textinput_box_label_cutout_padding 0x7f07030c
int dimen mtrl_textinput_box_stroke_width_default 0x7f07030d
int dimen mtrl_textinput_box_stroke_width_focused 0x7f07030e
int dimen mtrl_textinput_counter_margin_start 0x7f07030f
int dimen mtrl_textinput_end_icon_margin_start 0x7f070310
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f070311
int dimen mtrl_textinput_start_icon_margin_end 0x7f070312
int dimen mtrl_toolbar_default_height 0x7f070313
int dimen mtrl_tooltip_arrowSize 0x7f070314
int dimen mtrl_tooltip_cornerSize 0x7f070315
int dimen mtrl_tooltip_minHeight 0x7f070316
int dimen mtrl_tooltip_minWidth 0x7f070317
int dimen mtrl_tooltip_padding 0x7f070318
int dimen mtrl_transition_shared_axis_slide_distance 0x7f070319
int dimen notification_action_icon_size 0x7f07031a
int dimen notification_action_text_size 0x7f07031b
int dimen notification_big_circle_margin 0x7f07031c
int dimen notification_content_margin_start 0x7f07031d
int dimen notification_large_icon_height 0x7f07031e
int dimen notification_large_icon_width 0x7f07031f
int dimen notification_main_column_padding_top 0x7f070320
int dimen notification_media_narrow_margin 0x7f070321
int dimen notification_right_icon_size 0x7f070322
int dimen notification_right_side_padding_top 0x7f070323
int dimen notification_small_icon_background_padding 0x7f070324
int dimen notification_small_icon_size_as_large 0x7f070325
int dimen notification_subtext_size 0x7f070326
int dimen notification_top_pad 0x7f070327
int dimen notification_top_pad_large_text 0x7f070328
int dimen preference_dropdown_padding_start 0x7f070329
int dimen preference_icon_minWidth 0x7f07032a
int dimen preference_seekbar_padding_horizontal 0x7f07032b
int dimen preference_seekbar_padding_vertical 0x7f07032c
int dimen preference_seekbar_value_minWidth 0x7f07032d
int dimen preferences_detail_width 0x7f07032e
int dimen preferences_header_width 0x7f07032f
int dimen strokeWidth 0x7f070330
int dimen subtitle_corner_radius 0x7f070331
int dimen subtitle_outline_width 0x7f070332
int dimen subtitle_shadow_offset 0x7f070333
int dimen subtitle_shadow_radius 0x7f070334
int dimen tooltip_corner_radius 0x7f070335
int dimen tooltip_horizontal_padding 0x7f070336
int dimen tooltip_margin 0x7f070337
int dimen tooltip_precise_anchor_extra_offset 0x7f070338
int dimen tooltip_precise_anchor_threshold 0x7f070339
int dimen tooltip_vertical_padding 0x7f07033a
int dimen tooltip_y_offset_non_touch 0x7f07033b
int dimen tooltip_y_offset_touch 0x7f07033c
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080028
int drawable abc_action_bar_item_background_material 0x7f080029
int drawable abc_btn_borderless_material 0x7f08002a
int drawable abc_btn_check_material 0x7f08002b
int drawable abc_btn_check_material_anim 0x7f08002c
int drawable abc_btn_check_to_on_mtrl_000 0x7f08002d
int drawable abc_btn_check_to_on_mtrl_015 0x7f08002e
int drawable abc_btn_colored_material 0x7f08002f
int drawable abc_btn_default_mtrl_shape 0x7f080030
int drawable abc_btn_radio_material 0x7f080031
int drawable abc_btn_radio_material_anim 0x7f080032
int drawable abc_btn_radio_to_on_mtrl_000 0x7f080033
int drawable abc_btn_radio_to_on_mtrl_015 0x7f080034
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f080035
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f080036
int drawable abc_cab_background_internal_bg 0x7f080037
int drawable abc_cab_background_top_material 0x7f080038
int drawable abc_cab_background_top_mtrl_alpha 0x7f080039
int drawable abc_control_background_material 0x7f08003a
int drawable abc_dialog_material_background 0x7f08003b
int drawable abc_edit_text_material 0x7f08003c
int drawable abc_ic_ab_back_material 0x7f08003d
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f08003e
int drawable abc_ic_clear_material 0x7f08003f
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f080040
int drawable abc_ic_go_search_api_material 0x7f080041
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f080042
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f080043
int drawable abc_ic_menu_overflow_material 0x7f080044
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f080045
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f080046
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080047
int drawable abc_ic_search_api_material 0x7f080048
int drawable abc_ic_voice_search_api_material 0x7f080049
int drawable abc_item_background_holo_dark 0x7f08004a
int drawable abc_item_background_holo_light 0x7f08004b
int drawable abc_list_divider_material 0x7f08004c
int drawable abc_list_divider_mtrl_alpha 0x7f08004d
int drawable abc_list_focused_holo 0x7f08004e
int drawable abc_list_longpressed_holo 0x7f08004f
int drawable abc_list_pressed_holo_dark 0x7f080050
int drawable abc_list_pressed_holo_light 0x7f080051
int drawable abc_list_selector_background_transition_holo_dark 0x7f080052
int drawable abc_list_selector_background_transition_holo_light 0x7f080053
int drawable abc_list_selector_disabled_holo_dark 0x7f080054
int drawable abc_list_selector_disabled_holo_light 0x7f080055
int drawable abc_list_selector_holo_dark 0x7f080056
int drawable abc_list_selector_holo_light 0x7f080057
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080058
int drawable abc_popup_background_mtrl_mult 0x7f080059
int drawable abc_ratingbar_indicator_material 0x7f08005a
int drawable abc_ratingbar_material 0x7f08005b
int drawable abc_ratingbar_small_material 0x7f08005c
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08005d
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08005e
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08005f
int drawable abc_scrubber_primary_mtrl_alpha 0x7f080060
int drawable abc_scrubber_track_mtrl_alpha 0x7f080061
int drawable abc_seekbar_thumb_material 0x7f080062
int drawable abc_seekbar_tick_mark_material 0x7f080063
int drawable abc_seekbar_track_material 0x7f080064
int drawable abc_spinner_mtrl_am_alpha 0x7f080065
int drawable abc_spinner_textfield_background_material 0x7f080066
int drawable abc_star_black_48dp 0x7f080067
int drawable abc_star_half_black_48dp 0x7f080068
int drawable abc_switch_thumb_material 0x7f080069
int drawable abc_switch_track_mtrl_alpha 0x7f08006a
int drawable abc_tab_indicator_material 0x7f08006b
int drawable abc_tab_indicator_mtrl_alpha 0x7f08006c
int drawable abc_text_cursor_material 0x7f08006d
int drawable abc_text_select_handle_left_mtrl 0x7f08006e
int drawable abc_text_select_handle_middle_mtrl 0x7f08006f
int drawable abc_text_select_handle_right_mtrl 0x7f080070
int drawable abc_textfield_activated_mtrl_alpha 0x7f080071
int drawable abc_textfield_default_mtrl_alpha 0x7f080072
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080073
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080074
int drawable abc_textfield_search_material 0x7f080075
int drawable abc_vector_test 0x7f080076
int drawable about 0x7f080077
int drawable admob_close_button_black_circle_white_cross 0x7f080078
int drawable admob_close_button_white_circle_black_cross 0x7f080079
int drawable avd_hide_password 0x7f08007a
int drawable avd_show_password 0x7f08007b
int drawable black 0x7f08007c
int drawable border 0x7f08007d
int drawable brown 0x7f08007e
int drawable btn_checkbox_checked_mtrl 0x7f08007f
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f080080
int drawable btn_checkbox_unchecked_mtrl 0x7f080081
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f080082
int drawable btn_radio_off_mtrl 0x7f080083
int drawable btn_radio_off_to_on_mtrl_animation 0x7f080084
int drawable btn_radio_on_mtrl 0x7f080085
int drawable btn_radio_on_to_off_mtrl_animation 0x7f080086
int drawable common_full_open_on_phone 0x7f080087
int drawable common_google_signin_btn_icon_dark 0x7f080088
int drawable common_google_signin_btn_icon_dark_focused 0x7f080089
int drawable common_google_signin_btn_icon_dark_normal 0x7f08008a
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f08008b
int drawable common_google_signin_btn_icon_disabled 0x7f08008c
int drawable common_google_signin_btn_icon_light 0x7f08008d
int drawable common_google_signin_btn_icon_light_focused 0x7f08008e
int drawable common_google_signin_btn_icon_light_normal 0x7f08008f
int drawable common_google_signin_btn_icon_light_normal_background 0x7f080090
int drawable common_google_signin_btn_text_dark 0x7f080091
int drawable common_google_signin_btn_text_dark_focused 0x7f080092
int drawable common_google_signin_btn_text_dark_normal 0x7f080093
int drawable common_google_signin_btn_text_dark_normal_background 0x7f080094
int drawable common_google_signin_btn_text_disabled 0x7f080095
int drawable common_google_signin_btn_text_light 0x7f080096
int drawable common_google_signin_btn_text_light_focused 0x7f080097
int drawable common_google_signin_btn_text_light_normal 0x7f080098
int drawable common_google_signin_btn_text_light_normal_background 0x7f080099
int drawable decorative_circle 0x7f08009a
int drawable deep_blue 0x7f08009b
int drawable deep_green 0x7f08009c
int drawable deep_orange 0x7f08009d
int drawable deep_pink 0x7f08009e
int drawable deep_purple 0x7f08009f
int drawable design_fab_background 0x7f0800a0
int drawable design_ic_visibility 0x7f0800a1
int drawable design_ic_visibility_off 0x7f0800a2
int drawable design_password_eye 0x7f0800a3
int drawable design_snackbar_background 0x7f0800a4
int drawable googleg_disabled_color_18 0x7f0800a5
int drawable googleg_standard_color_18 0x7f0800a6
int drawable gp1 0x7f0800a7
int drawable gp1_1 0x7f0800a8
int drawable gp1_2 0x7f0800a9
int drawable gp1_3 0x7f0800aa
int drawable gp1_4 0x7f0800ab
int drawable gp1_5 0x7f0800ac
int drawable gp1_6 0x7f0800ad
int drawable gp1_press 0x7f0800ae
int drawable gp2 0x7f0800af
int drawable gp2_1 0x7f0800b0
int drawable gp2_2 0x7f0800b1
int drawable gp2_3 0x7f0800b2
int drawable gp2_4 0x7f0800b3
int drawable gp2_5 0x7f0800b4
int drawable gp2_6 0x7f0800b5
int drawable gp2_7 0x7f0800b6
int drawable gp2_8 0x7f0800b7
int drawable gp2_press 0x7f0800b8
int drawable gp3 0x7f0800b9
int drawable gp3_1 0x7f0800ba
int drawable gp3_10 0x7f0800bb
int drawable gp3_11 0x7f0800bc
int drawable gp3_12 0x7f0800bd
int drawable gp3_13 0x7f0800be
int drawable gp3_14 0x7f0800bf
int drawable gp3_15 0x7f0800c0
int drawable gp3_16 0x7f0800c1
int drawable gp3_17 0x7f0800c2
int drawable gp3_18 0x7f0800c3
int drawable gp3_19 0x7f0800c4
int drawable gp3_2 0x7f0800c5
int drawable gp3_20 0x7f0800c6
int drawable gp3_21 0x7f0800c7
int drawable gp3_22 0x7f0800c8
int drawable gp3_23 0x7f0800c9
int drawable gp3_24 0x7f0800ca
int drawable gp3_25 0x7f0800cb
int drawable gp3_26 0x7f0800cc
int drawable gp3_27 0x7f0800cd
int drawable gp3_28 0x7f0800ce
int drawable gp3_29 0x7f0800cf
int drawable gp3_3 0x7f0800d0
int drawable gp3_30 0x7f0800d1
int drawable gp3_31 0x7f0800d2
int drawable gp3_32 0x7f0800d3
int drawable gp3_33 0x7f0800d4
int drawable gp3_34 0x7f0800d5
int drawable gp3_35 0x7f0800d6
int drawable gp3_36 0x7f0800d7
int drawable gp3_37 0x7f0800d8
int drawable gp3_38 0x7f0800d9
int drawable gp3_39 0x7f0800da
int drawable gp3_4 0x7f0800db
int drawable gp3_40 0x7f0800dc
int drawable gp3_41 0x7f0800dd
int drawable gp3_42 0x7f0800de
int drawable gp3_43 0x7f0800df
int drawable gp3_44 0x7f0800e0
int drawable gp3_45 0x7f0800e1
int drawable gp3_46 0x7f0800e2
int drawable gp3_47 0x7f0800e3
int drawable gp3_48 0x7f0800e4
int drawable gp3_49 0x7f0800e5
int drawable gp3_5 0x7f0800e6
int drawable gp3_50 0x7f0800e7
int drawable gp3_51 0x7f0800e8
int drawable gp3_52 0x7f0800e9
int drawable gp3_53 0x7f0800ea
int drawable gp3_54 0x7f0800eb
int drawable gp3_55 0x7f0800ec
int drawable gp3_56 0x7f0800ed
int drawable gp3_57 0x7f0800ee
int drawable gp3_58 0x7f0800ef
int drawable gp3_59 0x7f0800f0
int drawable gp3_6 0x7f0800f1
int drawable gp3_60 0x7f0800f2
int drawable gp3_61 0x7f0800f3
int drawable gp3_62 0x7f0800f4
int drawable gp3_63 0x7f0800f5
int drawable gp3_64 0x7f0800f6
int drawable gp3_65 0x7f0800f7
int drawable gp3_66 0x7f0800f8
int drawable gp3_67 0x7f0800f9
int drawable gp3_68 0x7f0800fa
int drawable gp3_69 0x7f0800fb
int drawable gp3_7 0x7f0800fc
int drawable gp3_70 0x7f0800fd
int drawable gp3_71 0x7f0800fe
int drawable gp3_72 0x7f0800ff
int drawable gp3_73 0x7f080100
int drawable gp3_74 0x7f080101
int drawable gp3_75 0x7f080102
int drawable gp3_76 0x7f080103
int drawable gp3_77 0x7f080104
int drawable gp3_78 0x7f080105
int drawable gp3_79 0x7f080106
int drawable gp3_8 0x7f080107
int drawable gp3_80 0x7f080108
int drawable gp3_81 0x7f080109
int drawable gp3_82 0x7f08010a
int drawable gp3_83 0x7f08010b
int drawable gp3_84 0x7f08010c
int drawable gp3_85 0x7f08010d
int drawable gp3_86 0x7f08010e
int drawable gp3_9 0x7f08010f
int drawable gp3_press 0x7f080110
int drawable gp4 0x7f080111
int drawable gp4_1 0x7f080112
int drawable gp4_2 0x7f080113
int drawable gp4_3 0x7f080114
int drawable gp4_4 0x7f080115
int drawable gp4_5 0x7f080116
int drawable gp4_6 0x7f080117
int drawable gp4_press 0x7f080118
int drawable gp5 0x7f080119
int drawable gp5_1 0x7f08011a
int drawable gp5_2 0x7f08011b
int drawable gp5_3 0x7f08011c
int drawable gp5_4 0x7f08011d
int drawable gp5_5 0x7f08011e
int drawable gp5_6 0x7f08011f
int drawable gp5_press 0x7f080120
int drawable gp6 0x7f080121
int drawable gp6_1 0x7f080122
int drawable gp6_2 0x7f080123
int drawable gp6_3 0x7f080124
int drawable gp6_4 0x7f080125
int drawable gp6_5 0x7f080126
int drawable gp6_6 0x7f080127
int drawable gp6_press 0x7f080128
int drawable gray 0x7f080129
int drawable ic_ads_modern 0x7f08012a
int drawable ic_animals_modern 0x7f08012b
int drawable ic_arrow_back_black_24 0x7f08012c
int drawable ic_arrow_down_24dp 0x7f08012d
int drawable ic_call_answer 0x7f08012e
int drawable ic_call_answer_low 0x7f08012f
int drawable ic_call_answer_video 0x7f080130
int drawable ic_call_answer_video_low 0x7f080131
int drawable ic_call_decline 0x7f080132
int drawable ic_call_decline_low 0x7f080133
int drawable ic_cartoons_modern 0x7f080134
int drawable ic_categories_modern 0x7f080135
int drawable ic_clear_black_24 0x7f080136
int drawable ic_clock_black_24dp 0x7f080137
int drawable ic_favorites_modern 0x7f080138
int drawable ic_flowers_modern 0x7f080139
int drawable ic_foods_modern 0x7f08013a
int drawable ic_gallery_modern 0x7f08013b
int drawable ic_home_modern 0x7f08013c
int drawable ic_info_modern 0x7f08013d
int drawable ic_keyboard_black_24dp 0x7f08013e
int drawable ic_launcher_background 0x7f08013f
int drawable ic_launcher_foreground 0x7f080140
int drawable ic_m3_chip_check 0x7f080141
int drawable ic_m3_chip_checked_circle 0x7f080142
int drawable ic_m3_chip_close 0x7f080143
int drawable ic_mtrl_checked_circle 0x7f080144
int drawable ic_mtrl_chip_checked_black 0x7f080145
int drawable ic_mtrl_chip_checked_circle 0x7f080146
int drawable ic_mtrl_chip_close_circle 0x7f080147
int drawable ic_nature_modern 0x7f080148
int drawable ic_os_notification_fallback_white_24dp 0x7f080149
int drawable ic_privacy_modern 0x7f08014a
int drawable ic_search_black_24 0x7f08014b
int drawable ic_settings_modern 0x7f08014c
int drawable ic_share_modern 0x7f08014d
int drawable ic_star_modern 0x7f08014e
int drawable ic_support_modern 0x7f08014f
int drawable ic_transport_modern 0x7f080150
int drawable indeterminate_static 0x7f080151
int drawable light_blue 0x7f080152
int drawable light_green 0x7f080153
int drawable light_orange 0x7f080154
int drawable light_pink 0x7f080155
int drawable light_purple 0x7f080156
int drawable logo 0x7f080157
int drawable m3_avd_hide_password 0x7f080158
int drawable m3_avd_show_password 0x7f080159
int drawable m3_bottom_sheet_drag_handle 0x7f08015a
int drawable m3_password_eye 0x7f08015b
int drawable m3_popupmenu_background_overlay 0x7f08015c
int drawable m3_radiobutton_ripple 0x7f08015d
int drawable m3_selection_control_ripple 0x7f08015e
int drawable m3_tabs_background 0x7f08015f
int drawable m3_tabs_line_indicator 0x7f080160
int drawable m3_tabs_rounded_line_indicator 0x7f080161
int drawable m3_tabs_transparent_background 0x7f080162
int drawable main 0x7f080163
int drawable material_cursor_drawable 0x7f080164
int drawable material_ic_calendar_black_24dp 0x7f080165
int drawable material_ic_clear_black_24dp 0x7f080166
int drawable material_ic_edit_black_24dp 0x7f080167
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f080168
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f080169
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f08016a
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f08016b
int drawable material_ic_menu_arrow_down_black_24dp 0x7f08016c
int drawable material_ic_menu_arrow_up_black_24dp 0x7f08016d
int drawable modern_card_background 0x7f08016e
int drawable modern_card_overlay 0x7f08016f
int drawable modern_gradient_background 0x7f080170
int drawable modern_ripple_effect 0x7f080171
int drawable mtrl_bottomsheet_drag_handle 0x7f080172
int drawable mtrl_checkbox_button 0x7f080173
int drawable mtrl_checkbox_button_checked_unchecked 0x7f080174
int drawable mtrl_checkbox_button_icon 0x7f080175
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x7f080176
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x7f080177
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x7f080178
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x7f080179
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x7f08017a
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x7f08017b
int drawable mtrl_checkbox_button_unchecked_checked 0x7f08017c
int drawable mtrl_dialog_background 0x7f08017d
int drawable mtrl_dropdown_arrow 0x7f08017e
int drawable mtrl_ic_arrow_drop_down 0x7f08017f
int drawable mtrl_ic_arrow_drop_up 0x7f080180
int drawable mtrl_ic_cancel 0x7f080181
int drawable mtrl_ic_check_mark 0x7f080182
int drawable mtrl_ic_checkbox_checked 0x7f080183
int drawable mtrl_ic_checkbox_unchecked 0x7f080184
int drawable mtrl_ic_error 0x7f080185
int drawable mtrl_ic_indeterminate 0x7f080186
int drawable mtrl_navigation_bar_item_background 0x7f080187
int drawable mtrl_popupmenu_background 0x7f080188
int drawable mtrl_popupmenu_background_overlay 0x7f080189
int drawable mtrl_switch_thumb 0x7f08018a
int drawable mtrl_switch_thumb_checked 0x7f08018b
int drawable mtrl_switch_thumb_checked_pressed 0x7f08018c
int drawable mtrl_switch_thumb_checked_unchecked 0x7f08018d
int drawable mtrl_switch_thumb_pressed 0x7f08018e
int drawable mtrl_switch_thumb_pressed_checked 0x7f08018f
int drawable mtrl_switch_thumb_pressed_unchecked 0x7f080190
int drawable mtrl_switch_thumb_unchecked 0x7f080191
int drawable mtrl_switch_thumb_unchecked_checked 0x7f080192
int drawable mtrl_switch_thumb_unchecked_pressed 0x7f080193
int drawable mtrl_switch_track 0x7f080194
int drawable mtrl_switch_track_decoration 0x7f080195
int drawable mtrl_tabs_default_indicator 0x7f080196
int drawable nav_header_pattern 0x7f080197
int drawable navigation_empty_icon 0x7f080198
int drawable navitemcolor 0x7f080199
int drawable notification_action_background 0x7f08019a
int drawable notification_bg 0x7f08019b
int drawable notification_bg_low 0x7f08019c
int drawable notification_bg_low_normal 0x7f08019d
int drawable notification_bg_low_pressed 0x7f08019e
int drawable notification_bg_normal 0x7f08019f
int drawable notification_bg_normal_pressed 0x7f0801a0
int drawable notification_icon_background 0x7f0801a1
int drawable notification_oversize_large_icon_bg 0x7f0801a2
int drawable notification_template_icon_bg 0x7f0801a3
int drawable notification_template_icon_low_bg 0x7f0801a4
int drawable notification_tile_bg 0x7f0801a5
int drawable notify_panel_notification_icon_bg 0x7f0801a6
int drawable preference_list_divider_material 0x7f0801a7
int drawable premium_button_background 0x7f0801a8
int drawable premium_card_overlay 0x7f0801a9
int drawable premium_logo_background 0x7f0801aa
int drawable premium_nav_header_background 0x7f0801ab
int drawable premium_nav_item_background 0x7f0801ac
int drawable premium_nav_item_color 0x7f0801ad
int drawable premium_nav_pattern 0x7f0801ae
int drawable premium_ripple_effect 0x7f0801af
int drawable premium_text_background 0x7f0801b0
int drawable premium_version_badge 0x7f0801b1
int drawable pressed_no_corners 0x7f0801b2
int drawable privacy 0x7f0801b3
int drawable red 0x7f0801b4
int drawable save_icon 0x7f0801b5
int drawable select_color 0x7f0801b6
int drawable settings 0x7f0801b7
int drawable share 0x7f0801b8
int drawable share_icon 0x7f0801b9
int drawable small_logo 0x7f0801ba
int drawable sound_off 0x7f0801bb
int drawable sound_on 0x7f0801bc
int drawable star 0x7f0801bd
int drawable test_level_drawable 0x7f0801be
int drawable tooltip_frame_dark 0x7f0801bf
int drawable tooltip_frame_light 0x7f0801c0
int drawable undo_icon 0x7f0801c1
int drawable white 0x7f0801c2
int drawable yellow 0x7f0801c3
int font blabeloo 0x7f090000
int font blabeloo_regular 0x7f090001
int id ALT 0x7f0a0000
int id BOTTOM_END 0x7f0a0001
int id BOTTOM_START 0x7f0a0002
int id CIRCLE 0x7f0a0003
int id CTRL 0x7f0a0004
int id FLOWER 0x7f0a0005
int id FUNCTION 0x7f0a0006
int id META 0x7f0a0007
int id NO_DEBUG 0x7f0a0008
int id SHIFT 0x7f0a0009
int id SHOW_ALL 0x7f0a000a
int id SHOW_PATH 0x7f0a000b
int id SHOW_PROGRESS 0x7f0a000c
int id SYM 0x7f0a000d
int id TOP_END 0x7f0a000e
int id TOP_START 0x7f0a000f
int id about 0x7f0a0010
int id accelerate 0x7f0a0011
int id accessibility_action_clickable_span 0x7f0a0012
int id accessibility_custom_action_0 0x7f0a0013
int id accessibility_custom_action_1 0x7f0a0014
int id accessibility_custom_action_10 0x7f0a0015
int id accessibility_custom_action_11 0x7f0a0016
int id accessibility_custom_action_12 0x7f0a0017
int id accessibility_custom_action_13 0x7f0a0018
int id accessibility_custom_action_14 0x7f0a0019
int id accessibility_custom_action_15 0x7f0a001a
int id accessibility_custom_action_16 0x7f0a001b
int id accessibility_custom_action_17 0x7f0a001c
int id accessibility_custom_action_18 0x7f0a001d
int id accessibility_custom_action_19 0x7f0a001e
int id accessibility_custom_action_2 0x7f0a001f
int id accessibility_custom_action_20 0x7f0a0020
int id accessibility_custom_action_21 0x7f0a0021
int id accessibility_custom_action_22 0x7f0a0022
int id accessibility_custom_action_23 0x7f0a0023
int id accessibility_custom_action_24 0x7f0a0024
int id accessibility_custom_action_25 0x7f0a0025
int id accessibility_custom_action_26 0x7f0a0026
int id accessibility_custom_action_27 0x7f0a0027
int id accessibility_custom_action_28 0x7f0a0028
int id accessibility_custom_action_29 0x7f0a0029
int id accessibility_custom_action_3 0x7f0a002a
int id accessibility_custom_action_30 0x7f0a002b
int id accessibility_custom_action_31 0x7f0a002c
int id accessibility_custom_action_4 0x7f0a002d
int id accessibility_custom_action_5 0x7f0a002e
int id accessibility_custom_action_6 0x7f0a002f
int id accessibility_custom_action_7 0x7f0a0030
int id accessibility_custom_action_8 0x7f0a0031
int id accessibility_custom_action_9 0x7f0a0032
int id action0 0x7f0a0033
int id action_bar 0x7f0a0034
int id action_bar_activity_content 0x7f0a0035
int id action_bar_container 0x7f0a0036
int id action_bar_root 0x7f0a0037
int id action_bar_spinner 0x7f0a0038
int id action_bar_subtitle 0x7f0a0039
int id action_bar_title 0x7f0a003a
int id action_container 0x7f0a003b
int id action_context_bar 0x7f0a003c
int id action_divider 0x7f0a003d
int id action_image 0x7f0a003e
int id action_menu_divider 0x7f0a003f
int id action_menu_presenter 0x7f0a0040
int id action_mode_bar 0x7f0a0041
int id action_mode_bar_stub 0x7f0a0042
int id action_mode_close_button 0x7f0a0043
int id action_mute 0x7f0a0044
int id action_save 0x7f0a0045
int id action_settings 0x7f0a0046
int id action_share 0x7f0a0047
int id action_text 0x7f0a0048
int id action_undo 0x7f0a0049
int id actions 0x7f0a004a
int id activity_chooser_view_content 0x7f0a004b
int id activity_container 0x7f0a004c
int id activity_content 0x7f0a004d
int id adView 0x7f0a004e
int id add 0x7f0a004f
int id adjust_height 0x7f0a0050
int id adjust_width 0x7f0a0051
int id ads 0x7f0a0052
int id alertTitle 0x7f0a0053
int id aligned 0x7f0a0054
int id all 0x7f0a0055
int id always 0x7f0a0056
int id androidx_window_activity_scope 0x7f0a0057
int id animateToEnd 0x7f0a0058
int id animateToStart 0x7f0a0059
int id arc 0x7f0a005a
int id asConfigured 0x7f0a005b
int id async 0x7f0a005c
int id auto 0x7f0a005d
int id autoComplete 0x7f0a005e
int id autoCompleteToEnd 0x7f0a005f
int id autoCompleteToStart 0x7f0a0060
int id barrier 0x7f0a0061
int id baseline 0x7f0a0062
int id beginOnFirstDraw 0x7f0a0063
int id beginning 0x7f0a0064
int id bites 0x7f0a0065
int id black 0x7f0a0066
int id blocking 0x7f0a0067
int id bottom 0x7f0a0068
int id bounce 0x7f0a0069
int id brown 0x7f0a006a
int id browser_actions_header_text 0x7f0a006b
int id browser_actions_menu_item_icon 0x7f0a006c
int id browser_actions_menu_item_text 0x7f0a006d
int id browser_actions_menu_items 0x7f0a006e
int id browser_actions_menu_view 0x7f0a006f
int id buttonPanel 0x7f0a0070
int id c_1 0x7f0a0071
int id c_2 0x7f0a0072
int id c_3 0x7f0a0073
int id c_4 0x7f0a0074
int id c_5 0x7f0a0075
int id c_6 0x7f0a0076
int id cancel_action 0x7f0a0077
int id cancel_button 0x7f0a0078
int id categories 0x7f0a0079
int id center 0x7f0a007a
int id centerCrop 0x7f0a007b
int id centerInside 0x7f0a007c
int id center_horizontal 0x7f0a007d
int id center_vertical 0x7f0a007e
int id chain 0x7f0a007f
int id chains 0x7f0a0080
int id checkbox 0x7f0a0081
int id checked 0x7f0a0082
int id chronometer 0x7f0a0083
int id circle_center 0x7f0a0084
int id clear_text 0x7f0a0085
int id clip_horizontal 0x7f0a0086
int id clip_vertical 0x7f0a0087
int id clockwise 0x7f0a0088
int id collapseActionView 0x7f0a0089
int id color_indicator 0x7f0a008a
int id compress 0x7f0a008b
int id confirm_button 0x7f0a008c
int id container 0x7f0a008d
int id content 0x7f0a008e
int id contentPanel 0x7f0a008f
int id contiguous 0x7f0a0090
int id coordinator 0x7f0a0091
int id coringImage 0x7f0a0092
int id cos 0x7f0a0093
int id counterclockwise 0x7f0a0094
int id cradle 0x7f0a0095
int id custom 0x7f0a0096
int id customPanel 0x7f0a0097
int id cut 0x7f0a0098
int id dark 0x7f0a0099
int id date_picker_actions 0x7f0a009a
int id decelerate 0x7f0a009b
int id decelerateAndComplete 0x7f0a009c
int id decor_content_parent 0x7f0a009d
int id deep_blue 0x7f0a009e
int id deep_green 0x7f0a009f
int id deep_orange 0x7f0a00a0
int id deep_pink 0x7f0a00a1
int id deep_purple 0x7f0a00a2
int id default_activity_button 0x7f0a00a3
int id deltaRelative 0x7f0a00a4
int id design_bottom_sheet 0x7f0a00a5
int id design_menu_item_action_area 0x7f0a00a6
int id design_menu_item_action_area_stub 0x7f0a00a7
int id design_menu_item_text 0x7f0a00a8
int id design_navigation_view 0x7f0a00a9
int id dialog_button 0x7f0a00aa
int id dimensions 0x7f0a00ab
int id direct 0x7f0a00ac
int id disableHome 0x7f0a00ad
int id disablePostScroll 0x7f0a00ae
int id disableScroll 0x7f0a00af
int id disjoint 0x7f0a00b0
int id dragDown 0x7f0a00b1
int id dragEnd 0x7f0a00b2
int id dragLeft 0x7f0a00b3
int id dragRight 0x7f0a00b4
int id dragStart 0x7f0a00b5
int id dragUp 0x7f0a00b6
int id dropdown_menu 0x7f0a00b7
int id easeIn 0x7f0a00b8
int id easeInOut 0x7f0a00b9
int id easeOut 0x7f0a00ba
int id edge 0x7f0a00bb
int id edit_query 0x7f0a00bc
int id edit_text_id 0x7f0a00bd
int id elastic 0x7f0a00be
int id embed 0x7f0a00bf
int id end 0x7f0a00c0
int id endToStart 0x7f0a00c1
int id end_padder 0x7f0a00c2
int id enterAlways 0x7f0a00c3
int id enterAlwaysCollapsed 0x7f0a00c4
int id escape 0x7f0a00c5
int id exitUntilCollapsed 0x7f0a00c6
int id expand_activities_button 0x7f0a00c7
int id expanded_menu 0x7f0a00c8
int id fade 0x7f0a00c9
int id favorites 0x7f0a00ca
int id fill 0x7f0a00cb
int id fill_horizontal 0x7f0a00cc
int id fill_vertical 0x7f0a00cd
int id filled 0x7f0a00ce
int id fitCenter 0x7f0a00cf
int id fitEnd 0x7f0a00d0
int id fitStart 0x7f0a00d1
int id fitToContents 0x7f0a00d2
int id fitXY 0x7f0a00d3
int id fixed 0x7f0a00d4
int id flCentral 0x7f0a00d5
int id flip 0x7f0a00d6
int id floating 0x7f0a00d7
int id forever 0x7f0a00d8
int id fragment_container_view_tag 0x7f0a00d9
int id fullscreen_header 0x7f0a00da
int id gallery 0x7f0a00db
int id ghost_view 0x7f0a00dc
int id ghost_view_holder 0x7f0a00dd
int id gone 0x7f0a00de
int id graph 0x7f0a00df
int id graph_wrap 0x7f0a00e0
int id gray 0x7f0a00e1
int id gridView 0x7f0a00e2
int id group_divider 0x7f0a00e3
int id groups 0x7f0a00e4
int id header_title 0x7f0a00e5
int id hide_ime_id 0x7f0a00e6
int id hideable 0x7f0a00e7
int id home 0x7f0a00e8
int id homeAsUp 0x7f0a00e9
int id honorRequest 0x7f0a00ea
int id icon 0x7f0a00eb
int id icon_frame 0x7f0a00ec
int id icon_group 0x7f0a00ed
int id icon_only 0x7f0a00ee
int id ifRoom 0x7f0a00ef
int id ignore 0x7f0a00f0
int id ignoreRequest 0x7f0a00f1
int id image 0x7f0a00f2
int id imageView 0x7f0a00f3
int id image_preview 0x7f0a00f4
int id imgLogo 0x7f0a00f5
int id indeterminate 0x7f0a00f6
int id info 0x7f0a00f7
int id invisible 0x7f0a00f8
int id inward 0x7f0a00f9
int id italic 0x7f0a00fa
int id item_touch_helper_previous_elevation 0x7f0a00fb
int id jumpToEnd 0x7f0a00fc
int id jumpToStart 0x7f0a00fd
int id labeled 0x7f0a00fe
int id layout 0x7f0a00ff
int id left 0x7f0a0100
int id leftToRight 0x7f0a0101
int id legacy 0x7f0a0102
int id light 0x7f0a0103
int id light_blue 0x7f0a0104
int id light_green 0x7f0a0105
int id light_orange 0x7f0a0106
int id light_pink 0x7f0a0107
int id light_purple 0x7f0a0108
int id line1 0x7f0a0109
int id line3 0x7f0a010a
int id linear 0x7f0a010b
int id listMode 0x7f0a010c
int id list_item 0x7f0a010d
int id locale 0x7f0a010e
int id logo 0x7f0a010f
int id ltr 0x7f0a0110
int id m3_side_sheet 0x7f0a0111
int id mainmenu 0x7f0a0112
int id marquee 0x7f0a0113
int id masked 0x7f0a0114
int id match_parent 0x7f0a0115
int id material_clock_display 0x7f0a0116
int id material_clock_display_and_toggle 0x7f0a0117
int id material_clock_face 0x7f0a0118
int id material_clock_hand 0x7f0a0119
int id material_clock_level 0x7f0a011a
int id material_clock_period_am_button 0x7f0a011b
int id material_clock_period_pm_button 0x7f0a011c
int id material_clock_period_toggle 0x7f0a011d
int id material_hour_text_input 0x7f0a011e
int id material_hour_tv 0x7f0a011f
int id material_label 0x7f0a0120
int id material_minute_text_input 0x7f0a0121
int id material_minute_tv 0x7f0a0122
int id material_textinput_timepicker 0x7f0a0123
int id material_timepicker_cancel_button 0x7f0a0124
int id material_timepicker_container 0x7f0a0125
int id material_timepicker_mode_button 0x7f0a0126
int id material_timepicker_ok_button 0x7f0a0127
int id material_timepicker_view 0x7f0a0128
int id material_value_index 0x7f0a0129
int id matrix 0x7f0a012a
int id media_actions 0x7f0a012b
int id message 0x7f0a012c
int id middle 0x7f0a012d
int id mini 0x7f0a012e
int id month_grid 0x7f0a012f
int id month_navigation_bar 0x7f0a0130
int id month_navigation_fragment_toggle 0x7f0a0131
int id month_navigation_next 0x7f0a0132
int id month_navigation_previous 0x7f0a0133
int id month_title 0x7f0a0134
int id motion_base 0x7f0a0135
int id mtrl_anchor_parent 0x7f0a0136
int id mtrl_calendar_day_selector_frame 0x7f0a0137
int id mtrl_calendar_days_of_week 0x7f0a0138
int id mtrl_calendar_frame 0x7f0a0139
int id mtrl_calendar_main_pane 0x7f0a013a
int id mtrl_calendar_months 0x7f0a013b
int id mtrl_calendar_selection_frame 0x7f0a013c
int id mtrl_calendar_text_input_frame 0x7f0a013d
int id mtrl_calendar_year_selector_frame 0x7f0a013e
int id mtrl_card_checked_layer_id 0x7f0a013f
int id mtrl_child_content_container 0x7f0a0140
int id mtrl_internal_children_alpha_tag 0x7f0a0141
int id mtrl_motion_snapshot_view 0x7f0a0142
int id mtrl_picker_fullscreen 0x7f0a0143
int id mtrl_picker_header 0x7f0a0144
int id mtrl_picker_header_selection_text 0x7f0a0145
int id mtrl_picker_header_title_and_selection 0x7f0a0146
int id mtrl_picker_header_toggle 0x7f0a0147
int id mtrl_picker_text_input_date 0x7f0a0148
int id mtrl_picker_text_input_range_end 0x7f0a0149
int id mtrl_picker_text_input_range_start 0x7f0a014a
int id mtrl_picker_title_text 0x7f0a014b
int id mtrl_view_tag_bottom_padding 0x7f0a014c
int id multiply 0x7f0a014d
int id nav_logo 0x7f0a014e
int id navigationView 0x7f0a014f
int id navigation_bar_item_active_indicator_view 0x7f0a0150
int id navigation_bar_item_icon_container 0x7f0a0151
int id navigation_bar_item_icon_view 0x7f0a0152
int id navigation_bar_item_labels_group 0x7f0a0153
int id navigation_bar_item_large_label_view 0x7f0a0154
int id navigation_bar_item_small_label_view 0x7f0a0155
int id navigation_header_container 0x7f0a0156
int id never 0x7f0a0157
int id noScroll 0x7f0a0158
int id none 0x7f0a0159
int id normal 0x7f0a015a
int id notification_background 0x7f0a015b
int id notification_main_column 0x7f0a015c
int id notification_main_column_container 0x7f0a015d
int id off 0x7f0a015e
int id on 0x7f0a015f
int id open_search_bar_text_view 0x7f0a0160
int id open_search_view_background 0x7f0a0161
int id open_search_view_clear_button 0x7f0a0162
int id open_search_view_content_container 0x7f0a0163
int id open_search_view_divider 0x7f0a0164
int id open_search_view_dummy_toolbar 0x7f0a0165
int id open_search_view_edit_text 0x7f0a0166
int id open_search_view_header_container 0x7f0a0167
int id open_search_view_root 0x7f0a0168
int id open_search_view_scrim 0x7f0a0169
int id open_search_view_search_prefix 0x7f0a016a
int id open_search_view_status_bar_spacer 0x7f0a016b
int id open_search_view_toolbar 0x7f0a016c
int id open_search_view_toolbar_container 0x7f0a016d
int id os_bgimage_notif_bgimage 0x7f0a016e
int id os_bgimage_notif_bgimage_align_layout 0x7f0a016f
int id os_bgimage_notif_bgimage_right_aligned 0x7f0a0170
int id os_bgimage_notif_body 0x7f0a0171
int id os_bgimage_notif_title 0x7f0a0172
int id outline 0x7f0a0173
int id outward 0x7f0a0174
int id packed 0x7f0a0175
int id parallax 0x7f0a0176
int id parent 0x7f0a0177
int id parentPanel 0x7f0a0178
int id parentRelative 0x7f0a0179
int id parent_matrix 0x7f0a017a
int id password_toggle 0x7f0a017b
int id path 0x7f0a017c
int id pathRelative 0x7f0a017d
int id peekHeight 0x7f0a017e
int id percent 0x7f0a017f
int id pin 0x7f0a0180
int id plain 0x7f0a0181
int id position 0x7f0a0182
int id postLayout 0x7f0a0183
int id preferences_detail 0x7f0a0184
int id preferences_header 0x7f0a0185
int id preferences_sliding_pane_layout 0x7f0a0186
int id pressed 0x7f0a0187
int id privacyPolicy 0x7f0a0188
int id progress_circular 0x7f0a0189
int id progress_horizontal 0x7f0a018a
int id radio 0x7f0a018b
int id rate 0x7f0a018c
int id ratio 0x7f0a018d
int id rectangles 0x7f0a018e
int id recycler_view 0x7f0a018f
int id red 0x7f0a0190
int id relative_layout 0x7f0a0191
int id report_drawn 0x7f0a0192
int id reverseSawtooth 0x7f0a0193
int id rfParent 0x7f0a0194
int id right 0x7f0a0195
int id rightToLeft 0x7f0a0196
int id right_icon 0x7f0a0197
int id right_side 0x7f0a0198
int id rlColor 0x7f0a0199
int id rounded 0x7f0a019a
int id row_index_key 0x7f0a019b
int id rtl 0x7f0a019c
int id save_non_transition_alpha 0x7f0a019d
int id save_overlay_view 0x7f0a019e
int id sawtooth 0x7f0a019f
int id scale 0x7f0a01a0
int id screen 0x7f0a01a1
int id scroll 0x7f0a01a2
int id scrollIndicatorDown 0x7f0a01a3
int id scrollIndicatorUp 0x7f0a01a4
int id scrollView 0x7f0a01a5
int id scrollable 0x7f0a01a6
int id search_badge 0x7f0a01a7
int id search_bar 0x7f0a01a8
int id search_button 0x7f0a01a9
int id search_close_btn 0x7f0a01aa
int id search_edit_frame 0x7f0a01ab
int id search_go_btn 0x7f0a01ac
int id search_mag_icon 0x7f0a01ad
int id search_plate 0x7f0a01ae
int id search_src_text 0x7f0a01af
int id search_voice_btn 0x7f0a01b0
int id seekbar 0x7f0a01b1
int id seekbar_value 0x7f0a01b2
int id select_color 0x7f0a01b3
int id select_dialog_listview 0x7f0a01b4
int id selected 0x7f0a01b5
int id selection_type 0x7f0a01b6
int id settings 0x7f0a01b7
int id share 0x7f0a01b8
int id shortcut 0x7f0a01b9
int id showCustom 0x7f0a01ba
int id showHome 0x7f0a01bb
int id showTitle 0x7f0a01bc
int id sin 0x7f0a01bd
int id skipCollapsed 0x7f0a01be
int id slide 0x7f0a01bf
int id snackbar_action 0x7f0a01c0
int id snackbar_text 0x7f0a01c1
int id snap 0x7f0a01c2
int id snapMargins 0x7f0a01c3
int id spacer 0x7f0a01c4
int id special_effects_controller_view_tag 0x7f0a01c5
int id spikes 0x7f0a01c6
int id spinner 0x7f0a01c7
int id spline 0x7f0a01c8
int id split_action_bar 0x7f0a01c9
int id spread 0x7f0a01ca
int id spread_inside 0x7f0a01cb
int id square 0x7f0a01cc
int id squares 0x7f0a01cd
int id src_atop 0x7f0a01ce
int id src_in 0x7f0a01cf
int id src_over 0x7f0a01d0
int id standard 0x7f0a01d1
int id start 0x7f0a01d2
int id startHorizontal 0x7f0a01d3
int id startToEnd 0x7f0a01d4
int id startVertical 0x7f0a01d5
int id staticLayout 0x7f0a01d6
int id staticPostLayout 0x7f0a01d7
int id status_bar_latest_event_content 0x7f0a01d8
int id stop 0x7f0a01d9
int id stretch 0x7f0a01da
int id submenuarrow 0x7f0a01db
int id submit_area 0x7f0a01dc
int id support 0x7f0a01dd
int id switchWidget 0x7f0a01de
int id tabMode 0x7f0a01df
int id tag_accessibility_actions 0x7f0a01e0
int id tag_accessibility_clickable_spans 0x7f0a01e1
int id tag_accessibility_heading 0x7f0a01e2
int id tag_accessibility_pane_title 0x7f0a01e3
int id tag_on_apply_window_listener 0x7f0a01e4
int id tag_on_receive_content_listener 0x7f0a01e5
int id tag_on_receive_content_mime_types 0x7f0a01e6
int id tag_screen_reader_focusable 0x7f0a01e7
int id tag_state_description 0x7f0a01e8
int id tag_transition_group 0x7f0a01e9
int id tag_unhandled_key_event_manager 0x7f0a01ea
int id tag_unhandled_key_listeners 0x7f0a01eb
int id tag_window_insets_animation_callback 0x7f0a01ec
int id text 0x7f0a01ed
int id text2 0x7f0a01ee
int id textEnd 0x7f0a01ef
int id textSpacerNoButtons 0x7f0a01f0
int id textSpacerNoTitle 0x7f0a01f1
int id textStart 0x7f0a01f2
int id textTop 0x7f0a01f3
int id text_card_name1 0x7f0a01f4
int id text_card_name2 0x7f0a01f5
int id text_card_name3 0x7f0a01f6
int id text_card_name4 0x7f0a01f7
int id text_card_name5 0x7f0a01f8
int id text_card_name6 0x7f0a01f9
int id text_input_end_icon 0x7f0a01fa
int id text_input_error_icon 0x7f0a01fb
int id text_input_start_icon 0x7f0a01fc
int id textinput_counter 0x7f0a01fd
int id textinput_error 0x7f0a01fe
int id textinput_helper_text 0x7f0a01ff
int id textinput_placeholder 0x7f0a0200
int id textinput_prefix_text 0x7f0a0201
int id textinput_suffix_text 0x7f0a0202
int id time 0x7f0a0203
int id title 0x7f0a0204
int id titleDividerNoCustom 0x7f0a0205
int id title_template 0x7f0a0206
int id toggle 0x7f0a0207
int id toolbar 0x7f0a0208
int id top 0x7f0a0209
int id topPanel 0x7f0a020a
int id touch_outside 0x7f0a020b
int id transitionToEnd 0x7f0a020c
int id transitionToStart 0x7f0a020d
int id transition_clip 0x7f0a020e
int id transition_current_scene 0x7f0a020f
int id transition_image_transform 0x7f0a0210
int id transition_layout_save 0x7f0a0211
int id transition_pause_alpha 0x7f0a0212
int id transition_position 0x7f0a0213
int id transition_scene_layoutid_cache 0x7f0a0214
int id transition_transform 0x7f0a0215
int id triangle 0x7f0a0216
int id txtTitle 0x7f0a0217
int id unchecked 0x7f0a0218
int id uniform 0x7f0a0219
int id unlabeled 0x7f0a021a
int id up 0x7f0a021b
int id useLogo 0x7f0a021c
int id view_offset_helper 0x7f0a021d
int id view_tree_lifecycle_owner 0x7f0a021e
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0a021f
int id view_tree_saved_state_registry_owner 0x7f0a0220
int id view_tree_view_model_store_owner 0x7f0a0221
int id visible 0x7f0a0222
int id visible_removing_fragment_view_tag 0x7f0a0223
int id waves 0x7f0a0224
int id white 0x7f0a0225
int id wide 0x7f0a0226
int id withText 0x7f0a0227
int id with_icon 0x7f0a0228
int id withinBounds 0x7f0a0229
int id wrap 0x7f0a022a
int id wrap_content 0x7f0a022b
int id yellow 0x7f0a022c
int integer abc_config_activityDefaultDur 0x7f0b0000
int integer abc_config_activityShortDur 0x7f0b0001
int integer app_bar_elevation_anim_duration 0x7f0b0002
int integer bottom_sheet_slide_duration 0x7f0b0003
int integer cancel_button_image_alpha 0x7f0b0004
int integer config_tooltipAnimTime 0x7f0b0005
int integer design_snackbar_text_max_lines 0x7f0b0006
int integer design_tab_indicator_anim_duration_ms 0x7f0b0007
int integer fillDuration 0x7f0b0008
int integer google_play_services_version 0x7f0b0009
int integer hide_password_duration 0x7f0b000a
int integer m3_badge_max_number 0x7f0b000b
int integer m3_btn_anim_delay_ms 0x7f0b000c
int integer m3_btn_anim_duration_ms 0x7f0b000d
int integer m3_card_anim_delay_ms 0x7f0b000e
int integer m3_card_anim_duration_ms 0x7f0b000f
int integer m3_chip_anim_duration 0x7f0b0010
int integer m3_sys_motion_duration_extra_long1 0x7f0b0011
int integer m3_sys_motion_duration_extra_long2 0x7f0b0012
int integer m3_sys_motion_duration_extra_long3 0x7f0b0013
int integer m3_sys_motion_duration_extra_long4 0x7f0b0014
int integer m3_sys_motion_duration_long1 0x7f0b0015
int integer m3_sys_motion_duration_long2 0x7f0b0016
int integer m3_sys_motion_duration_long3 0x7f0b0017
int integer m3_sys_motion_duration_long4 0x7f0b0018
int integer m3_sys_motion_duration_medium1 0x7f0b0019
int integer m3_sys_motion_duration_medium2 0x7f0b001a
int integer m3_sys_motion_duration_medium3 0x7f0b001b
int integer m3_sys_motion_duration_medium4 0x7f0b001c
int integer m3_sys_motion_duration_short1 0x7f0b001d
int integer m3_sys_motion_duration_short2 0x7f0b001e
int integer m3_sys_motion_duration_short3 0x7f0b001f
int integer m3_sys_motion_duration_short4 0x7f0b0020
int integer m3_sys_motion_path 0x7f0b0021
int integer m3_sys_shape_corner_extra_large_corner_family 0x7f0b0022
int integer m3_sys_shape_corner_extra_small_corner_family 0x7f0b0023
int integer m3_sys_shape_corner_full_corner_family 0x7f0b0024
int integer m3_sys_shape_corner_large_corner_family 0x7f0b0025
int integer m3_sys_shape_corner_medium_corner_family 0x7f0b0026
int integer m3_sys_shape_corner_small_corner_family 0x7f0b0027
int integer material_motion_duration_long_1 0x7f0b0028
int integer material_motion_duration_long_2 0x7f0b0029
int integer material_motion_duration_medium_1 0x7f0b002a
int integer material_motion_duration_medium_2 0x7f0b002b
int integer material_motion_duration_short_1 0x7f0b002c
int integer material_motion_duration_short_2 0x7f0b002d
int integer material_motion_path 0x7f0b002e
int integer mtrl_badge_max_character_count 0x7f0b002f
int integer mtrl_btn_anim_delay_ms 0x7f0b0030
int integer mtrl_btn_anim_duration_ms 0x7f0b0031
int integer mtrl_calendar_header_orientation 0x7f0b0032
int integer mtrl_calendar_selection_text_lines 0x7f0b0033
int integer mtrl_calendar_year_selector_span 0x7f0b0034
int integer mtrl_card_anim_delay_ms 0x7f0b0035
int integer mtrl_card_anim_duration_ms 0x7f0b0036
int integer mtrl_chip_anim_duration 0x7f0b0037
int integer mtrl_switch_thumb_motion_duration 0x7f0b0038
int integer mtrl_switch_thumb_post_morphing_duration 0x7f0b0039
int integer mtrl_switch_thumb_pre_morphing_duration 0x7f0b003a
int integer mtrl_switch_thumb_pressed_duration 0x7f0b003b
int integer mtrl_switch_thumb_viewport_center_coordinate 0x7f0b003c
int integer mtrl_switch_thumb_viewport_size 0x7f0b003d
int integer mtrl_switch_track_viewport_height 0x7f0b003e
int integer mtrl_switch_track_viewport_width 0x7f0b003f
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0b0040
int integer mtrl_view_gone 0x7f0b0041
int integer mtrl_view_invisible 0x7f0b0042
int integer mtrl_view_visible 0x7f0b0043
int integer preferences_detail_pane_weight 0x7f0b0044
int integer preferences_header_pane_weight 0x7f0b0045
int integer show_password_duration 0x7f0b0046
int integer status_bar_notification_info_maxnum 0x7f0b0047
int integer strokeDrawingDuration 0x7f0b0048
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0c0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0c0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0c0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0c0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0c0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0c0005
int interpolator fast_out_slow_in 0x7f0c0006
int interpolator m3_sys_motion_easing_emphasized 0x7f0c0007
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x7f0c0008
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x7f0c0009
int interpolator m3_sys_motion_easing_linear 0x7f0c000a
int interpolator m3_sys_motion_easing_standard 0x7f0c000b
int interpolator m3_sys_motion_easing_standard_accelerate 0x7f0c000c
int interpolator m3_sys_motion_easing_standard_decelerate 0x7f0c000d
int interpolator mtrl_fast_out_linear_in 0x7f0c000e
int interpolator mtrl_fast_out_slow_in 0x7f0c000f
int interpolator mtrl_linear 0x7f0c0010
int interpolator mtrl_linear_out_slow_in 0x7f0c0011
int layout abc_action_bar_title_item 0x7f0d0000
int layout abc_action_bar_up_container 0x7f0d0001
int layout abc_action_menu_item_layout 0x7f0d0002
int layout abc_action_menu_layout 0x7f0d0003
int layout abc_action_mode_bar 0x7f0d0004
int layout abc_action_mode_close_item_material 0x7f0d0005
int layout abc_activity_chooser_view 0x7f0d0006
int layout abc_activity_chooser_view_list_item 0x7f0d0007
int layout abc_alert_dialog_button_bar_material 0x7f0d0008
int layout abc_alert_dialog_material 0x7f0d0009
int layout abc_alert_dialog_title_material 0x7f0d000a
int layout abc_cascading_menu_item_layout 0x7f0d000b
int layout abc_dialog_title_material 0x7f0d000c
int layout abc_expanded_menu_layout 0x7f0d000d
int layout abc_list_menu_item_checkbox 0x7f0d000e
int layout abc_list_menu_item_icon 0x7f0d000f
int layout abc_list_menu_item_layout 0x7f0d0010
int layout abc_list_menu_item_radio 0x7f0d0011
int layout abc_popup_menu_header_item_layout 0x7f0d0012
int layout abc_popup_menu_item_layout 0x7f0d0013
int layout abc_screen_content_include 0x7f0d0014
int layout abc_screen_simple 0x7f0d0015
int layout abc_screen_simple_overlay_action_mode 0x7f0d0016
int layout abc_screen_toolbar 0x7f0d0017
int layout abc_search_dropdown_item_icons_2line 0x7f0d0018
int layout abc_search_view 0x7f0d0019
int layout abc_select_dialog_material 0x7f0d001a
int layout abc_tooltip 0x7f0d001b
int layout activity_base 0x7f0d001c
int layout activity_categories 0x7f0d001d
int layout activity_category_items 0x7f0d001e
int layout activity_main 0x7f0d001f
int layout activity_main_lib 0x7f0d0020
int layout activity_splash 0x7f0d0021
int layout admob_empty_layout 0x7f0d0022
int layout browser_actions_context_menu_page 0x7f0d0023
int layout browser_actions_context_menu_row 0x7f0d0024
int layout color_edit 0x7f0d0025
int layout color_preview 0x7f0d0026
int layout color_selector 0x7f0d0027
int layout color_widget 0x7f0d0028
int layout custom_dialog 0x7f0d0029
int layout design_bottom_navigation_item 0x7f0d002a
int layout design_bottom_sheet_dialog 0x7f0d002b
int layout design_layout_snackbar 0x7f0d002c
int layout design_layout_snackbar_include 0x7f0d002d
int layout design_layout_tab_icon 0x7f0d002e
int layout design_layout_tab_text 0x7f0d002f
int layout design_menu_item_action_area 0x7f0d0030
int layout design_navigation_item 0x7f0d0031
int layout design_navigation_item_header 0x7f0d0032
int layout design_navigation_item_separator 0x7f0d0033
int layout design_navigation_item_subheader 0x7f0d0034
int layout design_navigation_menu 0x7f0d0035
int layout design_navigation_menu_item 0x7f0d0036
int layout design_text_input_end_icon 0x7f0d0037
int layout design_text_input_start_icon 0x7f0d0038
int layout expand_button 0x7f0d0039
int layout grid_item_layout 0x7f0d003a
int layout image_frame 0x7f0d003b
int layout ime_base_split_test_activity 0x7f0d003c
int layout ime_secondary_split_test_activity 0x7f0d003d
int layout m3_alert_dialog 0x7f0d003e
int layout m3_alert_dialog_actions 0x7f0d003f
int layout m3_alert_dialog_title 0x7f0d0040
int layout m3_auto_complete_simple_item 0x7f0d0041
int layout m3_side_sheet_dialog 0x7f0d0042
int layout material_chip_input_combo 0x7f0d0043
int layout material_clock_display 0x7f0d0044
int layout material_clock_display_divider 0x7f0d0045
int layout material_clock_period_toggle 0x7f0d0046
int layout material_clock_period_toggle_land 0x7f0d0047
int layout material_clockface_textview 0x7f0d0048
int layout material_clockface_view 0x7f0d0049
int layout material_radial_view_group 0x7f0d004a
int layout material_textinput_timepicker 0x7f0d004b
int layout material_time_chip 0x7f0d004c
int layout material_time_input 0x7f0d004d
int layout material_timepicker 0x7f0d004e
int layout material_timepicker_dialog 0x7f0d004f
int layout material_timepicker_textinput_display 0x7f0d0050
int layout mtrl_alert_dialog 0x7f0d0051
int layout mtrl_alert_dialog_actions 0x7f0d0052
int layout mtrl_alert_dialog_title 0x7f0d0053
int layout mtrl_alert_select_dialog_item 0x7f0d0054
int layout mtrl_alert_select_dialog_multichoice 0x7f0d0055
int layout mtrl_alert_select_dialog_singlechoice 0x7f0d0056
int layout mtrl_auto_complete_simple_item 0x7f0d0057
int layout mtrl_calendar_day 0x7f0d0058
int layout mtrl_calendar_day_of_week 0x7f0d0059
int layout mtrl_calendar_days_of_week 0x7f0d005a
int layout mtrl_calendar_horizontal 0x7f0d005b
int layout mtrl_calendar_month 0x7f0d005c
int layout mtrl_calendar_month_labeled 0x7f0d005d
int layout mtrl_calendar_month_navigation 0x7f0d005e
int layout mtrl_calendar_months 0x7f0d005f
int layout mtrl_calendar_vertical 0x7f0d0060
int layout mtrl_calendar_year 0x7f0d0061
int layout mtrl_layout_snackbar 0x7f0d0062
int layout mtrl_layout_snackbar_include 0x7f0d0063
int layout mtrl_navigation_rail_item 0x7f0d0064
int layout mtrl_picker_actions 0x7f0d0065
int layout mtrl_picker_dialog 0x7f0d0066
int layout mtrl_picker_fullscreen 0x7f0d0067
int layout mtrl_picker_header_dialog 0x7f0d0068
int layout mtrl_picker_header_fullscreen 0x7f0d0069
int layout mtrl_picker_header_selection_text 0x7f0d006a
int layout mtrl_picker_header_title_text 0x7f0d006b
int layout mtrl_picker_header_toggle 0x7f0d006c
int layout mtrl_picker_text_input_date 0x7f0d006d
int layout mtrl_picker_text_input_date_range 0x7f0d006e
int layout mtrl_search_bar 0x7f0d006f
int layout mtrl_search_view 0x7f0d0070
int layout nav_header_drawer 0x7f0d0071
int layout notification_action 0x7f0d0072
int layout notification_action_tombstone 0x7f0d0073
int layout notification_media_action 0x7f0d0074
int layout notification_media_cancel_action 0x7f0d0075
int layout notification_template_big_media 0x7f0d0076
int layout notification_template_big_media_custom 0x7f0d0077
int layout notification_template_big_media_narrow 0x7f0d0078
int layout notification_template_big_media_narrow_custom 0x7f0d0079
int layout notification_template_custom_big 0x7f0d007a
int layout notification_template_icon_group 0x7f0d007b
int layout notification_template_lines_media 0x7f0d007c
int layout notification_template_media 0x7f0d007d
int layout notification_template_media_custom 0x7f0d007e
int layout notification_template_part_chronometer 0x7f0d007f
int layout notification_template_part_time 0x7f0d0080
int layout onesignal_bgimage_notif_layout 0x7f0d0081
int layout preference 0x7f0d0082
int layout preference_category 0x7f0d0083
int layout preference_category_material 0x7f0d0084
int layout preference_dialog_edittext 0x7f0d0085
int layout preference_dropdown 0x7f0d0086
int layout preference_dropdown_material 0x7f0d0087
int layout preference_information 0x7f0d0088
int layout preference_information_material 0x7f0d0089
int layout preference_list_fragment 0x7f0d008a
int layout preference_material 0x7f0d008b
int layout preference_recyclerview 0x7f0d008c
int layout preference_widget_checkbox 0x7f0d008d
int layout preference_widget_seekbar 0x7f0d008e
int layout preference_widget_seekbar_material 0x7f0d008f
int layout preference_widget_switch 0x7f0d0090
int layout preference_widget_switch_compat 0x7f0d0091
int layout premium_nav_header 0x7f0d0092
int layout select_dialog_item_material 0x7f0d0093
int layout select_dialog_multichoice_material 0x7f0d0094
int layout select_dialog_singlechoice_material 0x7f0d0095
int layout support_simple_spinner_dropdown_item 0x7f0d0096
int menu activity_nav_drawer 0x7f0f0000
int menu menu_main 0x7f0f0001
int menu premium_nav_drawer 0x7f0f0002
int menu share_save_menu 0x7f0f0003
int mipmap ic_launcher 0x7f100000
int mipmap ic_launcher_foreground 0x7f100001
int mipmap ic_launcher_round 0x7f100002
int plurals mtrl_badge_content_description 0x7f110000
int raw background_1 0x7f120000
int raw background_2 0x7f120001
int raw background_3 0x7f120002
int raw background_4 0x7f120003
int raw background_5 0x7f120004
int raw background_6 0x7f120005
int raw click 0x7f120006
int raw consumer_onesignal_keep 0x7f120007
int raw firebase_common_keep 0x7f120008
int string Share_The_App 0x7f130000
int string abc_action_bar_home_description 0x7f130001
int string abc_action_bar_up_description 0x7f130002
int string abc_action_menu_overflow_description 0x7f130003
int string abc_action_mode_done 0x7f130004
int string abc_activity_chooser_view_see_all 0x7f130005
int string abc_activitychooserview_choose_application 0x7f130006
int string abc_capital_off 0x7f130007
int string abc_capital_on 0x7f130008
int string abc_menu_alt_shortcut_label 0x7f130009
int string abc_menu_ctrl_shortcut_label 0x7f13000a
int string abc_menu_delete_shortcut_label 0x7f13000b
int string abc_menu_enter_shortcut_label 0x7f13000c
int string abc_menu_function_shortcut_label 0x7f13000d
int string abc_menu_meta_shortcut_label 0x7f13000e
int string abc_menu_shift_shortcut_label 0x7f13000f
int string abc_menu_space_shortcut_label 0x7f130010
int string abc_menu_sym_shortcut_label 0x7f130011
int string abc_prepend_shortcut_label 0x7f130012
int string abc_search_hint 0x7f130013
int string abc_searchview_description_clear 0x7f130014
int string abc_searchview_description_query 0x7f130015
int string abc_searchview_description_search 0x7f130016
int string abc_searchview_description_submit 0x7f130017
int string abc_searchview_description_voice 0x7f130018
int string abc_shareactionprovider_share_with 0x7f130019
int string abc_shareactionprovider_share_with_application 0x7f13001a
int string abc_toolbar_collapse_description 0x7f13001b
int string about 0x7f13001c
int string about_text 0x7f13001d
int string action_mute 0x7f13001e
int string action_remove_ads 0x7f13001f
int string action_save 0x7f130020
int string action_settings 0x7f130021
int string action_share 0x7f130022
int string action_undo 0x7f130023
int string ads 0x7f130024
int string androidx_startup 0x7f130025
int string animals 0x7f130026
int string appId 0x7f130027
int string app_name 0x7f130028
int string app_open_ad_unit_id 0x7f130029
int string appbar_scrolling_view_behavior 0x7f13002a
int string banner_unit_id 0x7f13002b
int string bottom_sheet_behavior 0x7f13002c
int string bottomsheet_action_collapse 0x7f13002d
int string bottomsheet_action_expand 0x7f13002e
int string bottomsheet_action_expand_halfway 0x7f13002f
int string bottomsheet_drag_handle_clicked 0x7f130030
int string bottomsheet_drag_handle_content_description 0x7f130031
int string call_notification_answer_action 0x7f130032
int string call_notification_answer_video_action 0x7f130033
int string call_notification_decline_action 0x7f130034
int string call_notification_hang_up_action 0x7f130035
int string call_notification_incoming_text 0x7f130036
int string call_notification_ongoing_text 0x7f130037
int string call_notification_screening_text 0x7f130038
int string cancel 0x7f130039
int string cartoons 0x7f13003a
int string character_counter_content_description 0x7f13003b
int string character_counter_overflowed_content_description 0x7f13003c
int string character_counter_pattern 0x7f13003d
int string chooseColor 0x7f13003e
int string clear_text_end_icon_content_description 0x7f13003f
int string common_google_play_services_enable_button 0x7f130040
int string common_google_play_services_enable_text 0x7f130041
int string common_google_play_services_enable_title 0x7f130042
int string common_google_play_services_install_button 0x7f130043
int string common_google_play_services_install_text 0x7f130044
int string common_google_play_services_install_title 0x7f130045
int string common_google_play_services_notification_channel_name 0x7f130046
int string common_google_play_services_notification_ticker 0x7f130047
int string common_google_play_services_unknown_issue 0x7f130048
int string common_google_play_services_unsupported_text 0x7f130049
int string common_google_play_services_update_button 0x7f13004a
int string common_google_play_services_update_text 0x7f13004b
int string common_google_play_services_update_title 0x7f13004c
int string common_google_play_services_updating_text 0x7f13004d
int string common_google_play_services_wear_update_text 0x7f13004e
int string common_open_on_phone 0x7f13004f
int string common_signin_button_text 0x7f130050
int string common_signin_button_text_long 0x7f130051
int string copy 0x7f130052
int string copy_toast_msg 0x7f130053
int string dialog_close 0x7f130054
int string error_a11y_label 0x7f130055
int string error_icon_content_description 0x7f130056
int string eu_consent_change_setting 0x7f130057
int string eu_consent_no 0x7f130058
int string eu_consent_question 0x7f130059
int string eu_consent_text 0x7f13005a
int string eu_consent_yes 0x7f13005b
int string expand_button_title 0x7f13005c
int string exposed_dropdown_menu_content_description 0x7f13005d
int string fab_transformation_scrim_behavior 0x7f13005e
int string fab_transformation_sheet_behavior 0x7f13005f
int string fallback_menu_item_copy_link 0x7f130060
int string fallback_menu_item_open_in_browser 0x7f130061
int string fallback_menu_item_share_link 0x7f130062
int string fcm_fallback_notification_channel_label 0x7f130063
int string flowers 0x7f130064
int string foods 0x7f130065
int string gcm_defaultSenderId 0x7f130066
int string gdpr_privacypolicy 0x7f130067
int string google_api_key 0x7f130068
int string google_app_id 0x7f130069
int string google_crash_reporting_api_key 0x7f13006a
int string google_partners 0x7f13006b
int string google_storage_bucket 0x7f13006c
int string hello_world 0x7f13006d
int string hide_bottom_view_on_scroll_behavior 0x7f13006e
int string icon_content_description 0x7f13006f
int string interstitial_unit_id 0x7f130070
int string item_view_role_description 0x7f130071
int string learn_more 0x7f130072
int string location_permission_missing_message 0x7f130073
int string location_permission_missing_title 0x7f130074
int string location_permission_name_for_title 0x7f130075
int string location_permission_settings_message 0x7f130076
int string m3_exceed_max_badge_text_suffix 0x7f130077
int string m3_ref_typeface_brand_medium 0x7f130078
int string m3_ref_typeface_brand_regular 0x7f130079
int string m3_ref_typeface_plain_medium 0x7f13007a
int string m3_ref_typeface_plain_regular 0x7f13007b
int string m3_sys_motion_easing_emphasized 0x7f13007c
int string m3_sys_motion_easing_emphasized_accelerate 0x7f13007d
int string m3_sys_motion_easing_emphasized_decelerate 0x7f13007e
int string m3_sys_motion_easing_emphasized_path_data 0x7f13007f
int string m3_sys_motion_easing_legacy 0x7f130080
int string m3_sys_motion_easing_legacy_accelerate 0x7f130081
int string m3_sys_motion_easing_legacy_decelerate 0x7f130082
int string m3_sys_motion_easing_linear 0x7f130083
int string m3_sys_motion_easing_standard 0x7f130084
int string m3_sys_motion_easing_standard_accelerate 0x7f130085
int string m3_sys_motion_easing_standard_decelerate 0x7f130086
int string main 0x7f130087
int string material_clock_display_divider 0x7f130088
int string material_clock_toggle_content_description 0x7f130089
int string material_hour_24h_suffix 0x7f13008a
int string material_hour_selection 0x7f13008b
int string material_hour_suffix 0x7f13008c
int string material_minute_selection 0x7f13008d
int string material_minute_suffix 0x7f13008e
int string material_motion_easing_accelerated 0x7f13008f
int string material_motion_easing_decelerated 0x7f130090
int string material_motion_easing_emphasized 0x7f130091
int string material_motion_easing_linear 0x7f130092
int string material_motion_easing_standard 0x7f130093
int string material_slider_range_end 0x7f130094
int string material_slider_range_start 0x7f130095
int string material_slider_value 0x7f130096
int string material_timepicker_am 0x7f130097
int string material_timepicker_clock_mode_description 0x7f130098
int string material_timepicker_hour 0x7f130099
int string material_timepicker_minute 0x7f13009a
int string material_timepicker_pm 0x7f13009b
int string material_timepicker_select_time 0x7f13009c
int string material_timepicker_text_input_mode_description 0x7f13009d
int string mtrl_badge_numberless_content_description 0x7f13009e
int string mtrl_checkbox_button_icon_path_checked 0x7f13009f
int string mtrl_checkbox_button_icon_path_group_name 0x7f1300a0
int string mtrl_checkbox_button_icon_path_indeterminate 0x7f1300a1
int string mtrl_checkbox_button_icon_path_name 0x7f1300a2
int string mtrl_checkbox_button_path_checked 0x7f1300a3
int string mtrl_checkbox_button_path_group_name 0x7f1300a4
int string mtrl_checkbox_button_path_name 0x7f1300a5
int string mtrl_checkbox_button_path_unchecked 0x7f1300a6
int string mtrl_checkbox_state_description_checked 0x7f1300a7
int string mtrl_checkbox_state_description_indeterminate 0x7f1300a8
int string mtrl_checkbox_state_description_unchecked 0x7f1300a9
int string mtrl_chip_close_icon_content_description 0x7f1300aa
int string mtrl_exceed_max_badge_number_content_description 0x7f1300ab
int string mtrl_exceed_max_badge_number_suffix 0x7f1300ac
int string mtrl_picker_a11y_next_month 0x7f1300ad
int string mtrl_picker_a11y_prev_month 0x7f1300ae
int string mtrl_picker_announce_current_range_selection 0x7f1300af
int string mtrl_picker_announce_current_selection 0x7f1300b0
int string mtrl_picker_announce_current_selection_none 0x7f1300b1
int string mtrl_picker_cancel 0x7f1300b2
int string mtrl_picker_confirm 0x7f1300b3
int string mtrl_picker_date_header_selected 0x7f1300b4
int string mtrl_picker_date_header_title 0x7f1300b5
int string mtrl_picker_date_header_unselected 0x7f1300b6
int string mtrl_picker_day_of_week_column_header 0x7f1300b7
int string mtrl_picker_end_date_description 0x7f1300b8
int string mtrl_picker_invalid_format 0x7f1300b9
int string mtrl_picker_invalid_format_example 0x7f1300ba
int string mtrl_picker_invalid_format_use 0x7f1300bb
int string mtrl_picker_invalid_range 0x7f1300bc
int string mtrl_picker_navigate_to_current_year_description 0x7f1300bd
int string mtrl_picker_navigate_to_year_description 0x7f1300be
int string mtrl_picker_out_of_range 0x7f1300bf
int string mtrl_picker_range_header_only_end_selected 0x7f1300c0
int string mtrl_picker_range_header_only_start_selected 0x7f1300c1
int string mtrl_picker_range_header_selected 0x7f1300c2
int string mtrl_picker_range_header_title 0x7f1300c3
int string mtrl_picker_range_header_unselected 0x7f1300c4
int string mtrl_picker_save 0x7f1300c5
int string mtrl_picker_start_date_description 0x7f1300c6
int string mtrl_picker_text_input_date_hint 0x7f1300c7
int string mtrl_picker_text_input_date_range_end_hint 0x7f1300c8
int string mtrl_picker_text_input_date_range_start_hint 0x7f1300c9
int string mtrl_picker_text_input_day_abbr 0x7f1300ca
int string mtrl_picker_text_input_month_abbr 0x7f1300cb
int string mtrl_picker_text_input_year_abbr 0x7f1300cc
int string mtrl_picker_today_description 0x7f1300cd
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f1300ce
int string mtrl_picker_toggle_to_day_selection 0x7f1300cf
int string mtrl_picker_toggle_to_text_input_mode 0x7f1300d0
int string mtrl_picker_toggle_to_year_selection 0x7f1300d1
int string mtrl_switch_thumb_group_name 0x7f1300d2
int string mtrl_switch_thumb_path_checked 0x7f1300d3
int string mtrl_switch_thumb_path_morphing 0x7f1300d4
int string mtrl_switch_thumb_path_name 0x7f1300d5
int string mtrl_switch_thumb_path_pressed 0x7f1300d6
int string mtrl_switch_thumb_path_unchecked 0x7f1300d7
int string mtrl_switch_track_decoration_path 0x7f1300d8
int string mtrl_switch_track_path 0x7f1300d9
int string mtrl_timepicker_cancel 0x7f1300da
int string mtrl_timepicker_confirm 0x7f1300db
int string native_body 0x7f1300dc
int string native_headline 0x7f1300dd
int string native_media_view 0x7f1300de
int string nature 0x7f1300df
int string nav_drawer_closed 0x7f1300e0
int string nav_drawer_opened 0x7f1300e1
int string no_data 0x7f1300e2
int string nomore 0x7f1300e3
int string not_set 0x7f1300e4
int string notification_body 0x7f1300e5
int string notification_from 0x7f1300e6
int string notification_permission_denied 0x7f1300e7
int string notification_permission_granted 0x7f1300e8
int string notification_permission_name_for_title 0x7f1300e9
int string notification_permission_settings_message 0x7f1300ea
int string offline_notification_text 0x7f1300eb
int string offline_notification_title 0x7f1300ec
int string offline_opt_in_confirm 0x7f1300ed
int string offline_opt_in_confirmation 0x7f1300ee
int string offline_opt_in_decline 0x7f1300ef
int string offline_opt_in_message 0x7f1300f0
int string offline_opt_in_title 0x7f1300f1
int string ok 0x7f1300f2
int string password_toggle_content_description 0x7f1300f3
int string path_password_eye 0x7f1300f4
int string path_password_eye_mask_strike_through 0x7f1300f5
int string path_password_eye_mask_visible 0x7f1300f6
int string path_password_strike_through 0x7f1300f7
int string permission 0x7f1300f8
int string permission_not_available_message 0x7f1300f9
int string permission_not_available_open_settings_option 0x7f1300fa
int string permission_not_available_title 0x7f1300fb
int string preference_copied 0x7f1300fc
int string privacypolicy 0x7f1300fd
int string project_id 0x7f1300fe
int string pub_id 0x7f1300ff
int string rate 0x7f130100
int string s1 0x7f130101
int string s2 0x7f130102
int string s3 0x7f130103
int string s4 0x7f130104
int string s5 0x7f130105
int string s6 0x7f130106
int string s7 0x7f130107
int string save 0x7f130108
int string search_menu_title 0x7f130109
int string searchbar_scrolling_view_behavior 0x7f13010a
int string searchview_clear_text_content_description 0x7f13010b
int string searchview_navigation_content_description 0x7f13010c
int string settings 0x7f13010d
int string side_sheet_accessibility_pane_title 0x7f13010e
int string side_sheet_behavior 0x7f13010f
int string soon 0x7f130110
int string soundoff 0x7f130111
int string soundon 0x7f130112
int string status_bar_notification_info_overflow 0x7f130113
int string summary_collapsed_preference_list 0x7f130114
int string transport 0x7f130115
int string v7_preference_off 0x7f130116
int string v7_preference_on 0x7f130117
int string watermark_label_prefix 0x7f130118
int style AlertDialog_AppCompat 0x7f140000
int style AlertDialog_AppCompat_Light 0x7f140001
int style Animation_AppCompat_Dialog 0x7f140002
int style Animation_AppCompat_DropDownUp 0x7f140003
int style Animation_AppCompat_Tooltip 0x7f140004
int style Animation_Design_BottomSheetDialog 0x7f140005
int style Animation_Material3_BottomSheetDialog 0x7f140006
int style Animation_Material3_SideSheetDialog 0x7f140007
int style Animation_Material3_SideSheetDialog_Left 0x7f140008
int style Animation_Material3_SideSheetDialog_Right 0x7f140009
int style Animation_MaterialComponents_BottomSheetDialog 0x7f14000a
int style AppTheme 0x7f14000b
int style AppTheme_AppBarOverlay 0x7f14000c
int style AppTheme_NoActionBar 0x7f14000d
int style AppTheme_NoActionBar_AppBarOverlay 0x7f14000e
int style AppTheme_PopupOverlay 0x7f14000f
int style Base_AlertDialog_AppCompat 0x7f140010
int style Base_AlertDialog_AppCompat_Light 0x7f140011
int style Base_Animation_AppCompat_Dialog 0x7f140012
int style Base_Animation_AppCompat_DropDownUp 0x7f140013
int style Base_Animation_AppCompat_Tooltip 0x7f140014
int style Base_CardView 0x7f140015
int style Base_DialogWindowTitle_AppCompat 0x7f140016
int style Base_DialogWindowTitleBackground_AppCompat 0x7f140017
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f140018
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f140019
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f14001a
int style Base_TextAppearance_AppCompat 0x7f14001b
int style Base_TextAppearance_AppCompat_Body1 0x7f14001c
int style Base_TextAppearance_AppCompat_Body2 0x7f14001d
int style Base_TextAppearance_AppCompat_Button 0x7f14001e
int style Base_TextAppearance_AppCompat_Caption 0x7f14001f
int style Base_TextAppearance_AppCompat_Display1 0x7f140020
int style Base_TextAppearance_AppCompat_Display2 0x7f140021
int style Base_TextAppearance_AppCompat_Display3 0x7f140022
int style Base_TextAppearance_AppCompat_Display4 0x7f140023
int style Base_TextAppearance_AppCompat_Headline 0x7f140024
int style Base_TextAppearance_AppCompat_Inverse 0x7f140025
int style Base_TextAppearance_AppCompat_Large 0x7f140026
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f140027
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f140028
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f140029
int style Base_TextAppearance_AppCompat_Medium 0x7f14002a
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f14002b
int style Base_TextAppearance_AppCompat_Menu 0x7f14002c
int style Base_TextAppearance_AppCompat_SearchResult 0x7f14002d
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f14002e
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f14002f
int style Base_TextAppearance_AppCompat_Small 0x7f140030
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f140031
int style Base_TextAppearance_AppCompat_Subhead 0x7f140032
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f140033
int style Base_TextAppearance_AppCompat_Title 0x7f140034
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f140035
int style Base_TextAppearance_AppCompat_Tooltip 0x7f140036
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f140037
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f140038
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f140039
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f14003a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f14003b
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f14003c
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f14003d
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f14003e
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f14003f
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f140040
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f140041
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f140042
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f140043
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f140044
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f140045
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f140046
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f140047
int style Base_TextAppearance_Material3_Search 0x7f140048
int style Base_TextAppearance_MaterialComponents_Badge 0x7f140049
int style Base_TextAppearance_MaterialComponents_Button 0x7f14004a
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f14004b
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f14004c
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f14004d
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f14004e
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f14004f
int style Base_Theme_AppCompat 0x7f140050
int style Base_Theme_AppCompat_CompactMenu 0x7f140051
int style Base_Theme_AppCompat_Dialog 0x7f140052
int style Base_Theme_AppCompat_Dialog_Alert 0x7f140053
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f140054
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f140055
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f140056
int style Base_Theme_AppCompat_Light 0x7f140057
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f140058
int style Base_Theme_AppCompat_Light_Dialog 0x7f140059
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f14005a
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f14005b
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f14005c
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f14005d
int style Base_Theme_Material3_Dark 0x7f14005e
int style Base_Theme_Material3_Dark_BottomSheetDialog 0x7f14005f
int style Base_Theme_Material3_Dark_Dialog 0x7f140060
int style Base_Theme_Material3_Dark_Dialog_FixedSize 0x7f140061
int style Base_Theme_Material3_Dark_DialogWhenLarge 0x7f140062
int style Base_Theme_Material3_Dark_SideSheetDialog 0x7f140063
int style Base_Theme_Material3_Light 0x7f140064
int style Base_Theme_Material3_Light_BottomSheetDialog 0x7f140065
int style Base_Theme_Material3_Light_Dialog 0x7f140066
int style Base_Theme_Material3_Light_Dialog_FixedSize 0x7f140067
int style Base_Theme_Material3_Light_DialogWhenLarge 0x7f140068
int style Base_Theme_Material3_Light_SideSheetDialog 0x7f140069
int style Base_Theme_MaterialComponents 0x7f14006a
int style Base_Theme_MaterialComponents_Bridge 0x7f14006b
int style Base_Theme_MaterialComponents_CompactMenu 0x7f14006c
int style Base_Theme_MaterialComponents_Dialog 0x7f14006d
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f14006e
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f14006f
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f140070
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f140071
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f140072
int style Base_Theme_MaterialComponents_Light 0x7f140073
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f140074
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f140075
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f140076
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f140077
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f140078
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f140079
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f14007a
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f14007b
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f14007c
int style Base_ThemeOverlay_AppCompat 0x7f14007d
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f14007e
int style Base_ThemeOverlay_AppCompat_Dark 0x7f14007f
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f140080
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f140081
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f140082
int style Base_ThemeOverlay_AppCompat_Light 0x7f140083
int style Base_ThemeOverlay_Material3_AutoCompleteTextView 0x7f140084
int style Base_ThemeOverlay_Material3_BottomSheetDialog 0x7f140085
int style Base_ThemeOverlay_Material3_Dialog 0x7f140086
int style Base_ThemeOverlay_Material3_SideSheetDialog 0x7f140087
int style Base_ThemeOverlay_Material3_TextInputEditText 0x7f140088
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f140089
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f14008a
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f14008b
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f14008c
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f14008d
int style Base_V14_Theme_Material3_Dark 0x7f14008e
int style Base_V14_Theme_Material3_Dark_BottomSheetDialog 0x7f14008f
int style Base_V14_Theme_Material3_Dark_Dialog 0x7f140090
int style Base_V14_Theme_Material3_Dark_SideSheetDialog 0x7f140091
int style Base_V14_Theme_Material3_Light 0x7f140092
int style Base_V14_Theme_Material3_Light_BottomSheetDialog 0x7f140093
int style Base_V14_Theme_Material3_Light_Dialog 0x7f140094
int style Base_V14_Theme_Material3_Light_SideSheetDialog 0x7f140095
int style Base_V14_Theme_MaterialComponents 0x7f140096
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f140097
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f140098
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f140099
int style Base_V14_Theme_MaterialComponents_Light 0x7f14009a
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f14009b
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f14009c
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f14009d
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f14009e
int style Base_V14_ThemeOverlay_Material3_BottomSheetDialog 0x7f14009f
int style Base_V14_ThemeOverlay_Material3_SideSheetDialog 0x7f1400a0
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1400a1
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f1400a2
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f1400a3
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f1400a4
int style Base_V14_Widget_MaterialComponents_AutoCompleteTextView 0x7f1400a5
int style Base_V21_Theme_AppCompat 0x7f1400a6
int style Base_V21_Theme_AppCompat_Dialog 0x7f1400a7
int style Base_V21_Theme_AppCompat_Light 0x7f1400a8
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f1400a9
int style Base_V21_Theme_MaterialComponents 0x7f1400aa
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f1400ab
int style Base_V21_Theme_MaterialComponents_Light 0x7f1400ac
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f1400ad
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f1400ae
int style Base_V21_ThemeOverlay_Material3_BottomSheetDialog 0x7f1400af
int style Base_V21_ThemeOverlay_Material3_SideSheetDialog 0x7f1400b0
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1400b1
int style Base_V22_Theme_AppCompat 0x7f1400b2
int style Base_V22_Theme_AppCompat_Light 0x7f1400b3
int style Base_V23_Theme_AppCompat 0x7f1400b4
int style Base_V23_Theme_AppCompat_Light 0x7f1400b5
int style Base_V24_Theme_Material3_Dark 0x7f1400b6
int style Base_V24_Theme_Material3_Dark_Dialog 0x7f1400b7
int style Base_V24_Theme_Material3_Light 0x7f1400b8
int style Base_V24_Theme_Material3_Light_Dialog 0x7f1400b9
int style Base_V26_Theme_AppCompat 0x7f1400ba
int style Base_V26_Theme_AppCompat_Light 0x7f1400bb
int style Base_V26_Widget_AppCompat_Toolbar 0x7f1400bc
int style Base_V28_Theme_AppCompat 0x7f1400bd
int style Base_V28_Theme_AppCompat_Light 0x7f1400be
int style Base_V7_Theme_AppCompat 0x7f1400bf
int style Base_V7_Theme_AppCompat_Dialog 0x7f1400c0
int style Base_V7_Theme_AppCompat_Light 0x7f1400c1
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f1400c2
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f1400c3
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f1400c4
int style Base_V7_Widget_AppCompat_EditText 0x7f1400c5
int style Base_V7_Widget_AppCompat_Toolbar 0x7f1400c6
int style Base_Widget_AppCompat_ActionBar 0x7f1400c7
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f1400c8
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f1400c9
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1400ca
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1400cb
int style Base_Widget_AppCompat_ActionButton 0x7f1400cc
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1400cd
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1400ce
int style Base_Widget_AppCompat_ActionMode 0x7f1400cf
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1400d0
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1400d1
int style Base_Widget_AppCompat_Button 0x7f1400d2
int style Base_Widget_AppCompat_Button_Borderless 0x7f1400d3
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1400d4
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1400d5
int style Base_Widget_AppCompat_Button_Colored 0x7f1400d6
int style Base_Widget_AppCompat_Button_Small 0x7f1400d7
int style Base_Widget_AppCompat_ButtonBar 0x7f1400d8
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1400d9
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1400da
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1400db
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1400dc
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1400dd
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1400de
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1400df
int style Base_Widget_AppCompat_EditText 0x7f1400e0
int style Base_Widget_AppCompat_ImageButton 0x7f1400e1
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1400e2
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1400e3
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1400e4
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1400e5
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1400e6
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1400e7
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1400e8
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1400e9
int style Base_Widget_AppCompat_ListMenuView 0x7f1400ea
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1400eb
int style Base_Widget_AppCompat_ListView 0x7f1400ec
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1400ed
int style Base_Widget_AppCompat_ListView_Menu 0x7f1400ee
int style Base_Widget_AppCompat_PopupMenu 0x7f1400ef
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1400f0
int style Base_Widget_AppCompat_PopupWindow 0x7f1400f1
int style Base_Widget_AppCompat_ProgressBar 0x7f1400f2
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1400f3
int style Base_Widget_AppCompat_RatingBar 0x7f1400f4
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1400f5
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1400f6
int style Base_Widget_AppCompat_SearchView 0x7f1400f7
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1400f8
int style Base_Widget_AppCompat_SeekBar 0x7f1400f9
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1400fa
int style Base_Widget_AppCompat_Spinner 0x7f1400fb
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1400fc
int style Base_Widget_AppCompat_TextView 0x7f1400fd
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1400fe
int style Base_Widget_AppCompat_Toolbar 0x7f1400ff
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f140100
int style Base_Widget_Design_TabLayout 0x7f140101
int style Base_Widget_Material3_ActionBar_Solid 0x7f140102
int style Base_Widget_Material3_ActionMode 0x7f140103
int style Base_Widget_Material3_BottomNavigationView 0x7f140104
int style Base_Widget_Material3_CardView 0x7f140105
int style Base_Widget_Material3_Chip 0x7f140106
int style Base_Widget_Material3_CollapsingToolbar 0x7f140107
int style Base_Widget_Material3_CompoundButton_CheckBox 0x7f140108
int style Base_Widget_Material3_CompoundButton_RadioButton 0x7f140109
int style Base_Widget_Material3_CompoundButton_Switch 0x7f14010a
int style Base_Widget_Material3_ExtendedFloatingActionButton 0x7f14010b
int style Base_Widget_Material3_ExtendedFloatingActionButton_Icon 0x7f14010c
int style Base_Widget_Material3_FloatingActionButton 0x7f14010d
int style Base_Widget_Material3_FloatingActionButton_Large 0x7f14010e
int style Base_Widget_Material3_FloatingActionButton_Small 0x7f14010f
int style Base_Widget_Material3_Light_ActionBar_Solid 0x7f140110
int style Base_Widget_Material3_MaterialCalendar_NavigationButton 0x7f140111
int style Base_Widget_Material3_Snackbar 0x7f140112
int style Base_Widget_Material3_TabLayout 0x7f140113
int style Base_Widget_Material3_TabLayout_OnSurface 0x7f140114
int style Base_Widget_Material3_TabLayout_Secondary 0x7f140115
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f140116
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f140117
int style Base_Widget_MaterialComponents_Chip 0x7f140118
int style Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f140119
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f14011a
int style Base_Widget_MaterialComponents_PopupMenu 0x7f14011b
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f14011c
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f14011d
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f14011e
int style Base_Widget_MaterialComponents_Slider 0x7f14011f
int style Base_Widget_MaterialComponents_Snackbar 0x7f140120
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f140121
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f140122
int style Base_Widget_MaterialComponents_TextView 0x7f140123
int style BasePreferenceThemeOverlay 0x7f140124
int style CardView 0x7f140125
int style CardView_Dark 0x7f140126
int style CardView_Light 0x7f140127
int style MaterialAlertDialog_Material3 0x7f140128
int style MaterialAlertDialog_Material3_Animation 0x7f140129
int style MaterialAlertDialog_Material3_Body_Text 0x7f14012a
int style MaterialAlertDialog_Material3_Body_Text_CenterStacked 0x7f14012b
int style MaterialAlertDialog_Material3_Title_Icon 0x7f14012c
int style MaterialAlertDialog_Material3_Title_Icon_CenterStacked 0x7f14012d
int style MaterialAlertDialog_Material3_Title_Panel 0x7f14012e
int style MaterialAlertDialog_Material3_Title_Panel_CenterStacked 0x7f14012f
int style MaterialAlertDialog_Material3_Title_Text 0x7f140130
int style MaterialAlertDialog_Material3_Title_Text_CenterStacked 0x7f140131
int style MaterialAlertDialog_MaterialComponents 0x7f140132
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f140133
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f140134
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f140135
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f140136
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f140137
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f140138
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f140139
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f14013a
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f14013b
int style PickerEditText 0x7f14013c
int style Platform_AppCompat 0x7f14013d
int style Platform_AppCompat_Light 0x7f14013e
int style Platform_MaterialComponents 0x7f14013f
int style Platform_MaterialComponents_Dialog 0x7f140140
int style Platform_MaterialComponents_Light 0x7f140141
int style Platform_MaterialComponents_Light_Dialog 0x7f140142
int style Platform_ThemeOverlay_AppCompat 0x7f140143
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f140144
int style Platform_ThemeOverlay_AppCompat_Light 0x7f140145
int style Platform_V21_AppCompat 0x7f140146
int style Platform_V21_AppCompat_Light 0x7f140147
int style Platform_V25_AppCompat 0x7f140148
int style Platform_V25_AppCompat_Light 0x7f140149
int style Platform_Widget_AppCompat_Spinner 0x7f14014a
int style Preference 0x7f14014b
int style Preference_Category 0x7f14014c
int style Preference_Category_Material 0x7f14014d
int style Preference_CheckBoxPreference 0x7f14014e
int style Preference_CheckBoxPreference_Material 0x7f14014f
int style Preference_DialogPreference 0x7f140150
int style Preference_DialogPreference_EditTextPreference 0x7f140151
int style Preference_DialogPreference_EditTextPreference_Material 0x7f140152
int style Preference_DialogPreference_Material 0x7f140153
int style Preference_DropDown 0x7f140154
int style Preference_DropDown_Material 0x7f140155
int style Preference_Information 0x7f140156
int style Preference_Information_Material 0x7f140157
int style Preference_Material 0x7f140158
int style Preference_PreferenceScreen 0x7f140159
int style Preference_PreferenceScreen_Material 0x7f14015a
int style Preference_SeekBarPreference 0x7f14015b
int style Preference_SeekBarPreference_Material 0x7f14015c
int style Preference_SwitchPreference 0x7f14015d
int style Preference_SwitchPreference_Material 0x7f14015e
int style Preference_SwitchPreferenceCompat 0x7f14015f
int style Preference_SwitchPreferenceCompat_Material 0x7f140160
int style PreferenceCategoryTitleTextStyle 0x7f140161
int style PreferenceFragment 0x7f140162
int style PreferenceFragment_Material 0x7f140163
int style PreferenceFragmentList 0x7f140164
int style PreferenceFragmentList_Material 0x7f140165
int style PreferenceSummaryTextStyle 0x7f140166
int style PreferenceThemeOverlay 0x7f140167
int style PreferenceThemeOverlay_v14 0x7f140168
int style PreferenceThemeOverlay_v14_Material 0x7f140169
int style PremiumButtonStyle 0x7f14016a
int style PremiumCardStyle 0x7f14016b
int style PremiumNavItemShape 0x7f14016c
int style PremiumNavTextAppearance 0x7f14016d
int style PremiumSubtitleText 0x7f14016e
int style PremiumTitleText 0x7f14016f
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f140170
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f140171
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f140172
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f140173
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f140174
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f140175
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f140176
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f140177
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f140178
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f140179
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f14017a
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f14017b
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f14017c
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f14017d
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f14017e
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f14017f
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f140180
int style ShapeAppearance_M3_Comp_Badge_Large_Shape 0x7f140181
int style ShapeAppearance_M3_Comp_Badge_Shape 0x7f140182
int style ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape 0x7f140183
int style ShapeAppearance_M3_Comp_DatePicker_Modal_Date_Container_Shape 0x7f140184
int style ShapeAppearance_M3_Comp_FilledButton_Container_Shape 0x7f140185
int style ShapeAppearance_M3_Comp_NavigationBar_ActiveIndicator_Shape 0x7f140186
int style ShapeAppearance_M3_Comp_NavigationBar_Container_Shape 0x7f140187
int style ShapeAppearance_M3_Comp_NavigationDrawer_ActiveIndicator_Shape 0x7f140188
int style ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape 0x7f140189
int style ShapeAppearance_M3_Comp_NavigationRail_Container_Shape 0x7f14018a
int style ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape 0x7f14018b
int style ShapeAppearance_M3_Comp_SearchBar_Container_Shape 0x7f14018c
int style ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape 0x7f14018d
int style ShapeAppearance_M3_Comp_Sheet_Side_Docked_Container_Shape 0x7f14018e
int style ShapeAppearance_M3_Comp_Switch_Handle_Shape 0x7f14018f
int style ShapeAppearance_M3_Comp_Switch_StateLayer_Shape 0x7f140190
int style ShapeAppearance_M3_Comp_Switch_Track_Shape 0x7f140191
int style ShapeAppearance_M3_Comp_TextButton_Container_Shape 0x7f140192
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge 0x7f140193
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall 0x7f140194
int style ShapeAppearance_M3_Sys_Shape_Corner_Full 0x7f140195
int style ShapeAppearance_M3_Sys_Shape_Corner_Large 0x7f140196
int style ShapeAppearance_M3_Sys_Shape_Corner_Medium 0x7f140197
int style ShapeAppearance_M3_Sys_Shape_Corner_None 0x7f140198
int style ShapeAppearance_M3_Sys_Shape_Corner_Small 0x7f140199
int style ShapeAppearance_Material3_Corner_ExtraLarge 0x7f14019a
int style ShapeAppearance_Material3_Corner_ExtraSmall 0x7f14019b
int style ShapeAppearance_Material3_Corner_Full 0x7f14019c
int style ShapeAppearance_Material3_Corner_Large 0x7f14019d
int style ShapeAppearance_Material3_Corner_Medium 0x7f14019e
int style ShapeAppearance_Material3_Corner_None 0x7f14019f
int style ShapeAppearance_Material3_Corner_Small 0x7f1401a0
int style ShapeAppearance_Material3_LargeComponent 0x7f1401a1
int style ShapeAppearance_Material3_MediumComponent 0x7f1401a2
int style ShapeAppearance_Material3_NavigationBarView_ActiveIndicator 0x7f1401a3
int style ShapeAppearance_Material3_SmallComponent 0x7f1401a4
int style ShapeAppearance_Material3_Tooltip 0x7f1401a5
int style ShapeAppearance_MaterialComponents 0x7f1401a6
int style ShapeAppearance_MaterialComponents_Badge 0x7f1401a7
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f1401a8
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f1401a9
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f1401aa
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f1401ab
int style ShapeAppearanceOverlay_Material3_Button 0x7f1401ac
int style ShapeAppearanceOverlay_Material3_Chip 0x7f1401ad
int style ShapeAppearanceOverlay_Material3_Corner_Bottom 0x7f1401ae
int style ShapeAppearanceOverlay_Material3_Corner_Left 0x7f1401af
int style ShapeAppearanceOverlay_Material3_Corner_Right 0x7f1401b0
int style ShapeAppearanceOverlay_Material3_Corner_Top 0x7f1401b1
int style ShapeAppearanceOverlay_Material3_FloatingActionButton 0x7f1401b2
int style ShapeAppearanceOverlay_Material3_NavigationView_Item 0x7f1401b3
int style ShapeAppearanceOverlay_Material3_SearchBar 0x7f1401b4
int style ShapeAppearanceOverlay_Material3_SearchView 0x7f1401b5
int style ShapeAppearanceOverlay_MaterialAlertDialog_Material3 0x7f1401b6
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f1401b7
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f1401b8
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f1401b9
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f1401ba
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f1401bb
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f1401bc
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f1401bd
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f1401be
int style TextAppearance_AppCompat 0x7f1401bf
int style TextAppearance_AppCompat_Body1 0x7f1401c0
int style TextAppearance_AppCompat_Body2 0x7f1401c1
int style TextAppearance_AppCompat_Button 0x7f1401c2
int style TextAppearance_AppCompat_Caption 0x7f1401c3
int style TextAppearance_AppCompat_Display1 0x7f1401c4
int style TextAppearance_AppCompat_Display2 0x7f1401c5
int style TextAppearance_AppCompat_Display3 0x7f1401c6
int style TextAppearance_AppCompat_Display4 0x7f1401c7
int style TextAppearance_AppCompat_Headline 0x7f1401c8
int style TextAppearance_AppCompat_Inverse 0x7f1401c9
int style TextAppearance_AppCompat_Large 0x7f1401ca
int style TextAppearance_AppCompat_Large_Inverse 0x7f1401cb
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f1401cc
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f1401cd
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f1401ce
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f1401cf
int style TextAppearance_AppCompat_Medium 0x7f1401d0
int style TextAppearance_AppCompat_Medium_Inverse 0x7f1401d1
int style TextAppearance_AppCompat_Menu 0x7f1401d2
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f1401d3
int style TextAppearance_AppCompat_SearchResult_Title 0x7f1401d4
int style TextAppearance_AppCompat_Small 0x7f1401d5
int style TextAppearance_AppCompat_Small_Inverse 0x7f1401d6
int style TextAppearance_AppCompat_Subhead 0x7f1401d7
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f1401d8
int style TextAppearance_AppCompat_Title 0x7f1401d9
int style TextAppearance_AppCompat_Title_Inverse 0x7f1401da
int style TextAppearance_AppCompat_Tooltip 0x7f1401db
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f1401dc
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1401dd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1401de
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1401df
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f1401e0
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f1401e1
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f1401e2
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f1401e3
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f1401e4
int style TextAppearance_AppCompat_Widget_Button 0x7f1401e5
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f1401e6
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f1401e7
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f1401e8
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f1401e9
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f1401ea
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f1401eb
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f1401ec
int style TextAppearance_AppCompat_Widget_Switch 0x7f1401ed
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f1401ee
int style TextAppearance_Compat_Notification 0x7f1401ef
int style TextAppearance_Compat_Notification_Info 0x7f1401f0
int style TextAppearance_Compat_Notification_Info_Media 0x7f1401f1
int style TextAppearance_Compat_Notification_Line2 0x7f1401f2
int style TextAppearance_Compat_Notification_Line2_Media 0x7f1401f3
int style TextAppearance_Compat_Notification_Media 0x7f1401f4
int style TextAppearance_Compat_Notification_Time 0x7f1401f5
int style TextAppearance_Compat_Notification_Time_Media 0x7f1401f6
int style TextAppearance_Compat_Notification_Title 0x7f1401f7
int style TextAppearance_Compat_Notification_Title_Media 0x7f1401f8
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f1401f9
int style TextAppearance_Design_Counter 0x7f1401fa
int style TextAppearance_Design_Counter_Overflow 0x7f1401fb
int style TextAppearance_Design_Error 0x7f1401fc
int style TextAppearance_Design_HelperText 0x7f1401fd
int style TextAppearance_Design_Hint 0x7f1401fe
int style TextAppearance_Design_Placeholder 0x7f1401ff
int style TextAppearance_Design_Prefix 0x7f140200
int style TextAppearance_Design_Snackbar_Message 0x7f140201
int style TextAppearance_Design_Suffix 0x7f140202
int style TextAppearance_Design_Tab 0x7f140203
int style TextAppearance_M3_Sys_Typescale_BodyLarge 0x7f140204
int style TextAppearance_M3_Sys_Typescale_BodyMedium 0x7f140205
int style TextAppearance_M3_Sys_Typescale_BodySmall 0x7f140206
int style TextAppearance_M3_Sys_Typescale_DisplayLarge 0x7f140207
int style TextAppearance_M3_Sys_Typescale_DisplayMedium 0x7f140208
int style TextAppearance_M3_Sys_Typescale_DisplaySmall 0x7f140209
int style TextAppearance_M3_Sys_Typescale_HeadlineLarge 0x7f14020a
int style TextAppearance_M3_Sys_Typescale_HeadlineMedium 0x7f14020b
int style TextAppearance_M3_Sys_Typescale_HeadlineSmall 0x7f14020c
int style TextAppearance_M3_Sys_Typescale_LabelLarge 0x7f14020d
int style TextAppearance_M3_Sys_Typescale_LabelMedium 0x7f14020e
int style TextAppearance_M3_Sys_Typescale_LabelSmall 0x7f14020f
int style TextAppearance_M3_Sys_Typescale_TitleLarge 0x7f140210
int style TextAppearance_M3_Sys_Typescale_TitleMedium 0x7f140211
int style TextAppearance_M3_Sys_Typescale_TitleSmall 0x7f140212
int style TextAppearance_Material3_ActionBar_Subtitle 0x7f140213
int style TextAppearance_Material3_ActionBar_Title 0x7f140214
int style TextAppearance_Material3_BodyLarge 0x7f140215
int style TextAppearance_Material3_BodyMedium 0x7f140216
int style TextAppearance_Material3_BodySmall 0x7f140217
int style TextAppearance_Material3_DisplayLarge 0x7f140218
int style TextAppearance_Material3_DisplayMedium 0x7f140219
int style TextAppearance_Material3_DisplaySmall 0x7f14021a
int style TextAppearance_Material3_HeadlineLarge 0x7f14021b
int style TextAppearance_Material3_HeadlineMedium 0x7f14021c
int style TextAppearance_Material3_HeadlineSmall 0x7f14021d
int style TextAppearance_Material3_LabelLarge 0x7f14021e
int style TextAppearance_Material3_LabelMedium 0x7f14021f
int style TextAppearance_Material3_LabelSmall 0x7f140220
int style TextAppearance_Material3_MaterialTimePicker_Title 0x7f140221
int style TextAppearance_Material3_SearchBar 0x7f140222
int style TextAppearance_Material3_SearchView 0x7f140223
int style TextAppearance_Material3_SearchView_Prefix 0x7f140224
int style TextAppearance_Material3_TitleLarge 0x7f140225
int style TextAppearance_Material3_TitleMedium 0x7f140226
int style TextAppearance_Material3_TitleSmall 0x7f140227
int style TextAppearance_MaterialComponents_Badge 0x7f140228
int style TextAppearance_MaterialComponents_Body1 0x7f140229
int style TextAppearance_MaterialComponents_Body2 0x7f14022a
int style TextAppearance_MaterialComponents_Button 0x7f14022b
int style TextAppearance_MaterialComponents_Caption 0x7f14022c
int style TextAppearance_MaterialComponents_Chip 0x7f14022d
int style TextAppearance_MaterialComponents_Headline1 0x7f14022e
int style TextAppearance_MaterialComponents_Headline2 0x7f14022f
int style TextAppearance_MaterialComponents_Headline3 0x7f140230
int style TextAppearance_MaterialComponents_Headline4 0x7f140231
int style TextAppearance_MaterialComponents_Headline5 0x7f140232
int style TextAppearance_MaterialComponents_Headline6 0x7f140233
int style TextAppearance_MaterialComponents_Overline 0x7f140234
int style TextAppearance_MaterialComponents_Subtitle1 0x7f140235
int style TextAppearance_MaterialComponents_Subtitle2 0x7f140236
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f140237
int style TextAppearance_MaterialComponents_Tooltip 0x7f140238
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f140239
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f14023a
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f14023b
int style Theme_AppCompat 0x7f14023c
int style Theme_AppCompat_CompactMenu 0x7f14023d
int style Theme_AppCompat_DayNight 0x7f14023e
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f14023f
int style Theme_AppCompat_DayNight_Dialog 0x7f140240
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f140241
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f140242
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f140243
int style Theme_AppCompat_DayNight_NoActionBar 0x7f140244
int style Theme_AppCompat_Dialog 0x7f140245
int style Theme_AppCompat_Dialog_Alert 0x7f140246
int style Theme_AppCompat_Dialog_MinWidth 0x7f140247
int style Theme_AppCompat_DialogWhenLarge 0x7f140248
int style Theme_AppCompat_Empty 0x7f140249
int style Theme_AppCompat_Light 0x7f14024a
int style Theme_AppCompat_Light_DarkActionBar 0x7f14024b
int style Theme_AppCompat_Light_Dialog 0x7f14024c
int style Theme_AppCompat_Light_Dialog_Alert 0x7f14024d
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f14024e
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f14024f
int style Theme_AppCompat_Light_NoActionBar 0x7f140250
int style Theme_AppCompat_NoActionBar 0x7f140251
int style Theme_Design 0x7f140252
int style Theme_Design_BottomSheetDialog 0x7f140253
int style Theme_Design_Light 0x7f140254
int style Theme_Design_Light_BottomSheetDialog 0x7f140255
int style Theme_Design_Light_NoActionBar 0x7f140256
int style Theme_Design_NoActionBar 0x7f140257
int style Theme_IAPTheme 0x7f140258
int style Theme_Material3_Dark 0x7f140259
int style Theme_Material3_Dark_BottomSheetDialog 0x7f14025a
int style Theme_Material3_Dark_Dialog 0x7f14025b
int style Theme_Material3_Dark_Dialog_Alert 0x7f14025c
int style Theme_Material3_Dark_Dialog_MinWidth 0x7f14025d
int style Theme_Material3_Dark_DialogWhenLarge 0x7f14025e
int style Theme_Material3_Dark_NoActionBar 0x7f14025f
int style Theme_Material3_Dark_SideSheetDialog 0x7f140260
int style Theme_Material3_DayNight 0x7f140261
int style Theme_Material3_DayNight_BottomSheetDialog 0x7f140262
int style Theme_Material3_DayNight_Dialog 0x7f140263
int style Theme_Material3_DayNight_Dialog_Alert 0x7f140264
int style Theme_Material3_DayNight_Dialog_MinWidth 0x7f140265
int style Theme_Material3_DayNight_DialogWhenLarge 0x7f140266
int style Theme_Material3_DayNight_NoActionBar 0x7f140267
int style Theme_Material3_DayNight_SideSheetDialog 0x7f140268
int style Theme_Material3_DynamicColors_Dark 0x7f140269
int style Theme_Material3_DynamicColors_Dark_NoActionBar 0x7f14026a
int style Theme_Material3_DynamicColors_DayNight 0x7f14026b
int style Theme_Material3_DynamicColors_DayNight_NoActionBar 0x7f14026c
int style Theme_Material3_DynamicColors_Light 0x7f14026d
int style Theme_Material3_DynamicColors_Light_NoActionBar 0x7f14026e
int style Theme_Material3_Light 0x7f14026f
int style Theme_Material3_Light_BottomSheetDialog 0x7f140270
int style Theme_Material3_Light_Dialog 0x7f140271
int style Theme_Material3_Light_Dialog_Alert 0x7f140272
int style Theme_Material3_Light_Dialog_MinWidth 0x7f140273
int style Theme_Material3_Light_DialogWhenLarge 0x7f140274
int style Theme_Material3_Light_NoActionBar 0x7f140275
int style Theme_Material3_Light_SideSheetDialog 0x7f140276
int style Theme_MaterialComponents 0x7f140277
int style Theme_MaterialComponents_BottomSheetDialog 0x7f140278
int style Theme_MaterialComponents_Bridge 0x7f140279
int style Theme_MaterialComponents_CompactMenu 0x7f14027a
int style Theme_MaterialComponents_DayNight 0x7f14027b
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f14027c
int style Theme_MaterialComponents_DayNight_Bridge 0x7f14027d
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f14027e
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f14027f
int style Theme_MaterialComponents_DayNight_Dialog 0x7f140280
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f140281
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f140282
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f140283
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f140284
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f140285
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f140286
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f140287
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f140288
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f140289
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f14028a
int style Theme_MaterialComponents_Dialog 0x7f14028b
int style Theme_MaterialComponents_Dialog_Alert 0x7f14028c
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f14028d
int style Theme_MaterialComponents_Dialog_Bridge 0x7f14028e
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f14028f
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f140290
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f140291
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f140292
int style Theme_MaterialComponents_DialogWhenLarge 0x7f140293
int style Theme_MaterialComponents_Light 0x7f140294
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f140295
int style Theme_MaterialComponents_Light_Bridge 0x7f140296
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f140297
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f140298
int style Theme_MaterialComponents_Light_Dialog 0x7f140299
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f14029a
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f14029b
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f14029c
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f14029d
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f14029e
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f14029f
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f1402a0
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f1402a1
int style Theme_MaterialComponents_Light_NoActionBar 0x7f1402a2
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f1402a3
int style Theme_MaterialComponents_NoActionBar 0x7f1402a4
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f1402a5
int style Theme_PlayCore_Transparent 0x7f1402a6
int style ThemeOverlay_AppCompat 0x7f1402a7
int style ThemeOverlay_AppCompat_ActionBar 0x7f1402a8
int style ThemeOverlay_AppCompat_Dark 0x7f1402a9
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f1402aa
int style ThemeOverlay_AppCompat_DayNight 0x7f1402ab
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f1402ac
int style ThemeOverlay_AppCompat_Dialog 0x7f1402ad
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f1402ae
int style ThemeOverlay_AppCompat_Light 0x7f1402af
int style ThemeOverlay_Design_TextInputEditText 0x7f1402b0
int style ThemeOverlay_Material3 0x7f1402b1
int style ThemeOverlay_Material3_ActionBar 0x7f1402b2
int style ThemeOverlay_Material3_AutoCompleteTextView 0x7f1402b3
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox 0x7f1402b4
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f1402b5
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox 0x7f1402b6
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f1402b7
int style ThemeOverlay_Material3_BottomAppBar 0x7f1402b8
int style ThemeOverlay_Material3_BottomAppBar_Legacy 0x7f1402b9
int style ThemeOverlay_Material3_BottomNavigationView 0x7f1402ba
int style ThemeOverlay_Material3_BottomSheetDialog 0x7f1402bb
int style ThemeOverlay_Material3_Button 0x7f1402bc
int style ThemeOverlay_Material3_Button_ElevatedButton 0x7f1402bd
int style ThemeOverlay_Material3_Button_IconButton 0x7f1402be
int style ThemeOverlay_Material3_Button_IconButton_Filled 0x7f1402bf
int style ThemeOverlay_Material3_Button_IconButton_Filled_Tonal 0x7f1402c0
int style ThemeOverlay_Material3_Button_TextButton 0x7f1402c1
int style ThemeOverlay_Material3_Button_TextButton_Snackbar 0x7f1402c2
int style ThemeOverlay_Material3_Button_TonalButton 0x7f1402c3
int style ThemeOverlay_Material3_Chip 0x7f1402c4
int style ThemeOverlay_Material3_Chip_Assist 0x7f1402c5
int style ThemeOverlay_Material3_Dark 0x7f1402c6
int style ThemeOverlay_Material3_Dark_ActionBar 0x7f1402c7
int style ThemeOverlay_Material3_DayNight_BottomSheetDialog 0x7f1402c8
int style ThemeOverlay_Material3_DayNight_SideSheetDialog 0x7f1402c9
int style ThemeOverlay_Material3_Dialog 0x7f1402ca
int style ThemeOverlay_Material3_Dialog_Alert 0x7f1402cb
int style ThemeOverlay_Material3_Dialog_Alert_Framework 0x7f1402cc
int style ThemeOverlay_Material3_DynamicColors_Dark 0x7f1402cd
int style ThemeOverlay_Material3_DynamicColors_DayNight 0x7f1402ce
int style ThemeOverlay_Material3_DynamicColors_Light 0x7f1402cf
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary 0x7f1402d0
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary 0x7f1402d1
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface 0x7f1402d2
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary 0x7f1402d3
int style ThemeOverlay_Material3_FloatingActionButton_Primary 0x7f1402d4
int style ThemeOverlay_Material3_FloatingActionButton_Secondary 0x7f1402d5
int style ThemeOverlay_Material3_FloatingActionButton_Surface 0x7f1402d6
int style ThemeOverlay_Material3_FloatingActionButton_Tertiary 0x7f1402d7
int style ThemeOverlay_Material3_HarmonizedColors 0x7f1402d8
int style ThemeOverlay_Material3_HarmonizedColors_Empty 0x7f1402d9
int style ThemeOverlay_Material3_Light 0x7f1402da
int style ThemeOverlay_Material3_Light_Dialog_Alert_Framework 0x7f1402db
int style ThemeOverlay_Material3_MaterialAlertDialog 0x7f1402dc
int style ThemeOverlay_Material3_MaterialAlertDialog_Centered 0x7f1402dd
int style ThemeOverlay_Material3_MaterialCalendar 0x7f1402de
int style ThemeOverlay_Material3_MaterialCalendar_Fullscreen 0x7f1402df
int style ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton 0x7f1402e0
int style ThemeOverlay_Material3_MaterialTimePicker 0x7f1402e1
int style ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1402e2
int style ThemeOverlay_Material3_NavigationRailView 0x7f1402e3
int style ThemeOverlay_Material3_NavigationView 0x7f1402e4
int style ThemeOverlay_Material3_PersonalizedColors 0x7f1402e5
int style ThemeOverlay_Material3_Search 0x7f1402e6
int style ThemeOverlay_Material3_SideSheetDialog 0x7f1402e7
int style ThemeOverlay_Material3_Snackbar 0x7f1402e8
int style ThemeOverlay_Material3_TabLayout 0x7f1402e9
int style ThemeOverlay_Material3_TextInputEditText 0x7f1402ea
int style ThemeOverlay_Material3_TextInputEditText_FilledBox 0x7f1402eb
int style ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense 0x7f1402ec
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox 0x7f1402ed
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense 0x7f1402ee
int style ThemeOverlay_Material3_Toolbar_Surface 0x7f1402ef
int style ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon 0x7f1402f0
int style ThemeOverlay_MaterialComponents 0x7f1402f1
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f1402f2
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f1402f3
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f1402f4
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f1402f5
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1402f6
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1402f7
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1402f8
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1402f9
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f1402fa
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f1402fb
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1402fc
int style ThemeOverlay_MaterialComponents_Dark 0x7f1402fd
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f1402fe
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f1402ff
int style ThemeOverlay_MaterialComponents_Dialog 0x7f140300
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f140301
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f140302
int style ThemeOverlay_MaterialComponents_Light 0x7f140303
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f140304
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f140305
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f140306
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f140307
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f140308
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f140309
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f14030a
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f14030b
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f14030c
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f14030d
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f14030e
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f14030f
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f140310
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f140311
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f140312
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f140313
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f140314
int style ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f140315
int style ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary 0x7f140316
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f140317
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f140318
int style Widget_AppCompat_ActionBar 0x7f140319
int style Widget_AppCompat_ActionBar_Solid 0x7f14031a
int style Widget_AppCompat_ActionBar_TabBar 0x7f14031b
int style Widget_AppCompat_ActionBar_TabText 0x7f14031c
int style Widget_AppCompat_ActionBar_TabView 0x7f14031d
int style Widget_AppCompat_ActionButton 0x7f14031e
int style Widget_AppCompat_ActionButton_CloseMode 0x7f14031f
int style Widget_AppCompat_ActionButton_Overflow 0x7f140320
int style Widget_AppCompat_ActionMode 0x7f140321
int style Widget_AppCompat_ActivityChooserView 0x7f140322
int style Widget_AppCompat_AutoCompleteTextView 0x7f140323
int style Widget_AppCompat_Button 0x7f140324
int style Widget_AppCompat_Button_Borderless 0x7f140325
int style Widget_AppCompat_Button_Borderless_Colored 0x7f140326
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f140327
int style Widget_AppCompat_Button_Colored 0x7f140328
int style Widget_AppCompat_Button_Small 0x7f140329
int style Widget_AppCompat_ButtonBar 0x7f14032a
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f14032b
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f14032c
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f14032d
int style Widget_AppCompat_CompoundButton_Switch 0x7f14032e
int style Widget_AppCompat_DrawerArrowToggle 0x7f14032f
int style Widget_AppCompat_DropDownItem_Spinner 0x7f140330
int style Widget_AppCompat_EditText 0x7f140331
int style Widget_AppCompat_ImageButton 0x7f140332
int style Widget_AppCompat_Light_ActionBar 0x7f140333
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f140334
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f140335
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f140336
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f140337
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f140338
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f140339
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f14033a
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f14033b
int style Widget_AppCompat_Light_ActionButton 0x7f14033c
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f14033d
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f14033e
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f14033f
int style Widget_AppCompat_Light_ActivityChooserView 0x7f140340
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f140341
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f140342
int style Widget_AppCompat_Light_ListPopupWindow 0x7f140343
int style Widget_AppCompat_Light_ListView_DropDown 0x7f140344
int style Widget_AppCompat_Light_PopupMenu 0x7f140345
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f140346
int style Widget_AppCompat_Light_SearchView 0x7f140347
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f140348
int style Widget_AppCompat_ListMenuView 0x7f140349
int style Widget_AppCompat_ListPopupWindow 0x7f14034a
int style Widget_AppCompat_ListView 0x7f14034b
int style Widget_AppCompat_ListView_DropDown 0x7f14034c
int style Widget_AppCompat_ListView_Menu 0x7f14034d
int style Widget_AppCompat_PopupMenu 0x7f14034e
int style Widget_AppCompat_PopupMenu_Overflow 0x7f14034f
int style Widget_AppCompat_PopupWindow 0x7f140350
int style Widget_AppCompat_ProgressBar 0x7f140351
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f140352
int style Widget_AppCompat_RatingBar 0x7f140353
int style Widget_AppCompat_RatingBar_Indicator 0x7f140354
int style Widget_AppCompat_RatingBar_Small 0x7f140355
int style Widget_AppCompat_SearchView 0x7f140356
int style Widget_AppCompat_SearchView_ActionBar 0x7f140357
int style Widget_AppCompat_SeekBar 0x7f140358
int style Widget_AppCompat_SeekBar_Discrete 0x7f140359
int style Widget_AppCompat_Spinner 0x7f14035a
int style Widget_AppCompat_Spinner_DropDown 0x7f14035b
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f14035c
int style Widget_AppCompat_Spinner_Underlined 0x7f14035d
int style Widget_AppCompat_TextView 0x7f14035e
int style Widget_AppCompat_TextView_SpinnerItem 0x7f14035f
int style Widget_AppCompat_Toolbar 0x7f140360
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f140361
int style Widget_Compat_NotificationActionContainer 0x7f140362
int style Widget_Compat_NotificationActionText 0x7f140363
int style Widget_Design_AppBarLayout 0x7f140364
int style Widget_Design_BottomNavigationView 0x7f140365
int style Widget_Design_BottomSheet_Modal 0x7f140366
int style Widget_Design_CollapsingToolbar 0x7f140367
int style Widget_Design_FloatingActionButton 0x7f140368
int style Widget_Design_NavigationView 0x7f140369
int style Widget_Design_ScrimInsetsFrameLayout 0x7f14036a
int style Widget_Design_Snackbar 0x7f14036b
int style Widget_Design_TabLayout 0x7f14036c
int style Widget_Design_TextInputEditText 0x7f14036d
int style Widget_Design_TextInputLayout 0x7f14036e
int style Widget_Material3_ActionBar_Solid 0x7f14036f
int style Widget_Material3_ActionMode 0x7f140370
int style Widget_Material3_AppBarLayout 0x7f140371
int style Widget_Material3_AutoCompleteTextView_FilledBox 0x7f140372
int style Widget_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f140373
int style Widget_Material3_AutoCompleteTextView_OutlinedBox 0x7f140374
int style Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f140375
int style Widget_Material3_Badge 0x7f140376
int style Widget_Material3_Badge_AdjustToBounds 0x7f140377
int style Widget_Material3_BottomAppBar 0x7f140378
int style Widget_Material3_BottomAppBar_Button_Navigation 0x7f140379
int style Widget_Material3_BottomAppBar_Legacy 0x7f14037a
int style Widget_Material3_BottomNavigation_Badge 0x7f14037b
int style Widget_Material3_BottomNavigationView 0x7f14037c
int style Widget_Material3_BottomNavigationView_ActiveIndicator 0x7f14037d
int style Widget_Material3_BottomSheet 0x7f14037e
int style Widget_Material3_BottomSheet_DragHandle 0x7f14037f
int style Widget_Material3_BottomSheet_Modal 0x7f140380
int style Widget_Material3_Button 0x7f140381
int style Widget_Material3_Button_ElevatedButton 0x7f140382
int style Widget_Material3_Button_ElevatedButton_Icon 0x7f140383
int style Widget_Material3_Button_Icon 0x7f140384
int style Widget_Material3_Button_IconButton 0x7f140385
int style Widget_Material3_Button_IconButton_Filled 0x7f140386
int style Widget_Material3_Button_IconButton_Filled_Tonal 0x7f140387
int style Widget_Material3_Button_IconButton_Outlined 0x7f140388
int style Widget_Material3_Button_OutlinedButton 0x7f140389
int style Widget_Material3_Button_OutlinedButton_Icon 0x7f14038a
int style Widget_Material3_Button_TextButton 0x7f14038b
int style Widget_Material3_Button_TextButton_Dialog 0x7f14038c
int style Widget_Material3_Button_TextButton_Dialog_Flush 0x7f14038d
int style Widget_Material3_Button_TextButton_Dialog_Icon 0x7f14038e
int style Widget_Material3_Button_TextButton_Icon 0x7f14038f
int style Widget_Material3_Button_TextButton_Snackbar 0x7f140390
int style Widget_Material3_Button_TonalButton 0x7f140391
int style Widget_Material3_Button_TonalButton_Icon 0x7f140392
int style Widget_Material3_Button_UnelevatedButton 0x7f140393
int style Widget_Material3_CardView_Elevated 0x7f140394
int style Widget_Material3_CardView_Filled 0x7f140395
int style Widget_Material3_CardView_Outlined 0x7f140396
int style Widget_Material3_CheckedTextView 0x7f140397
int style Widget_Material3_Chip_Assist 0x7f140398
int style Widget_Material3_Chip_Assist_Elevated 0x7f140399
int style Widget_Material3_Chip_Filter 0x7f14039a
int style Widget_Material3_Chip_Filter_Elevated 0x7f14039b
int style Widget_Material3_Chip_Input 0x7f14039c
int style Widget_Material3_Chip_Input_Elevated 0x7f14039d
int style Widget_Material3_Chip_Input_Icon 0x7f14039e
int style Widget_Material3_Chip_Input_Icon_Elevated 0x7f14039f
int style Widget_Material3_Chip_Suggestion 0x7f1403a0
int style Widget_Material3_Chip_Suggestion_Elevated 0x7f1403a1
int style Widget_Material3_ChipGroup 0x7f1403a2
int style Widget_Material3_CircularProgressIndicator 0x7f1403a3
int style Widget_Material3_CircularProgressIndicator_ExtraSmall 0x7f1403a4
int style Widget_Material3_CircularProgressIndicator_Legacy 0x7f1403a5
int style Widget_Material3_CircularProgressIndicator_Legacy_ExtraSmall 0x7f1403a6
int style Widget_Material3_CircularProgressIndicator_Legacy_Medium 0x7f1403a7
int style Widget_Material3_CircularProgressIndicator_Legacy_Small 0x7f1403a8
int style Widget_Material3_CircularProgressIndicator_Medium 0x7f1403a9
int style Widget_Material3_CircularProgressIndicator_Small 0x7f1403aa
int style Widget_Material3_CollapsingToolbar 0x7f1403ab
int style Widget_Material3_CollapsingToolbar_Large 0x7f1403ac
int style Widget_Material3_CollapsingToolbar_Medium 0x7f1403ad
int style Widget_Material3_CompoundButton_CheckBox 0x7f1403ae
int style Widget_Material3_CompoundButton_MaterialSwitch 0x7f1403af
int style Widget_Material3_CompoundButton_RadioButton 0x7f1403b0
int style Widget_Material3_CompoundButton_Switch 0x7f1403b1
int style Widget_Material3_DrawerLayout 0x7f1403b2
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Primary 0x7f1403b3
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary 0x7f1403b4
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Surface 0x7f1403b5
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary 0x7f1403b6
int style Widget_Material3_ExtendedFloatingActionButton_Primary 0x7f1403b7
int style Widget_Material3_ExtendedFloatingActionButton_Secondary 0x7f1403b8
int style Widget_Material3_ExtendedFloatingActionButton_Surface 0x7f1403b9
int style Widget_Material3_ExtendedFloatingActionButton_Tertiary 0x7f1403ba
int style Widget_Material3_FloatingActionButton_Large_Primary 0x7f1403bb
int style Widget_Material3_FloatingActionButton_Large_Secondary 0x7f1403bc
int style Widget_Material3_FloatingActionButton_Large_Surface 0x7f1403bd
int style Widget_Material3_FloatingActionButton_Large_Tertiary 0x7f1403be
int style Widget_Material3_FloatingActionButton_Primary 0x7f1403bf
int style Widget_Material3_FloatingActionButton_Secondary 0x7f1403c0
int style Widget_Material3_FloatingActionButton_Small_Primary 0x7f1403c1
int style Widget_Material3_FloatingActionButton_Small_Secondary 0x7f1403c2
int style Widget_Material3_FloatingActionButton_Small_Surface 0x7f1403c3
int style Widget_Material3_FloatingActionButton_Small_Tertiary 0x7f1403c4
int style Widget_Material3_FloatingActionButton_Surface 0x7f1403c5
int style Widget_Material3_FloatingActionButton_Tertiary 0x7f1403c6
int style Widget_Material3_Light_ActionBar_Solid 0x7f1403c7
int style Widget_Material3_LinearProgressIndicator 0x7f1403c8
int style Widget_Material3_LinearProgressIndicator_Legacy 0x7f1403c9
int style Widget_Material3_MaterialButtonToggleGroup 0x7f1403ca
int style Widget_Material3_MaterialCalendar 0x7f1403cb
int style Widget_Material3_MaterialCalendar_Day 0x7f1403cc
int style Widget_Material3_MaterialCalendar_Day_Invalid 0x7f1403cd
int style Widget_Material3_MaterialCalendar_Day_Selected 0x7f1403ce
int style Widget_Material3_MaterialCalendar_Day_Today 0x7f1403cf
int style Widget_Material3_MaterialCalendar_DayOfWeekLabel 0x7f1403d0
int style Widget_Material3_MaterialCalendar_DayTextView 0x7f1403d1
int style Widget_Material3_MaterialCalendar_Fullscreen 0x7f1403d2
int style Widget_Material3_MaterialCalendar_HeaderCancelButton 0x7f1403d3
int style Widget_Material3_MaterialCalendar_HeaderDivider 0x7f1403d4
int style Widget_Material3_MaterialCalendar_HeaderLayout 0x7f1403d5
int style Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen 0x7f1403d6
int style Widget_Material3_MaterialCalendar_HeaderSelection 0x7f1403d7
int style Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen 0x7f1403d8
int style Widget_Material3_MaterialCalendar_HeaderTitle 0x7f1403d9
int style Widget_Material3_MaterialCalendar_HeaderToggleButton 0x7f1403da
int style Widget_Material3_MaterialCalendar_Item 0x7f1403db
int style Widget_Material3_MaterialCalendar_MonthNavigationButton 0x7f1403dc
int style Widget_Material3_MaterialCalendar_MonthTextView 0x7f1403dd
int style Widget_Material3_MaterialCalendar_Year 0x7f1403de
int style Widget_Material3_MaterialCalendar_Year_Selected 0x7f1403df
int style Widget_Material3_MaterialCalendar_Year_Today 0x7f1403e0
int style Widget_Material3_MaterialCalendar_YearNavigationButton 0x7f1403e1
int style Widget_Material3_MaterialDivider 0x7f1403e2
int style Widget_Material3_MaterialDivider_Heavy 0x7f1403e3
int style Widget_Material3_MaterialTimePicker 0x7f1403e4
int style Widget_Material3_MaterialTimePicker_Button 0x7f1403e5
int style Widget_Material3_MaterialTimePicker_Clock 0x7f1403e6
int style Widget_Material3_MaterialTimePicker_Display 0x7f1403e7
int style Widget_Material3_MaterialTimePicker_Display_Divider 0x7f1403e8
int style Widget_Material3_MaterialTimePicker_Display_HelperText 0x7f1403e9
int style Widget_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f1403ea
int style Widget_Material3_MaterialTimePicker_Display_TextInputLayout 0x7f1403eb
int style Widget_Material3_MaterialTimePicker_ImageButton 0x7f1403ec
int style Widget_Material3_NavigationRailView 0x7f1403ed
int style Widget_Material3_NavigationRailView_ActiveIndicator 0x7f1403ee
int style Widget_Material3_NavigationRailView_Badge 0x7f1403ef
int style Widget_Material3_NavigationView 0x7f1403f0
int style Widget_Material3_PopupMenu 0x7f1403f1
int style Widget_Material3_PopupMenu_ContextMenu 0x7f1403f2
int style Widget_Material3_PopupMenu_ListPopupWindow 0x7f1403f3
int style Widget_Material3_PopupMenu_Overflow 0x7f1403f4
int style Widget_Material3_Search_ActionButton_Overflow 0x7f1403f5
int style Widget_Material3_Search_Toolbar_Button_Navigation 0x7f1403f6
int style Widget_Material3_SearchBar 0x7f1403f7
int style Widget_Material3_SearchBar_Outlined 0x7f1403f8
int style Widget_Material3_SearchView 0x7f1403f9
int style Widget_Material3_SearchView_Prefix 0x7f1403fa
int style Widget_Material3_SearchView_Toolbar 0x7f1403fb
int style Widget_Material3_SideSheet 0x7f1403fc
int style Widget_Material3_SideSheet_Detached 0x7f1403fd
int style Widget_Material3_SideSheet_Modal 0x7f1403fe
int style Widget_Material3_SideSheet_Modal_Detached 0x7f1403ff
int style Widget_Material3_Slider 0x7f140400
int style Widget_Material3_Slider_Label 0x7f140401
int style Widget_Material3_Slider_Legacy 0x7f140402
int style Widget_Material3_Slider_Legacy_Label 0x7f140403
int style Widget_Material3_Snackbar 0x7f140404
int style Widget_Material3_Snackbar_FullWidth 0x7f140405
int style Widget_Material3_Snackbar_TextView 0x7f140406
int style Widget_Material3_TabLayout 0x7f140407
int style Widget_Material3_TabLayout_OnSurface 0x7f140408
int style Widget_Material3_TabLayout_Secondary 0x7f140409
int style Widget_Material3_TextInputEditText_FilledBox 0x7f14040a
int style Widget_Material3_TextInputEditText_FilledBox_Dense 0x7f14040b
int style Widget_Material3_TextInputEditText_OutlinedBox 0x7f14040c
int style Widget_Material3_TextInputEditText_OutlinedBox_Dense 0x7f14040d
int style Widget_Material3_TextInputLayout_FilledBox 0x7f14040e
int style Widget_Material3_TextInputLayout_FilledBox_Dense 0x7f14040f
int style Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f140410
int style Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f140411
int style Widget_Material3_TextInputLayout_OutlinedBox 0x7f140412
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense 0x7f140413
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f140414
int style Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f140415
int style Widget_Material3_Toolbar 0x7f140416
int style Widget_Material3_Toolbar_OnSurface 0x7f140417
int style Widget_Material3_Toolbar_Surface 0x7f140418
int style Widget_Material3_Tooltip 0x7f140419
int style Widget_MaterialComponents_ActionBar_Primary 0x7f14041a
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f14041b
int style Widget_MaterialComponents_ActionBar_Solid 0x7f14041c
int style Widget_MaterialComponents_ActionBar_Surface 0x7f14041d
int style Widget_MaterialComponents_ActionMode 0x7f14041e
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f14041f
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f140420
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f140421
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f140422
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f140423
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f140424
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f140425
int style Widget_MaterialComponents_Badge 0x7f140426
int style Widget_MaterialComponents_BottomAppBar 0x7f140427
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f140428
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f140429
int style Widget_MaterialComponents_BottomNavigationView 0x7f14042a
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f14042b
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f14042c
int style Widget_MaterialComponents_BottomSheet 0x7f14042d
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f14042e
int style Widget_MaterialComponents_Button 0x7f14042f
int style Widget_MaterialComponents_Button_Icon 0x7f140430
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f140431
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f140432
int style Widget_MaterialComponents_Button_TextButton 0x7f140433
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f140434
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f140435
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f140436
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f140437
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f140438
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f140439
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f14043a
int style Widget_MaterialComponents_CardView 0x7f14043b
int style Widget_MaterialComponents_CheckedTextView 0x7f14043c
int style Widget_MaterialComponents_Chip_Action 0x7f14043d
int style Widget_MaterialComponents_Chip_Choice 0x7f14043e
int style Widget_MaterialComponents_Chip_Entry 0x7f14043f
int style Widget_MaterialComponents_Chip_Filter 0x7f140440
int style Widget_MaterialComponents_ChipGroup 0x7f140441
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f140442
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f140443
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f140444
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f140445
int style Widget_MaterialComponents_CollapsingToolbar 0x7f140446
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f140447
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f140448
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f140449
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f14044a
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f14044b
int style Widget_MaterialComponents_FloatingActionButton 0x7f14044c
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f14044d
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f14044e
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f14044f
int style Widget_MaterialComponents_MaterialCalendar 0x7f140450
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f140451
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f140452
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f140453
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f140454
int style Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel 0x7f140455
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f140456
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f140457
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f140458
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f140459
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f14045a
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f14045b
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen 0x7f14045c
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f14045d
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f14045e
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f14045f
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f140460
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f140461
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f140462
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f140463
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f140464
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f140465
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f140466
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f140467
int style Widget_MaterialComponents_MaterialDivider 0x7f140468
int style Widget_MaterialComponents_NavigationRailView 0x7f140469
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f14046a
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f14046b
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f14046c
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f14046d
int style Widget_MaterialComponents_NavigationView 0x7f14046e
int style Widget_MaterialComponents_PopupMenu 0x7f14046f
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f140470
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f140471
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f140472
int style Widget_MaterialComponents_ProgressIndicator 0x7f140473
int style Widget_MaterialComponents_ShapeableImageView 0x7f140474
int style Widget_MaterialComponents_Slider 0x7f140475
int style Widget_MaterialComponents_Snackbar 0x7f140476
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f140477
int style Widget_MaterialComponents_Snackbar_TextView 0x7f140478
int style Widget_MaterialComponents_TabLayout 0x7f140479
int style Widget_MaterialComponents_TabLayout_Colored 0x7f14047a
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f14047b
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f14047c
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f14047d
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f14047e
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f14047f
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f140480
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f140481
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f140482
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f140483
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f140484
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f140485
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f140486
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f140487
int style Widget_MaterialComponents_TextView 0x7f140488
int style Widget_MaterialComponents_TimePicker 0x7f140489
int style Widget_MaterialComponents_TimePicker_Button 0x7f14048a
int style Widget_MaterialComponents_TimePicker_Clock 0x7f14048b
int style Widget_MaterialComponents_TimePicker_Display 0x7f14048c
int style Widget_MaterialComponents_TimePicker_Display_Divider 0x7f14048d
int style Widget_MaterialComponents_TimePicker_Display_HelperText 0x7f14048e
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f14048f
int style Widget_MaterialComponents_TimePicker_Display_TextInputLayout 0x7f140490
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f140491
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f140492
int style Widget_MaterialComponents_Toolbar 0x7f140493
int style Widget_MaterialComponents_Toolbar_Primary 0x7f140494
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f140495
int style Widget_MaterialComponents_Toolbar_Surface 0x7f140496
int style Widget_MaterialComponents_Tooltip 0x7f140497
int style Widget_Support_CoordinatorLayout 0x7f140498
int[] styleable AbsCustomSlider { 0x7f040243 }
int styleable AbsCustomSlider_inVerticalOrientation 0
int[] styleable ActionBar { 0x7f04004f, 0x7f040056, 0x7f040057, 0x7f040133, 0x7f040134, 0x7f040135, 0x7f040136, 0x7f040137, 0x7f040138, 0x7f040161, 0x7f04017c, 0x7f04017d, 0x7f04019f, 0x7f040223, 0x7f04022b, 0x7f040231, 0x7f040232, 0x7f040236, 0x7f040245, 0x7f04025f, 0x7f0402d8, 0x7f040353, 0x7f04038d, 0x7f0403a0, 0x7f0403a1, 0x7f040416, 0x7f04041a, 0x7f04049b, 0x7f0404a9 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f04004f, 0x7f040056, 0x7f0400e0, 0x7f040223, 0x7f04041a, 0x7f0404a9 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0401be, 0x7f04024c }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f040025, 0x7f040027 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x7f04003a }
int styleable ActivityRule_alwaysExpand 0
int[] styleable AdsAttrs { 0x7f040028, 0x7f040029, 0x7f04002a }
int styleable AdsAttrs_adSize 0
int styleable AdsAttrs_adSizes 1
int styleable AdsAttrs_adUnitId 2
int[] styleable AlertDialog { 0x010100f2, 0x7f040098, 0x7f04009b, 0x7f0402cd, 0x7f0402ce, 0x7f04034f, 0x7f0403de, 0x7f0403e6 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f04019f, 0x7f0401bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f04040c }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppBarLayoutStates { 0x7f040403, 0x7f040404, 0x7f040408, 0x7f040409 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0402bd, 0x7f0402be, 0x7f0402bf }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f0403f8, 0x7f040498, 0x7f040499 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f040492, 0x7f040493, 0x7f040494 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f040048, 0x7f040049, 0x7f04004a, 0x7f04004b, 0x7f04004c, 0x7f040189, 0x7f04018a, 0x7f04018b, 0x7f04018c, 0x7f04018e, 0x7f04018f, 0x7f040190, 0x7f040191, 0x7f0401a3, 0x7f0401e1, 0x7f04020d, 0x7f040216, 0x7f04027c, 0x7f0402c6, 0x7f04044b, 0x7f04047c }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040000, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000e, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020, 0x7f040026, 0x7f04002d, 0x7f04002e, 0x7f04002f, 0x7f040030, 0x7f040046, 0x7f04007d, 0x7f040090, 0x7f040091, 0x7f040092, 0x7f040093, 0x7f040094, 0x7f04009d, 0x7f04009e, 0x7f0400b0, 0x7f0400bb, 0x7f0400ed, 0x7f0400ee, 0x7f0400ef, 0x7f0400f1, 0x7f0400f2, 0x7f0400f3, 0x7f0400f4, 0x7f04010d, 0x7f04010f, 0x7f040125, 0x7f040142, 0x7f040173, 0x7f040178, 0x7f040179, 0x7f04017f, 0x7f040184, 0x7f040196, 0x7f040197, 0x7f04019b, 0x7f04019c, 0x7f04019e, 0x7f040231, 0x7f040242, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cf, 0x7f0402d0, 0x7f0402d1, 0x7f0402d2, 0x7f0402d3, 0x7f0402d4, 0x7f0402d5, 0x7f0402d6, 0x7f0402d7, 0x7f040370, 0x7f040371, 0x7f040372, 0x7f04038c, 0x7f04038e, 0x7f0403a5, 0x7f0403a7, 0x7f0403a8, 0x7f0403a9, 0x7f0403bc, 0x7f0403c1, 0x7f0403c3, 0x7f0403c4, 0x7f0403f1, 0x7f0403f2, 0x7f040426, 0x7f040462, 0x7f040464, 0x7f040465, 0x7f040466, 0x7f040468, 0x7f040469, 0x7f04046a, 0x7f04046b, 0x7f040471, 0x7f040472, 0x7f0404ac, 0x7f0404ad, 0x7f0404af, 0x7f0404b0, 0x7f0404d6, 0x7f0404e1, 0x7f0404e2, 0x7f0404e3, 0x7f0404e4, 0x7f0404e5, 0x7f0404e6, 0x7f0404e7, 0x7f0404e8, 0x7f0404e9, 0x7f0404ea }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable BackgroundStyle { 0x0101030e, 0x7f0403c3 }
int styleable BackgroundStyle_android_selectableItemBackground 0
int styleable BackgroundStyle_selectableItemBackground 1
int[] styleable Badge { 0x7f040045, 0x7f040050, 0x7f04005a, 0x7f04005b, 0x7f04005c, 0x7f04005d, 0x7f04005e, 0x7f040060, 0x7f040061, 0x7f040062, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f040068, 0x7f040069, 0x7f04006a, 0x7f040233, 0x7f040234, 0x7f04027b, 0x7f040312, 0x7f040316, 0x7f04035a, 0x7f04035c, 0x7f0404d4, 0x7f0404d5 }
int styleable Badge_autoAdjustToWithinGrandparentBounds 0
int styleable Badge_backgroundColor 1
int styleable Badge_badgeGravity 2
int styleable Badge_badgeHeight 3
int styleable Badge_badgeRadius 4
int styleable Badge_badgeShapeAppearance 5
int styleable Badge_badgeShapeAppearanceOverlay 6
int styleable Badge_badgeText 7
int styleable Badge_badgeTextAppearance 8
int styleable Badge_badgeTextColor 9
int styleable Badge_badgeVerticalPadding 10
int styleable Badge_badgeWidePadding 11
int styleable Badge_badgeWidth 12
int styleable Badge_badgeWithTextHeight 13
int styleable Badge_badgeWithTextRadius 14
int styleable Badge_badgeWithTextShapeAppearance 15
int styleable Badge_badgeWithTextShapeAppearanceOverlay 16
int styleable Badge_badgeWithTextWidth 17
int styleable Badge_horizontalOffset 18
int styleable Badge_horizontalOffsetWithText 19
int styleable Badge_largeFontVerticalOffsetAdjustment 20
int styleable Badge_maxCharacterCount 21
int styleable Badge_maxNumber 22
int styleable Badge_number 23
int styleable Badge_offsetAlignmentMode 24
int styleable Badge_verticalOffset 25
int styleable Badge_verticalOffsetWithText 26
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f040228, 0x7f040246, 0x7f04024b, 0x7f04031f, 0x7f0403d5, 0x7f0403d7, 0x7f0404b8, 0x7f0404bb, 0x7f0404c2 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_indicatorTrackGapSize 3
int styleable BaseProgressIndicator_minHideDelay 4
int styleable BaseProgressIndicator_showAnimationBehavior 5
int styleable BaseProgressIndicator_showDelay 6
int styleable BaseProgressIndicator_trackColor 7
int styleable BaseProgressIndicator_trackCornerRadius 8
int styleable BaseProgressIndicator_trackThickness 9
int[] styleable BottomAppBar { 0x7f04002b, 0x7f040058, 0x7f04019f, 0x7f0401d1, 0x7f0401d2, 0x7f0401d3, 0x7f0401d4, 0x7f0401d5, 0x7f0401d6, 0x7f0401d7, 0x7f04022c, 0x7f04031b, 0x7f040352, 0x7f040368, 0x7f04036a, 0x7f04036b, 0x7f0403af }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x01010140, 0x7f04012b, 0x7f040259, 0x7f0403c7, 0x7f0403cf }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_shapeAppearance 3
int styleable BottomNavigationView_shapeAppearanceOverlay 4
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f040058, 0x7f040071, 0x7f040072, 0x7f040073, 0x7f040074, 0x7f040075, 0x7f040077, 0x7f040078, 0x7f040079, 0x7f04007a, 0x7f04021e, 0x7f0402dd, 0x7f0402de, 0x7f0402df, 0x7f040368, 0x7f04036a, 0x7f04036b, 0x7f04036f, 0x7f0403c7, 0x7f0403cf, 0x7f0403d4 }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_elevation 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x7f040034 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0403a4, 0x7f0403d2 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f0400a1, 0x7f0400a2, 0x7f0400a3, 0x7f0400a5, 0x7f0400a6, 0x7f0400a7, 0x7f040139, 0x7f04013a, 0x7f04013c, 0x7f04013d, 0x7f04013f }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x7f0400a9 }
int styleable Carousel_carousel_alignment 0
int[] styleable CheckBoxPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x7f04017b, 0x7f040420, 0x7f040421 }
int styleable CheckBoxPreference_android_summaryOn 0
int styleable CheckBoxPreference_android_summaryOff 1
int styleable CheckBoxPreference_android_disableDependentsState 2
int styleable CheckBoxPreference_disableDependentsState 3
int styleable CheckBoxPreference_summaryOff 4
int styleable CheckBoxPreference_summaryOn 5
int[] styleable CheckedTextView { 0x01010108, 0x7f0400ad, 0x7f0400ae, 0x7f0400af }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0400b3, 0x7f0400b4, 0x7f0400b8, 0x7f0400b9, 0x7f0400bc, 0x7f0400bd, 0x7f0400be, 0x7f0400c0, 0x7f0400c1, 0x7f0400c2, 0x7f0400c3, 0x7f0400c4, 0x7f0400c5, 0x7f0400c6, 0x7f0400cb, 0x7f0400cc, 0x7f0400cd, 0x7f0400cf, 0x7f0400d9, 0x7f0400da, 0x7f0400db, 0x7f0400dc, 0x7f0400dd, 0x7f0400de, 0x7f0400df, 0x7f0401b1, 0x7f040229, 0x7f040237, 0x7f04023c, 0x7f0403b1, 0x7f0403c7, 0x7f0403cf, 0x7f0403da, 0x7f040473, 0x7f04047d }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0400b2, 0x7f0400c7, 0x7f0400c8, 0x7f0400c9, 0x7f0403c5, 0x7f0403e7, 0x7f0403e9 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f040247, 0x7f040249, 0x7f04024a }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0400d5, 0x7f0400d8 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0400d6, 0x7f0402fc, 0x7f0403c6 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0400e4, 0x7f0400e5, 0x7f0400e6, 0x7f040140, 0x7f0401c1, 0x7f0401c2, 0x7f0401c3, 0x7f0401c4, 0x7f0401c5, 0x7f0401c6, 0x7f0401c7, 0x7f0401c8, 0x7f0401d0, 0x7f040218, 0x7f040315, 0x7f0403b6, 0x7f0403b8, 0x7f04040d, 0x7f04049b, 0x7f04049d, 0x7f04049e, 0x7f0404a5, 0x7f0404a8, 0x7f0404ab }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable CollapsingToolbarLayout_Layout { 0x7f040285, 0x7f040286 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorPickerPreference { 0x7f040036, 0x7f040037, 0x7f04007b, 0x7f040170, 0x7f04024d, 0x7f0402c3, 0x7f0402c4, 0x7f040380, 0x7f040381, 0x7f040382, 0x7f040383, 0x7f040384, 0x7f0404df }
int styleable ColorPickerPreference_alphaSlider 0
int styleable ColorPickerPreference_alphaSliderView 1
int styleable ColorPickerPreference_border 2
int styleable ColorPickerPreference_density 3
int styleable ColorPickerPreference_initialColor 4
int styleable ColorPickerPreference_lightnessSlider 5
int styleable ColorPickerPreference_lightnessSliderView 6
int styleable ColorPickerPreference_pickerButtonCancel 7
int styleable ColorPickerPreference_pickerButtonOk 8
int styleable ColorPickerPreference_pickerColorEdit 9
int styleable ColorPickerPreference_pickerColorEditTextColor 10
int styleable ColorPickerPreference_pickerTitle 11
int styleable ColorPickerPreference_wheelType 12
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f040035, 0x7f040277 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f040095, 0x7f04009f, 0x7f0400a0 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f04003d, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f0400ab, 0x7f04012f, 0x7f040188, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040204, 0x7f040205, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04020a, 0x7f04020b, 0x7f040287, 0x7f040288, 0x7f040289, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f04028d, 0x7f04028e, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f040349, 0x7f04034a, 0x7f040378, 0x7f040385, 0x7f0404c6, 0x7f0404c8, 0x7f0404d7 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animate_relativeTo 27
int styleable Constraint_barrierAllowsGoneWidgets 28
int styleable Constraint_barrierDirection 29
int styleable Constraint_barrierMargin 30
int styleable Constraint_chainUseRtl 31
int styleable Constraint_constraint_referenced_ids 32
int styleable Constraint_drawPath 33
int styleable Constraint_flow_firstHorizontalBias 34
int styleable Constraint_flow_firstHorizontalStyle 35
int styleable Constraint_flow_firstVerticalBias 36
int styleable Constraint_flow_firstVerticalStyle 37
int styleable Constraint_flow_horizontalAlign 38
int styleable Constraint_flow_horizontalBias 39
int styleable Constraint_flow_horizontalGap 40
int styleable Constraint_flow_horizontalStyle 41
int styleable Constraint_flow_lastHorizontalBias 42
int styleable Constraint_flow_lastHorizontalStyle 43
int styleable Constraint_flow_lastVerticalBias 44
int styleable Constraint_flow_lastVerticalStyle 45
int styleable Constraint_flow_maxElementsWrap 46
int styleable Constraint_flow_verticalAlign 47
int styleable Constraint_flow_verticalBias 48
int styleable Constraint_flow_verticalGap 49
int styleable Constraint_flow_verticalStyle 50
int styleable Constraint_flow_wrapMode 51
int styleable Constraint_layout_constrainedHeight 52
int styleable Constraint_layout_constrainedWidth 53
int styleable Constraint_layout_constraintBaseline_creator 54
int styleable Constraint_layout_constraintBaseline_toBaselineOf 55
int styleable Constraint_layout_constraintBottom_creator 56
int styleable Constraint_layout_constraintBottom_toBottomOf 57
int styleable Constraint_layout_constraintBottom_toTopOf 58
int styleable Constraint_layout_constraintCircle 59
int styleable Constraint_layout_constraintCircleAngle 60
int styleable Constraint_layout_constraintCircleRadius 61
int styleable Constraint_layout_constraintDimensionRatio 62
int styleable Constraint_layout_constraintEnd_toEndOf 63
int styleable Constraint_layout_constraintEnd_toStartOf 64
int styleable Constraint_layout_constraintGuide_begin 65
int styleable Constraint_layout_constraintGuide_end 66
int styleable Constraint_layout_constraintGuide_percent 67
int styleable Constraint_layout_constraintHeight_default 68
int styleable Constraint_layout_constraintHeight_max 69
int styleable Constraint_layout_constraintHeight_min 70
int styleable Constraint_layout_constraintHeight_percent 71
int styleable Constraint_layout_constraintHorizontal_bias 72
int styleable Constraint_layout_constraintHorizontal_chainStyle 73
int styleable Constraint_layout_constraintHorizontal_weight 74
int styleable Constraint_layout_constraintLeft_creator 75
int styleable Constraint_layout_constraintLeft_toLeftOf 76
int styleable Constraint_layout_constraintLeft_toRightOf 77
int styleable Constraint_layout_constraintRight_creator 78
int styleable Constraint_layout_constraintRight_toLeftOf 79
int styleable Constraint_layout_constraintRight_toRightOf 80
int styleable Constraint_layout_constraintStart_toEndOf 81
int styleable Constraint_layout_constraintStart_toStartOf 82
int styleable Constraint_layout_constraintTag 83
int styleable Constraint_layout_constraintTop_creator 84
int styleable Constraint_layout_constraintTop_toBottomOf 85
int styleable Constraint_layout_constraintTop_toTopOf 86
int styleable Constraint_layout_constraintVertical_bias 87
int styleable Constraint_layout_constraintVertical_chainStyle 88
int styleable Constraint_layout_constraintVertical_weight 89
int styleable Constraint_layout_constraintWidth_default 90
int styleable Constraint_layout_constraintWidth_max 91
int styleable Constraint_layout_constraintWidth_min 92
int styleable Constraint_layout_constraintWidth_percent 93
int styleable Constraint_layout_editor_absoluteX 94
int styleable Constraint_layout_editor_absoluteY 95
int styleable Constraint_layout_goneMarginBottom 96
int styleable Constraint_layout_goneMarginEnd 97
int styleable Constraint_layout_goneMarginLeft 98
int styleable Constraint_layout_goneMarginRight 99
int styleable Constraint_layout_goneMarginStart 100
int styleable Constraint_layout_goneMarginTop 101
int styleable Constraint_motionProgress 102
int styleable Constraint_motionStagger 103
int styleable Constraint_pathMotionArc 104
int styleable Constraint_pivotAnchor 105
int styleable Constraint_transitionEasing 106
int styleable Constraint_transitionPathRotate 107
int styleable Constraint_visibilityMode 108
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x01010440, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f0400ab, 0x7f04012c, 0x7f04012f, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040204, 0x7f040205, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04020a, 0x7f04020b, 0x7f04027f, 0x7f040287, 0x7f040288, 0x7f040289, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f04028d, 0x7f04028e, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f0402bc }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_maxWidth 7
int styleable ConstraintLayout_Layout_android_maxHeight 8
int styleable ConstraintLayout_Layout_android_minWidth 9
int styleable ConstraintLayout_Layout_android_minHeight 10
int styleable ConstraintLayout_Layout_android_paddingStart 11
int styleable ConstraintLayout_Layout_android_paddingEnd 12
int styleable ConstraintLayout_Layout_android_elevation 13
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 14
int styleable ConstraintLayout_Layout_barrierDirection 15
int styleable ConstraintLayout_Layout_barrierMargin 16
int styleable ConstraintLayout_Layout_chainUseRtl 17
int styleable ConstraintLayout_Layout_constraintSet 18
int styleable ConstraintLayout_Layout_constraint_referenced_ids 19
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 20
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 21
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 22
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 23
int styleable ConstraintLayout_Layout_flow_horizontalAlign 24
int styleable ConstraintLayout_Layout_flow_horizontalBias 25
int styleable ConstraintLayout_Layout_flow_horizontalGap 26
int styleable ConstraintLayout_Layout_flow_horizontalStyle 27
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 28
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 29
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 30
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 31
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 32
int styleable ConstraintLayout_Layout_flow_verticalAlign 33
int styleable ConstraintLayout_Layout_flow_verticalBias 34
int styleable ConstraintLayout_Layout_flow_verticalGap 35
int styleable ConstraintLayout_Layout_flow_verticalStyle 36
int styleable ConstraintLayout_Layout_flow_wrapMode 37
int styleable ConstraintLayout_Layout_layoutDescription 38
int styleable ConstraintLayout_Layout_layout_constrainedHeight 39
int styleable ConstraintLayout_Layout_layout_constrainedWidth 40
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 41
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 42
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 43
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 44
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 45
int styleable ConstraintLayout_Layout_layout_constraintCircle 46
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 47
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 48
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 49
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 50
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 51
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 52
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 53
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 54
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 55
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 56
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 57
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 58
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 59
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 60
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 61
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 62
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 63
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 64
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 65
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 66
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 67
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 68
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 69
int styleable ConstraintLayout_Layout_layout_constraintTag 70
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 71
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 72
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 73
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 74
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 75
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 76
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 77
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 78
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 79
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 80
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 81
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 82
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 83
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 84
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 85
int styleable ConstraintLayout_Layout_layout_goneMarginRight 86
int styleable ConstraintLayout_Layout_layout_goneMarginStart 87
int styleable ConstraintLayout_Layout_layout_goneMarginTop 88
int styleable ConstraintLayout_Layout_layout_optimizationLevel 89
int[] styleable ConstraintLayout_placeholder { 0x7f040131, 0x7f04038a }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f04003d, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f0400ab, 0x7f04012f, 0x7f040172, 0x7f040188, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040204, 0x7f040205, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04020a, 0x7f04020b, 0x7f040287, 0x7f040288, 0x7f040289, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f04028d, 0x7f04028e, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f040349, 0x7f04034a, 0x7f040378, 0x7f040385, 0x7f0404c6, 0x7f0404c8 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animate_relativeTo 29
int styleable ConstraintSet_barrierAllowsGoneWidgets 30
int styleable ConstraintSet_barrierDirection 31
int styleable ConstraintSet_barrierMargin 32
int styleable ConstraintSet_chainUseRtl 33
int styleable ConstraintSet_constraint_referenced_ids 34
int styleable ConstraintSet_deriveConstraintsFrom 35
int styleable ConstraintSet_drawPath 36
int styleable ConstraintSet_flow_firstHorizontalBias 37
int styleable ConstraintSet_flow_firstHorizontalStyle 38
int styleable ConstraintSet_flow_firstVerticalBias 39
int styleable ConstraintSet_flow_firstVerticalStyle 40
int styleable ConstraintSet_flow_horizontalAlign 41
int styleable ConstraintSet_flow_horizontalBias 42
int styleable ConstraintSet_flow_horizontalGap 43
int styleable ConstraintSet_flow_horizontalStyle 44
int styleable ConstraintSet_flow_lastHorizontalBias 45
int styleable ConstraintSet_flow_lastHorizontalStyle 46
int styleable ConstraintSet_flow_lastVerticalBias 47
int styleable ConstraintSet_flow_lastVerticalStyle 48
int styleable ConstraintSet_flow_maxElementsWrap 49
int styleable ConstraintSet_flow_verticalAlign 50
int styleable ConstraintSet_flow_verticalBias 51
int styleable ConstraintSet_flow_verticalGap 52
int styleable ConstraintSet_flow_verticalStyle 53
int styleable ConstraintSet_flow_wrapMode 54
int styleable ConstraintSet_layout_constrainedHeight 55
int styleable ConstraintSet_layout_constrainedWidth 56
int styleable ConstraintSet_layout_constraintBaseline_creator 57
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 58
int styleable ConstraintSet_layout_constraintBottom_creator 59
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 60
int styleable ConstraintSet_layout_constraintBottom_toTopOf 61
int styleable ConstraintSet_layout_constraintCircle 62
int styleable ConstraintSet_layout_constraintCircleAngle 63
int styleable ConstraintSet_layout_constraintCircleRadius 64
int styleable ConstraintSet_layout_constraintDimensionRatio 65
int styleable ConstraintSet_layout_constraintEnd_toEndOf 66
int styleable ConstraintSet_layout_constraintEnd_toStartOf 67
int styleable ConstraintSet_layout_constraintGuide_begin 68
int styleable ConstraintSet_layout_constraintGuide_end 69
int styleable ConstraintSet_layout_constraintGuide_percent 70
int styleable ConstraintSet_layout_constraintHeight_default 71
int styleable ConstraintSet_layout_constraintHeight_max 72
int styleable ConstraintSet_layout_constraintHeight_min 73
int styleable ConstraintSet_layout_constraintHeight_percent 74
int styleable ConstraintSet_layout_constraintHorizontal_bias 75
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 76
int styleable ConstraintSet_layout_constraintHorizontal_weight 77
int styleable ConstraintSet_layout_constraintLeft_creator 78
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 79
int styleable ConstraintSet_layout_constraintLeft_toRightOf 80
int styleable ConstraintSet_layout_constraintRight_creator 81
int styleable ConstraintSet_layout_constraintRight_toLeftOf 82
int styleable ConstraintSet_layout_constraintRight_toRightOf 83
int styleable ConstraintSet_layout_constraintStart_toEndOf 84
int styleable ConstraintSet_layout_constraintStart_toStartOf 85
int styleable ConstraintSet_layout_constraintTag 86
int styleable ConstraintSet_layout_constraintTop_creator 87
int styleable ConstraintSet_layout_constraintTop_toBottomOf 88
int styleable ConstraintSet_layout_constraintTop_toTopOf 89
int styleable ConstraintSet_layout_constraintVertical_bias 90
int styleable ConstraintSet_layout_constraintVertical_chainStyle 91
int styleable ConstraintSet_layout_constraintVertical_weight 92
int styleable ConstraintSet_layout_constraintWidth_default 93
int styleable ConstraintSet_layout_constraintWidth_max 94
int styleable ConstraintSet_layout_constraintWidth_min 95
int styleable ConstraintSet_layout_constraintWidth_percent 96
int styleable ConstraintSet_layout_editor_absoluteX 97
int styleable ConstraintSet_layout_editor_absoluteY 98
int styleable ConstraintSet_layout_goneMarginBottom 99
int styleable ConstraintSet_layout_goneMarginEnd 100
int styleable ConstraintSet_layout_goneMarginLeft 101
int styleable ConstraintSet_layout_goneMarginRight 102
int styleable ConstraintSet_layout_goneMarginStart 103
int styleable ConstraintSet_layout_goneMarginTop 104
int styleable ConstraintSet_motionProgress 105
int styleable ConstraintSet_motionStagger 106
int styleable ConstraintSet_pathMotionArc 107
int styleable ConstraintSet_pivotAnchor 108
int styleable ConstraintSet_transitionEasing 109
int styleable ConstraintSet_transitionPathRotate 110
int[] styleable CoordinatorLayout { 0x7f040276, 0x7f04040b }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f040282, 0x7f040283, 0x7f040284, 0x7f0402b1, 0x7f0402ba, 0x7f0402bb }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f040044, 0x7f04015b, 0x7f04015c, 0x7f04015d, 0x7f04015e, 0x7f04015f, 0x7f040160, 0x7f040162, 0x7f040163 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customStringValue 8
int[] styleable DialogPreference { 0x010101f2, 0x010101f3, 0x010101f4, 0x010101f5, 0x010101f6, 0x010101f7, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f04017a, 0x7f040356, 0x7f04038f }
int styleable DialogPreference_android_dialogTitle 0
int styleable DialogPreference_android_dialogMessage 1
int styleable DialogPreference_android_dialogIcon 2
int styleable DialogPreference_android_positiveButtonText 3
int styleable DialogPreference_android_negativeButtonText 4
int styleable DialogPreference_android_dialogLayout 5
int styleable DialogPreference_dialogIcon 6
int styleable DialogPreference_dialogLayout 7
int styleable DialogPreference_dialogMessage 8
int styleable DialogPreference_dialogTitle 9
int styleable DialogPreference_negativeButtonText 10
int styleable DialogPreference_positiveButtonText 11
int[] styleable DrawerArrowToggle { 0x7f040042, 0x7f040043, 0x7f04006b, 0x7f0400ec, 0x7f04018d, 0x7f04021d, 0x7f0403f0, 0x7f04047f }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f04019f }
int styleable DrawerLayout_elevation 0
int[] styleable EditTextPreference { 0x7f0404d2 }
int styleable EditTextPreference_useSimpleSummaryProvider 0
int[] styleable ExtendedFloatingActionButton { 0x7f0400e3, 0x7f04019f, 0x7f0401c9, 0x7f0401ca, 0x7f040229, 0x7f0403da, 0x7f0403df }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f04006f, 0x7f040070 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FillableLoader { 0x7f0401e2, 0x7f0401e3, 0x7f0401e4, 0x7f0401e5, 0x7f0401e6, 0x7f0401e7, 0x7f0401e8, 0x7f0401e9 }
int styleable FillableLoader_fl_clippingTransform 0
int styleable FillableLoader_fl_fillColor 1
int styleable FillableLoader_fl_fillDuration 2
int styleable FillableLoader_fl_originalHeight 3
int styleable FillableLoader_fl_originalWidth 4
int styleable FillableLoader_fl_strokeColor 5
int styleable FillableLoader_fl_strokeDrawingDuration 6
int styleable FillableLoader_fl_strokeWidth 7
int[] styleable FloatingActionButton { 0x0101000e, 0x7f040058, 0x7f040059, 0x7f04007c, 0x7f04019f, 0x7f0401b1, 0x7f0401d8, 0x7f0401d9, 0x7f040229, 0x7f040235, 0x7f040314, 0x7f04039e, 0x7f0403b1, 0x7f0403c7, 0x7f0403cf, 0x7f0403da, 0x7f0404cf }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f04006f }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f04026a, 0x7f0402c7 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f040211, 0x7f040212, 0x7f040213, 0x7f040214 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f04020c, 0x7f040215, 0x7f040216, 0x7f040217, 0x7f0404cd }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f04021a }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f040039, 0x7f04008f, 0x7f040141, 0x7f040156, 0x7f040366, 0x7f0403b2, 0x7f0403b3, 0x7f0403b4, 0x7f0404d9 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_brightness 1
int styleable ImageFilterView_contrast 2
int styleable ImageFilterView_crossfade 3
int styleable ImageFilterView_overlay 4
int styleable ImageFilterView_round 5
int styleable ImageFilterView_roundPercent 6
int styleable ImageFilterView_saturation 7
int styleable ImageFilterView_warmth 8
int[] styleable Insets { 0x7f0402dd, 0x7f0402de, 0x7f0402df, 0x7f040368, 0x7f04036a, 0x7f04036b, 0x7f04036d, 0x7f04036f }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingStartSystemWindowInsets 6
int styleable Insets_paddingTopSystemWindowInsets 7
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04015a, 0x7f04021c, 0x7f040349, 0x7f04034b, 0x7f0404c6, 0x7f0404c8 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transitionEasing 16
int styleable KeyAttribute_transitionPathRotate 17
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04015a, 0x7f04021c, 0x7f040349, 0x7f04034b, 0x7f0404c6, 0x7f0404c8, 0x7f0404db, 0x7f0404dc, 0x7f0404dd, 0x7f0404de }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_waveShape 18
int styleable KeyCycle_waveVariesBy 19
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f04015a, 0x7f040188, 0x7f04021c, 0x7f040274, 0x7f04034b, 0x7f040378, 0x7f04037a, 0x7f04037b, 0x7f04037c, 0x7f04037d, 0x7f0403ea, 0x7f0404c6 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04015a, 0x7f04021c, 0x7f040349, 0x7f04034b, 0x7f0404c6, 0x7f0404c8, 0x7f0404da, 0x7f0404db, 0x7f0404dc, 0x7f0404dd }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_waveShape 19
int[] styleable KeyTrigger { 0x7f04021c, 0x7f04034b, 0x7f04034c, 0x7f04034d, 0x7f04035d, 0x7f04035f, 0x7f040360, 0x7f0404ca, 0x7f0404cb, 0x7f0404cc }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f0400ab, 0x7f04012f, 0x7f040287, 0x7f040288, 0x7f040289, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f04028d, 0x7f04028e, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f040294, 0x7f040295, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f040313, 0x7f040318, 0x7f04031e, 0x7f040322 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_layout_constrainedHeight 14
int styleable Layout_layout_constrainedWidth 15
int styleable Layout_layout_constraintBaseline_creator 16
int styleable Layout_layout_constraintBaseline_toBaselineOf 17
int styleable Layout_layout_constraintBottom_creator 18
int styleable Layout_layout_constraintBottom_toBottomOf 19
int styleable Layout_layout_constraintBottom_toTopOf 20
int styleable Layout_layout_constraintCircle 21
int styleable Layout_layout_constraintCircleAngle 22
int styleable Layout_layout_constraintCircleRadius 23
int styleable Layout_layout_constraintDimensionRatio 24
int styleable Layout_layout_constraintEnd_toEndOf 25
int styleable Layout_layout_constraintEnd_toStartOf 26
int styleable Layout_layout_constraintGuide_begin 27
int styleable Layout_layout_constraintGuide_end 28
int styleable Layout_layout_constraintGuide_percent 29
int styleable Layout_layout_constraintHeight_default 30
int styleable Layout_layout_constraintHeight_max 31
int styleable Layout_layout_constraintHeight_min 32
int styleable Layout_layout_constraintHeight_percent 33
int styleable Layout_layout_constraintHorizontal_bias 34
int styleable Layout_layout_constraintHorizontal_chainStyle 35
int styleable Layout_layout_constraintHorizontal_weight 36
int styleable Layout_layout_constraintLeft_creator 37
int styleable Layout_layout_constraintLeft_toLeftOf 38
int styleable Layout_layout_constraintLeft_toRightOf 39
int styleable Layout_layout_constraintRight_creator 40
int styleable Layout_layout_constraintRight_toLeftOf 41
int styleable Layout_layout_constraintRight_toRightOf 42
int styleable Layout_layout_constraintStart_toEndOf 43
int styleable Layout_layout_constraintStart_toStartOf 44
int styleable Layout_layout_constraintTop_creator 45
int styleable Layout_layout_constraintTop_toBottomOf 46
int styleable Layout_layout_constraintTop_toTopOf 47
int styleable Layout_layout_constraintVertical_bias 48
int styleable Layout_layout_constraintVertical_chainStyle 49
int styleable Layout_layout_constraintVertical_weight 50
int styleable Layout_layout_constraintWidth_default 51
int styleable Layout_layout_constraintWidth_max 52
int styleable Layout_layout_constraintWidth_min 53
int styleable Layout_layout_constraintWidth_percent 54
int styleable Layout_layout_editor_absoluteX 55
int styleable Layout_layout_editor_absoluteY 56
int styleable Layout_layout_goneMarginBottom 57
int styleable Layout_layout_goneMarginEnd 58
int styleable Layout_layout_goneMarginLeft 59
int styleable Layout_layout_goneMarginRight 60
int styleable Layout_layout_goneMarginStart 61
int styleable Layout_layout_goneMarginTop 62
int styleable Layout_maxHeight 63
int styleable Layout_maxWidth 64
int styleable Layout_minHeight 65
int styleable Layout_minWidth 66
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f04017d, 0x7f040182, 0x7f040319, 0x7f0403d8 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f040244, 0x7f040248, 0x7f0404c1 }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int styleable LinearProgressIndicator_trackStopIndicatorSize 2
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable ListPreference { 0x010100b2, 0x010101f8, 0x7f0401b2, 0x7f0401b3, 0x7f0404d2 }
int styleable ListPreference_android_entries 0
int styleable ListPreference_android_entryValues 1
int styleable ListPreference_entries 2
int styleable ListPreference_entryValues 3
int styleable ListPreference_useSimpleSummaryProvider 4
int[] styleable LoadingImageView { 0x7f0400d0, 0x7f040240, 0x7f040241 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MaterialAlertDialog { 0x7f040051, 0x7f040052, 0x7f040053, 0x7f040054, 0x7f040058 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int styleable MaterialAlertDialog_backgroundTint 4
int[] styleable MaterialAlertDialogTheme { 0x7f0402e0, 0x7f0402e1, 0x7f0402e2, 0x7f0402e3, 0x7f0402e4, 0x7f0402e5 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int[] styleable MaterialAutoCompleteTextView { 0x01010220, 0x0101048c, 0x7f040195, 0x7f0403e2, 0x7f0403e3, 0x7f0403e4, 0x7f0403e5 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_dropDownBackgroundTint 2
int styleable MaterialAutoCompleteTextView_simpleItemLayout 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 4
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 5
int styleable MaterialAutoCompleteTextView_simpleItems 6
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f040058, 0x7f040059, 0x7f04014a, 0x7f04019f, 0x7f040236, 0x7f040238, 0x7f040239, 0x7f04023a, 0x7f04023d, 0x7f04023e, 0x7f0403b1, 0x7f0403c7, 0x7f0403cf, 0x7f04040e, 0x7f04040f, 0x7f0404aa }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialButtonToggleGroup { 0x0101000e, 0x7f0400b1, 0x7f0403c5, 0x7f0403e9 }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int[] styleable MaterialCalendar { 0x0101020d, 0x7f040058, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167, 0x7f040359, 0x7f0403a6, 0x7f0404eb, 0x7f0404ec, 0x7f0404ed }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_backgroundTint 1
int styleable MaterialCalendar_dayInvalidStyle 2
int styleable MaterialCalendar_daySelectedStyle 3
int styleable MaterialCalendar_dayStyle 4
int styleable MaterialCalendar_dayTodayStyle 5
int styleable MaterialCalendar_nestedScrollable 6
int styleable MaterialCalendar_rangeFillColor 7
int styleable MaterialCalendar_yearSelectedStyle 8
int styleable MaterialCalendar_yearStyle 9
int styleable MaterialCalendar_yearTodayStyle 10
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f040257, 0x7f040263, 0x7f040264, 0x7f04026b, 0x7f04026c, 0x7f040271 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f0400a4, 0x7f0400b3, 0x7f0400b5, 0x7f0400b6, 0x7f0400b7, 0x7f0400b8, 0x7f0403b1, 0x7f0403c7, 0x7f0403cf, 0x7f040405, 0x7f04040e, 0x7f04040f }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x01010107, 0x7f040095, 0x7f040097, 0x7f040099, 0x7f04009a, 0x7f04009f, 0x7f0400aa, 0x7f0400ba, 0x7f0401b4, 0x7f0401bb, 0x7f0404d1 }
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialCheckBoxStates { 0x7f040406, 0x7f040407 }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int[] styleable MaterialDivider { 0x7f04017e, 0x7f040180, 0x7f040181, 0x7f040183, 0x7f04027d }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x7f04009f, 0x7f0404d1 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f0403c7, 0x7f0403cf }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x7f040483, 0x7f040484, 0x7f040485, 0x7f040486, 0x7f0404bc, 0x7f0404bd, 0x7f0404be }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconSize 1
int styleable MaterialSwitch_thumbIconTint 2
int styleable MaterialSwitch_thumbIconTintMode 3
int styleable MaterialSwitch_trackDecoration 4
int styleable MaterialSwitch_trackDecorationTint 5
int styleable MaterialSwitch_trackDecorationTintMode 6
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f0402c6 }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f0402c6 }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f040058, 0x7f0400d7, 0x7f040275 }
int styleable MaterialTimePicker_backgroundTint 0
int styleable MaterialTimePicker_clockIcon 1
int styleable MaterialTimePicker_keyboardIcon 2
int[] styleable MaterialToolbar { 0x7f0402d9, 0x7f0402db, 0x7f040352, 0x7f040417, 0x7f04049c }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000d, 0x7f040021, 0x7f040023, 0x7f040038, 0x7f040132, 0x7f04023d, 0x7f04023e, 0x7f04035b, 0x7f0403d6, 0x7f0404b2 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f04039d, 0x7f040410 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f040323, 0x7f040324, 0x7f040325, 0x7f040326, 0x7f040327, 0x7f040328 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f04003d, 0x7f040188, 0x7f040348, 0x7f04034a, 0x7f040378, 0x7f0404c6 }
int styleable Motion_animate_relativeTo 0
int styleable Motion_drawPath 1
int styleable Motion_motionPathRotate 2
int styleable Motion_motionStagger 3
int styleable Motion_pathMotionArc 4
int styleable Motion_transitionEasing 5
int[] styleable MotionHelper { 0x7f04035e, 0x7f040361 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLayout { 0x7f040040, 0x7f040157, 0x7f04027f, 0x7f040329, 0x7f040349, 0x7f0403db }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f040168, 0x7f040280 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f040448, 0x7f040449, 0x7f04044a }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable MultiSelectListPreference { 0x010100b2, 0x010101f8, 0x7f0401b2, 0x7f0401b3 }
int styleable MultiSelectListPreference_android_entries 0
int styleable MultiSelectListPreference_android_entryValues 1
int styleable MultiSelectListPreference_entries 2
int styleable MultiSelectListPreference_entryValues 3
int[] styleable NavigationBarActiveIndicator { 0x01010155, 0x01010159, 0x010101a5, 0x7f0402dc, 0x7f0403c7 }
int styleable NavigationBarActiveIndicator_android_height 0
int styleable NavigationBarActiveIndicator_android_width 1
int styleable NavigationBarActiveIndicator_android_color 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x7f040024, 0x7f040058, 0x7f04019f, 0x7f040255, 0x7f040256, 0x7f04025b, 0x7f04025c, 0x7f040260, 0x7f040261, 0x7f040262, 0x7f04026e, 0x7f04026f, 0x7f040270, 0x7f040271, 0x7f04027a, 0x7f04031a }
int styleable NavigationBarView_activeIndicatorLabelPadding 0
int styleable NavigationBarView_backgroundTint 1
int styleable NavigationBarView_elevation 2
int styleable NavigationBarView_itemActiveIndicatorStyle 3
int styleable NavigationBarView_itemBackground 4
int styleable NavigationBarView_itemIconSize 5
int styleable NavigationBarView_itemIconTint 6
int styleable NavigationBarView_itemPaddingBottom 7
int styleable NavigationBarView_itemPaddingTop 8
int styleable NavigationBarView_itemRippleColor 9
int styleable NavigationBarView_itemTextAppearanceActive 10
int styleable NavigationBarView_itemTextAppearanceActiveBoldEnabled 11
int styleable NavigationBarView_itemTextAppearanceInactive 12
int styleable NavigationBarView_itemTextColor 13
int styleable NavigationBarView_labelVisibilityMode 14
int styleable NavigationBarView_menu 15
int[] styleable NavigationRailView { 0x7f040222, 0x7f04025e, 0x7f04031c, 0x7f040368, 0x7f04036d, 0x7f04036f, 0x7f0403c7, 0x7f0403cf }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingStartSystemWindowInsets 4
int styleable NavigationRailView_paddingTopSystemWindowInsets 5
int styleable NavigationRailView_shapeAppearance 6
int styleable NavigationRailView_shapeAppearanceOverlay 7
int[] styleable NavigationView { 0x010100b3, 0x010100d4, 0x010100dd, 0x0101011f, 0x7f04007f, 0x7f040180, 0x7f040181, 0x7f040193, 0x7f04019f, 0x7f040222, 0x7f040256, 0x7f040258, 0x7f04025a, 0x7f04025b, 0x7f04025c, 0x7f04025d, 0x7f040262, 0x7f040263, 0x7f040264, 0x7f040265, 0x7f040266, 0x7f040267, 0x7f040268, 0x7f040269, 0x7f04026d, 0x7f04026f, 0x7f040271, 0x7f040272, 0x7f04031a, 0x7f0403c7, 0x7f0403cf, 0x7f040411, 0x7f040412, 0x7f040413, 0x7f040414, 0x7f0404b3 }
int styleable NavigationView_android_layout_gravity 0
int styleable NavigationView_android_background 1
int styleable NavigationView_android_fitsSystemWindows 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextAppearanceActiveBoldEnabled 25
int styleable NavigationView_itemTextColor 26
int styleable NavigationView_itemVerticalPadding 27
int styleable NavigationView_menu 28
int styleable NavigationView_shapeAppearance 29
int styleable NavigationView_shapeAppearanceOverlay 30
int styleable NavigationView_subheaderColor 31
int styleable NavigationView_subheaderInsetEnd 32
int styleable NavigationView_subheaderInsetStart 33
int styleable NavigationView_subheaderTextAppearance 34
int styleable NavigationView_topInsetScrimEnabled 35
int[] styleable OnClick { 0x7f0400d4, 0x7f040447 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f040185, 0x7f040186, 0x7f040187, 0x7f0402c5, 0x7f04030f, 0x7f040317, 0x7f04034e, 0x7f040357, 0x7f040362, 0x7f0404b4, 0x7f0404b5, 0x7f0404b6 }
int styleable OnSwipe_dragDirection 0
int styleable OnSwipe_dragScale 1
int styleable OnSwipe_dragThreshold 2
int styleable OnSwipe_limitBoundsTo 3
int styleable OnSwipe_maxAcceleration 4
int styleable OnSwipe_maxVelocity 5
int styleable OnSwipe_moveWhenScrollAtTop 6
int styleable OnSwipe_nestedScrollFlags 7
int styleable OnSwipe_onTouchUp 8
int styleable OnSwipe_touchAnchorId 9
int styleable OnSwipe_touchAnchorSide 10
int styleable OnSwipe_touchRegionId 11
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f040365 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040402 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable Preference { 0x01010002, 0x0101000d, 0x0101000e, 0x010100f2, 0x010101e1, 0x010101e6, 0x010101e8, 0x010101e9, 0x010101ea, 0x010101eb, 0x010101ec, 0x010101ed, 0x010101ee, 0x010102e3, 0x0101055c, 0x01010561, 0x7f040031, 0x7f040033, 0x7f04016d, 0x7f040171, 0x7f0401a4, 0x7f0401a6, 0x7f04021b, 0x7f040236, 0x7f04023b, 0x7f040254, 0x7f040273, 0x7f04027e, 0x7f040363, 0x7f04037f, 0x7f0403c2, 0x7f0403d3, 0x7f0403e8, 0x7f04041f, 0x7f04049b, 0x7f0404e0 }
int styleable Preference_android_icon 0
int styleable Preference_android_persistent 1
int styleable Preference_android_enabled 2
int styleable Preference_android_layout 3
int styleable Preference_android_title 4
int styleable Preference_android_selectable 5
int styleable Preference_android_key 6
int styleable Preference_android_summary 7
int styleable Preference_android_order 8
int styleable Preference_android_widgetLayout 9
int styleable Preference_android_dependency 10
int styleable Preference_android_defaultValue 11
int styleable Preference_android_shouldDisableView 12
int styleable Preference_android_fragment 13
int styleable Preference_android_singleLineTitle 14
int styleable Preference_android_iconSpaceReserved 15
int styleable Preference_allowDividerAbove 16
int styleable Preference_allowDividerBelow 17
int styleable Preference_defaultValue 18
int styleable Preference_dependency 19
int styleable Preference_enableCopying 20
int styleable Preference_enabled 21
int styleable Preference_fragment 22
int styleable Preference_icon 23
int styleable Preference_iconSpaceReserved 24
int styleable Preference_isPreferenceVisible 25
int styleable Preference_key 26
int styleable Preference_layout 27
int styleable Preference_order 28
int styleable Preference_persistent 29
int styleable Preference_selectable 30
int styleable Preference_shouldDisableView 31
int styleable Preference_singleLineTitle 32
int styleable Preference_summary 33
int styleable Preference_title 34
int styleable Preference_widgetLayout 35
int[] styleable PreferenceFragment { 0x010100f2, 0x01010129, 0x0101012a, 0x7f040032 }
int styleable PreferenceFragment_android_layout 0
int styleable PreferenceFragment_android_divider 1
int styleable PreferenceFragment_android_dividerHeight 2
int styleable PreferenceFragment_allowDividerAfterLastItem 3
int[] styleable PreferenceFragmentCompat { 0x010100f2, 0x01010129, 0x0101012a, 0x7f040032 }
int styleable PreferenceFragmentCompat_android_layout 0
int styleable PreferenceFragmentCompat_android_divider 1
int styleable PreferenceFragmentCompat_android_dividerHeight 2
int styleable PreferenceFragmentCompat_allowDividerAfterLastItem 3
int[] styleable PreferenceGroup { 0x010101e7, 0x7f04024e, 0x7f040364 }
int styleable PreferenceGroup_android_orderingFromXml 0
int styleable PreferenceGroup_initialExpandedChildrenCount 1
int styleable PreferenceGroup_orderingFromXml 2
int[] styleable PreferenceImageView { 0x0101011f, 0x01010120, 0x7f040313, 0x7f040318 }
int styleable PreferenceImageView_android_maxWidth 0
int styleable PreferenceImageView_android_maxHeight 1
int styleable PreferenceImageView_maxHeight 2
int styleable PreferenceImageView_maxWidth 3
int[] styleable PreferenceTheme { 0x7f0400ac, 0x7f040177, 0x7f040198, 0x7f04019d, 0x7f040390, 0x7f040391, 0x7f040392, 0x7f040393, 0x7f040394, 0x7f040395, 0x7f040396, 0x7f040397, 0x7f040398, 0x7f040399, 0x7f0403c0, 0x7f040424, 0x7f040425 }
int styleable PreferenceTheme_checkBoxPreferenceStyle 0
int styleable PreferenceTheme_dialogPreferenceStyle 1
int styleable PreferenceTheme_dropdownPreferenceStyle 2
int styleable PreferenceTheme_editTextPreferenceStyle 3
int styleable PreferenceTheme_preferenceCategoryStyle 4
int styleable PreferenceTheme_preferenceCategoryTitleTextAppearance 5
int styleable PreferenceTheme_preferenceCategoryTitleTextColor 6
int styleable PreferenceTheme_preferenceFragmentCompatStyle 7
int styleable PreferenceTheme_preferenceFragmentListStyle 8
int styleable PreferenceTheme_preferenceFragmentStyle 9
int styleable PreferenceTheme_preferenceInformationStyle 10
int styleable PreferenceTheme_preferenceScreenStyle 11
int styleable PreferenceTheme_preferenceStyle 12
int styleable PreferenceTheme_preferenceTheme 13
int styleable PreferenceTheme_seekBarPreferenceStyle 14
int styleable PreferenceTheme_switchPreferenceCompatStyle 15
int styleable PreferenceTheme_switchPreferenceStyle 16
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f0402a6, 0x7f040349, 0x7f0404d7 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f0402fc }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f040320, 0x7f0404d3 }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f040367, 0x7f04036e }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0401da, 0x7f0401db, 0x7f0401dc, 0x7f0401dd, 0x7f0401de, 0x7f040281, 0x7f0403b0, 0x7f0403ef, 0x7f0403f9 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f04024f }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f040076 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x01010034, 0x0101014f, 0x01010150, 0x7f040058, 0x7f040169, 0x7f04016b, 0x7f04019f, 0x7f040219, 0x7f04022a, 0x7f040352, 0x7f04040e, 0x7f04040f, 0x7f04049a }
int styleable SearchBar_android_textAppearance 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_hint 2
int styleable SearchBar_backgroundTint 3
int styleable SearchBar_defaultMarginsEnabled 4
int styleable SearchBar_defaultScrollFlagsEnabled 5
int styleable SearchBar_elevation 6
int styleable SearchBar_forceDefaultNavigationOnClickListener 7
int styleable SearchBar_hideNavigationIcon 8
int styleable SearchBar_navigationIconTint 9
int styleable SearchBar_strokeColor 10
int styleable SearchBar_strokeWidth 11
int styleable SearchBar_tintNavigationIcon 12
int[] styleable SearchView { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f04003b, 0x7f04003c, 0x7f040047, 0x7f04004e, 0x7f040058, 0x7f0400d9, 0x7f04012a, 0x7f04016a, 0x7f04021f, 0x7f040222, 0x7f04022a, 0x7f04023f, 0x7f04027e, 0x7f0403a2, 0x7f0403a3, 0x7f0403b9, 0x7f0403ba, 0x7f0403bb, 0x7f040415, 0x7f04041e, 0x7f0404d0, 0x7f0404d8 }
int styleable SearchView_android_textAppearance 0
int styleable SearchView_android_focusable 1
int styleable SearchView_android_maxWidth 2
int styleable SearchView_android_text 3
int styleable SearchView_android_hint 4
int styleable SearchView_android_inputType 5
int styleable SearchView_android_imeOptions 6
int styleable SearchView_animateMenuItems 7
int styleable SearchView_animateNavigationIcon 8
int styleable SearchView_autoShowKeyboard 9
int styleable SearchView_backHandlingEnabled 10
int styleable SearchView_backgroundTint 11
int styleable SearchView_closeIcon 12
int styleable SearchView_commitIcon 13
int styleable SearchView_defaultQueryHint 14
int styleable SearchView_goIcon 15
int styleable SearchView_headerLayout 16
int styleable SearchView_hideNavigationIcon 17
int styleable SearchView_iconifiedByDefault 18
int styleable SearchView_layout 19
int styleable SearchView_queryBackground 20
int styleable SearchView_queryHint 21
int styleable SearchView_searchHintIcon 22
int styleable SearchView_searchIcon 23
int styleable SearchView_searchPrefixText 24
int styleable SearchView_submitBackground 25
int styleable SearchView_suggestionRowLayout 26
int styleable SearchView_useDrawerArrowDrawable 27
int styleable SearchView_voiceIcon 28
int[] styleable SeekBarPreference { 0x010100f2, 0x01010136, 0x7f04002c, 0x7f04031d, 0x7f0403bf, 0x7f0403dc, 0x7f0404ce }
int styleable SeekBarPreference_android_layout 0
int styleable SeekBarPreference_android_max 1
int styleable SeekBarPreference_adjustable 2
int styleable SeekBarPreference_min 3
int styleable SeekBarPreference_seekBarIncrement 4
int styleable SeekBarPreference_showSeekBarValue 5
int styleable SeekBarPreference_updatesContinuously 6
int[] styleable ShapeAppearance { 0x7f040145, 0x7f040146, 0x7f040147, 0x7f040148, 0x7f040149, 0x7f04014b, 0x7f04014c, 0x7f04014d, 0x7f04014e, 0x7f04014f }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f040139, 0x7f04013a, 0x7f04013b, 0x7f04013c, 0x7f04013d, 0x7f04013e, 0x7f04013f, 0x7f0403c7, 0x7f0403cf, 0x7f04040e, 0x7f04040f }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f040058, 0x7f040071, 0x7f040144, 0x7f0403c7, 0x7f0403cf }
int styleable SideSheetBehavior_Layout_android_maxWidth 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_elevation 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable SignInButton { 0x7f04009c, 0x7f040115, 0x7f0403b5 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f040220, 0x7f040221, 0x7f040278, 0x7f040279, 0x7f040321, 0x7f040480, 0x7f040481, 0x7f040482, 0x7f040487, 0x7f040488, 0x7f040489, 0x7f04048d, 0x7f04048e, 0x7f04048f, 0x7f040490, 0x7f040491, 0x7f040495, 0x7f040496, 0x7f040497, 0x7f0404b8, 0x7f0404b9, 0x7f0404ba, 0x7f0404bf, 0x7f0404c0, 0x7f0404c1 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbHeight 12
int styleable Slider_thumbRadius 13
int styleable Slider_thumbStrokeColor 14
int styleable Slider_thumbStrokeWidth 15
int styleable Slider_thumbTrackGapSize 16
int styleable Slider_thumbWidth 17
int styleable Slider_tickColor 18
int styleable Slider_tickColorActive 19
int styleable Slider_tickColorInactive 20
int styleable Slider_tickRadiusActive 21
int styleable Slider_tickRadiusInactive 22
int styleable Slider_tickVisible 23
int styleable Slider_trackColor 24
int styleable Slider_trackColorActive 25
int styleable Slider_trackColorInactive 26
int styleable Slider_trackHeight 27
int styleable Slider_trackInsideCornerSize 28
int styleable Slider_trackStopIndicatorSize 29
int[] styleable Snackbar { 0x7f0403ec, 0x7f0403ed, 0x7f0403ee }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f040022, 0x7f04003e, 0x7f040055, 0x7f040058, 0x7f040059, 0x7f04019f, 0x7f040310, 0x7f0403c7, 0x7f0403cf }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f04038d }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f04039f, 0x7f0403bd, 0x7f0403be }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f0400d3, 0x7f0401df, 0x7f0401e0, 0x7f0403f3, 0x7f0403f4, 0x7f0403f5, 0x7f0403f6 }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f040386, 0x7f0403f3, 0x7f0403f4, 0x7f0403f5, 0x7f0403f6 }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int[] styleable State { 0x010100d0, 0x7f040130 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f04016c }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0403dd, 0x7f0403f7, 0x7f040422, 0x7f040423, 0x7f040427, 0x7f04048a, 0x7f04048b, 0x7f04048c, 0x7f0404b7, 0x7f0404c3, 0x7f0404c4 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f0404d1 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable SwitchPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f04017b, 0x7f040420, 0x7f040421, 0x7f040428, 0x7f040429 }
int styleable SwitchPreference_android_summaryOn 0
int styleable SwitchPreference_android_summaryOff 1
int styleable SwitchPreference_android_disableDependentsState 2
int styleable SwitchPreference_android_switchTextOn 3
int styleable SwitchPreference_android_switchTextOff 4
int styleable SwitchPreference_disableDependentsState 5
int styleable SwitchPreference_summaryOff 6
int styleable SwitchPreference_summaryOn 7
int styleable SwitchPreference_switchTextOff 8
int styleable SwitchPreference_switchTextOn 9
int[] styleable SwitchPreferenceCompat { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f04017b, 0x7f040420, 0x7f040421, 0x7f040428, 0x7f040429 }
int styleable SwitchPreferenceCompat_android_summaryOn 0
int styleable SwitchPreferenceCompat_android_summaryOff 1
int styleable SwitchPreferenceCompat_android_disableDependentsState 2
int styleable SwitchPreferenceCompat_android_switchTextOn 3
int styleable SwitchPreferenceCompat_android_switchTextOff 4
int styleable SwitchPreferenceCompat_disableDependentsState 5
int styleable SwitchPreferenceCompat_summaryOff 6
int styleable SwitchPreferenceCompat_summaryOn 7
int styleable SwitchPreferenceCompat_switchTextOff 8
int styleable SwitchPreferenceCompat_switchTextOn 9
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f04042a, 0x7f04042b, 0x7f04042c, 0x7f04042d, 0x7f04042e, 0x7f04042f, 0x7f040430, 0x7f040431, 0x7f040432, 0x7f040433, 0x7f040434, 0x7f040435, 0x7f040436, 0x7f040437, 0x7f040438, 0x7f040439, 0x7f04043a, 0x7f04043b, 0x7f04043c, 0x7f04043d, 0x7f04043e, 0x7f04043f, 0x7f040441, 0x7f040442, 0x7f040444, 0x7f040445, 0x7f040446 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f04020d, 0x7f040216, 0x7f04044b, 0x7f04047c }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x7f040477 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x01010157, 0x0101015a, 0x7f040084, 0x7f040085, 0x7f040086, 0x7f040087, 0x7f040088, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008c, 0x7f04008d, 0x7f04008e, 0x7f040150, 0x7f040151, 0x7f040152, 0x7f040153, 0x7f040154, 0x7f040155, 0x7f040158, 0x7f040159, 0x7f0401a7, 0x7f0401a8, 0x7f0401a9, 0x7f0401aa, 0x7f0401ab, 0x7f0401ac, 0x7f0401ad, 0x7f0401ae, 0x7f0401b5, 0x7f0401b6, 0x7f0401b7, 0x7f0401b8, 0x7f0401b9, 0x7f0401ba, 0x7f0401bc, 0x7f0401bd, 0x7f0401c0, 0x7f040224, 0x7f040225, 0x7f040226, 0x7f040227, 0x7f04022d, 0x7f04022e, 0x7f04022f, 0x7f040230, 0x7f040373, 0x7f040374, 0x7f040375, 0x7f040376, 0x7f040377, 0x7f040387, 0x7f040388, 0x7f040389, 0x7f04039a, 0x7f04039b, 0x7f04039c, 0x7f0403c7, 0x7f0403cf, 0x7f0403fb, 0x7f0403fc, 0x7f0403fd, 0x7f0403fe, 0x7f0403ff, 0x7f040400, 0x7f040401, 0x7f04041b, 0x7f04041c, 0x7f04041d }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_android_maxEms 5
int styleable TextInputLayout_android_minEms 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_cursorColor 24
int styleable TextInputLayout_cursorErrorColor 25
int styleable TextInputLayout_endIconCheckable 26
int styleable TextInputLayout_endIconContentDescription 27
int styleable TextInputLayout_endIconDrawable 28
int styleable TextInputLayout_endIconMinSize 29
int styleable TextInputLayout_endIconMode 30
int styleable TextInputLayout_endIconScaleType 31
int styleable TextInputLayout_endIconTint 32
int styleable TextInputLayout_endIconTintMode 33
int styleable TextInputLayout_errorAccessibilityLiveRegion 34
int styleable TextInputLayout_errorContentDescription 35
int styleable TextInputLayout_errorEnabled 36
int styleable TextInputLayout_errorIconDrawable 37
int styleable TextInputLayout_errorIconTint 38
int styleable TextInputLayout_errorIconTintMode 39
int styleable TextInputLayout_errorTextAppearance 40
int styleable TextInputLayout_errorTextColor 41
int styleable TextInputLayout_expandedHintEnabled 42
int styleable TextInputLayout_helperText 43
int styleable TextInputLayout_helperTextEnabled 44
int styleable TextInputLayout_helperTextTextAppearance 45
int styleable TextInputLayout_helperTextTextColor 46
int styleable TextInputLayout_hintAnimationEnabled 47
int styleable TextInputLayout_hintEnabled 48
int styleable TextInputLayout_hintTextAppearance 49
int styleable TextInputLayout_hintTextColor 50
int styleable TextInputLayout_passwordToggleContentDescription 51
int styleable TextInputLayout_passwordToggleDrawable 52
int styleable TextInputLayout_passwordToggleEnabled 53
int styleable TextInputLayout_passwordToggleTint 54
int styleable TextInputLayout_passwordToggleTintMode 55
int styleable TextInputLayout_placeholderText 56
int styleable TextInputLayout_placeholderTextAppearance 57
int styleable TextInputLayout_placeholderTextColor 58
int styleable TextInputLayout_prefixText 59
int styleable TextInputLayout_prefixTextAppearance 60
int styleable TextInputLayout_prefixTextColor 61
int styleable TextInputLayout_shapeAppearance 62
int styleable TextInputLayout_shapeAppearanceOverlay 63
int styleable TextInputLayout_startIconCheckable 64
int styleable TextInputLayout_startIconContentDescription 65
int styleable TextInputLayout_startIconDrawable 66
int styleable TextInputLayout_startIconMinSize 67
int styleable TextInputLayout_startIconScaleType 68
int styleable TextInputLayout_startIconTint 69
int styleable TextInputLayout_startIconTintMode 70
int styleable TextInputLayout_suffixText 71
int styleable TextInputLayout_suffixTextAppearance 72
int styleable TextInputLayout_suffixTextColor 73
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0401af, 0x7f0401b0 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040096, 0x7f0400e1, 0x7f0400e2, 0x7f040133, 0x7f040134, 0x7f040135, 0x7f040136, 0x7f040137, 0x7f040138, 0x7f0402d8, 0x7f0402da, 0x7f040311, 0x7f04031a, 0x7f040350, 0x7f040351, 0x7f04038d, 0x7f040416, 0x7f040418, 0x7f040419, 0x7f04049b, 0x7f04049f, 0x7f0404a0, 0x7f0404a1, 0x7f0404a2, 0x7f0404a3, 0x7f0404a4, 0x7f0404a6, 0x7f0404a7 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x01010098, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f040058, 0x7f0403d9 }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_textColor 1
int styleable Tooltip_android_padding 2
int styleable Tooltip_android_layout_margin 3
int styleable Tooltip_android_minWidth 4
int styleable Tooltip_android_minHeight 5
int styleable Tooltip_android_text 6
int styleable Tooltip_backgroundTint 7
int styleable Tooltip_showMarker 8
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int[] styleable Transition { 0x010100d0, 0x7f04004d, 0x7f04012d, 0x7f04012e, 0x7f040199, 0x7f040280, 0x7f040346, 0x7f040378, 0x7f0403fa, 0x7f0404c5, 0x7f0404c7 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f040130, 0x7f0403ab, 0x7f0403ac, 0x7f0403ad, 0x7f0403ae }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f040369, 0x7f04036c, 0x7f04047e }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f040058, 0x7f040059 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int xml ga_ad_services_config 0x7f160000
int xml gma_ad_services_config 0x7f160001
int xml image_share_filepaths 0x7f160002
