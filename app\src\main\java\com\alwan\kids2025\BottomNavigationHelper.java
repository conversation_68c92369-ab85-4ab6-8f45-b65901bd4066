package com.alwan.kids2025;

import android.app.Activity;
import android.content.Intent;
import android.widget.LinearLayout;
import androidx.cardview.widget.CardView;
import android.widget.TextView;

public class BottomNavigationHelper {

    public static void setupBottomNavigation(Activity activity, int currentTab) {
        LinearLayout navHome = activity.findViewById(R.id.nav_home);
        LinearLayout navCategories = activity.findViewById(R.id.nav_categories);
        LinearLayout navGallery = activity.findViewById(R.id.nav_gallery);
        LinearLayout navFavorites = activity.findViewById(R.id.nav_favorites);
        LinearLayout navMore = activity.findViewById(R.id.nav_more);

        // Set current tab as active
        setActiveTab(activity, currentTab);

        // Home navigation
        if (navHome != null) {
            navHome.setOnClickListener(v -> {
                if (currentTab != 0) {
                    Intent intent = new Intent(activity, Categories.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        // Categories navigation
        if (navCategories != null) {
            navCategories.setOnClickListener(v -> {
                if (currentTab != 1) {
                    Intent intent = new Intent(activity, Categories.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        // Gallery navigation
        if (navGallery != null) {
            navGallery.setOnClickListener(v -> {
                if (currentTab != 2) {
                    Intent intent = new Intent(activity, GalleryActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        // Favorites navigation
        if (navFavorites != null) {
            navFavorites.setOnClickListener(v -> {
                if (currentTab != 3) {
                    Intent intent = new Intent(activity, FavoritesActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }

        // More navigation
        if (navMore != null) {
            navMore.setOnClickListener(v -> {
                if (currentTab != 4) {
                    Intent intent = new Intent(activity, MoreActivity.class);
                    activity.startActivity(intent);
                    activity.finish();
                }
            });
        }
    }

    private static void setActiveTab(Activity activity, int activeTabIndex) {
        // Reset all tabs first
        resetAllTabs(activity);

        // Set active tab
        switch (activeTabIndex) {
            case 0: // Home
                setTabActive(activity, R.id.nav_home, R.id.nav_home_icon, R.id.nav_home_text, R.color.modern_primary);
                break;
            case 1: // Categories
                setTabActive(activity, R.id.nav_categories, R.id.nav_categories_icon, R.id.nav_categories_text, R.color.cartoons_color);
                break;
            case 2: // Gallery
                setTabActive(activity, R.id.nav_gallery, R.id.nav_gallery_icon, R.id.nav_gallery_text, R.color.nature_color);
                break;
            case 3: // Favorites
                setTabActive(activity, R.id.nav_favorites, R.id.nav_favorites_icon, R.id.nav_favorites_text, R.color.modern_accent);
                break;
            case 4: // More
                setTabActive(activity, R.id.nav_more, R.id.nav_more_icon, R.id.nav_more_text, R.color.text_secondary);
                break;
        }
    }

    private static void resetAllTabs(Activity activity) {
        resetTab(activity, R.id.nav_home_icon, R.id.nav_home_text);
        resetTab(activity, R.id.nav_categories_icon, R.id.nav_categories_text);
        resetTab(activity, R.id.nav_gallery_icon, R.id.nav_gallery_text);
        resetTab(activity, R.id.nav_favorites_icon, R.id.nav_favorites_text);
        resetTab(activity, R.id.nav_more_icon, R.id.nav_more_text);
    }

    private static void resetTab(Activity activity, int iconId, int textId) {
        CardView icon = activity.findViewById(iconId);
        TextView text = activity.findViewById(textId);
        
        if (icon != null) {
            icon.setCardBackgroundColor(activity.getResources().getColor(R.color.text_secondary));
        }
        
        if (text != null) {
            text.setTextColor(activity.getResources().getColor(R.color.text_primary));
            text.setTypeface(null, android.graphics.Typeface.NORMAL);
        }
    }

    private static void setTabActive(Activity activity, int tabId, int iconId, int textId, int colorId) {
        CardView icon = activity.findViewById(iconId);
        TextView text = activity.findViewById(textId);
        
        if (icon != null) {
            icon.setCardBackgroundColor(activity.getResources().getColor(colorId));
        }
        
        if (text != null) {
            text.setTextColor(activity.getResources().getColor(colorId));
            text.setTypeface(null, android.graphics.Typeface.BOLD);
        }
    }
}
