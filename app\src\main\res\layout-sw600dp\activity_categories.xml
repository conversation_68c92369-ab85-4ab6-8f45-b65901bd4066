<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:layoutDirection="locale"
    android:orientation="vertical"
    tools:context=".Categories">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/header_gradient"
        android:elevation="8dp"
        android:orientation="vertical"
        android:paddingTop="30dp"
        android:paddingBottom="30dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="@android:color/white"
            android:textSize="36sp"
            android:textStyle="bold"
            android:fontFamily="@font/blabeloo"
            android:layout_marginTop="15dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="اختر فئة للتلوين"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:alpha="0.9"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- Categories Grid for Tablets -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:padding="24dp">

        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="3"
            android:rowCount="2"
            android:alignmentMode="alignBounds"
            android:columnOrderPreserved="false"
            android:useDefaultMargins="true">

            <!-- Cards will be larger for tablets -->
            <!-- Card 1: Flowers -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="220dp"
                android:layout_columnWeight="1"
                android:layout_margin="12dp"
                android:foreground="@drawable/card_ripple_effect"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="20dp"
                app:cardElevation="16dp"
                app:cardUseCompatPadding="true">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/c_1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/gp1_press" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/card_overlay" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:background="@drawable/card_text_background"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/text_card_name1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/flowers"
                            android:textColor="@android:color/white"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/blabeloo" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_margin="16dp"
                        android:background="@drawable/floating_icon_bg"
                        android:padding="10dp"
                        android:src="@drawable/flower_icon"
                        android:tint="@android:color/white" />

                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- Additional cards would follow the same pattern with larger dimensions -->

        </GridLayout>
    </ScrollView>

    <!-- Bottom Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</LinearLayout>
