<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="320dp"
    android:background="@drawable/modern_nav_header_bg"
    android:orientation="vertical"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <!-- Animated Background Elements -->
    <ImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="-20dp"
        android:layout_marginEnd="-20dp"
        android:src="@drawable/floating_star_1"
        android:alpha="0.15"
        android:rotation="15" />

    <ImageView
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_marginBottom="-10dp"
        android:layout_marginStart="-10dp"
        android:src="@drawable/floating_heart"
        android:alpha="0.2"
        android:rotation="-20" />

    <ImageView
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="-15dp"
        android:src="@drawable/floating_brush"
        android:alpha="0.1"
        android:rotation="45" />

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp">

        <!-- Modern Logo Container -->
        <RelativeLayout
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginBottom="24dp">

            <!-- Outer Ring -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/modern_logo_ring"
                android:alpha="0.3" />

            <!-- Logo Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_centerInParent="true"
                app:cardCornerRadius="60dp"
                app:cardElevation="20dp"
                app:cardBackgroundColor="@android:color/white">

                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="25dp"
                    android:src="@drawable/small_logo"
                    android:scaleType="centerInside" />

            </androidx.cardview.widget.CardView>

            <!-- Floating Sparkles -->
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:src="@drawable/sparkle_1"
                android:alpha="0.8" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentStart="true"
                android:src="@drawable/sparkle_2"
                android:alpha="0.6" />

        </RelativeLayout>

        <!-- App Title with Modern Style -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/modern_title_bg"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@android:color/white"
                android:textSize="26sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:gravity="center"
                android:shadowColor="#80000000"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="6" />

        </LinearLayout>

        <!-- Modern Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="✨ عالم الألوان السحري ✨"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:alpha="0.95"
            android:fontFamily="@font/blabeloo"
            android:background="@drawable/modern_subtitle_bg"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3" />

    </LinearLayout>

    <!-- Wave Effect at Bottom -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_alignParentBottom="true"
        android:src="@drawable/nav_wave_bottom"
        android:scaleType="fitXY" />

</RelativeLayout>
