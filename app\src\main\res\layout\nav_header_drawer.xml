<?xml version="1.0" encoding="utf-8"?>
<com.github.florent37.shapeofview.shapes.ArcView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="380dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark"
    app:shape_arc_height="30dp"
    app:shape_arc_position="bottom">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/luxury_nav_gradient"
        android:orientation="vertical">

        <!-- Luxury Background Elements -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="-30dp"
            android:layout_marginEnd="-30dp"
            android:src="@drawable/luxury_star_animation"
            android:alpha="0.2"
            android:rotation="20" />

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentStart="true"
            android:layout_marginBottom="-20dp"
            android:layout_marginStart="-20dp"
            android:src="@drawable/luxury_diamond"
            android:alpha="0.25"
            android:rotation="-25" />

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="-25dp"
            android:src="@drawable/luxury_crown"
            android:alpha="0.15"
            android:rotation="50" />

        <!-- Floating Particles -->
        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:src="@drawable/sparkle_1"
            android:alpha="0.6" />

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="80dp"
            android:src="@drawable/sparkle_2"
            android:alpha="0.7" />

        <!-- Luxury Main Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="40dp">

            <!-- Premium Logo Container -->
            <RelativeLayout
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_marginBottom="32dp">

                <!-- Triple Ring System -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/luxury_outer_ring"
                    android:alpha="0.4" />

                <View
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/luxury_middle_ring"
                    android:alpha="0.6" />

                <View
                    android:layout_width="140dp"
                    android:layout_height="140dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/luxury_inner_ring"
                    android:alpha="0.8" />

                <!-- Premium Logo Card -->
                <com.github.florent37.shapeofview.shapes.CircleView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_centerInParent="true"
                    android:elevation="24dp"
                    app:shape_circle_borderColor="@color/luxury_gold"
                    app:shape_circle_borderWidth="4dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/luxury_logo_bg">

                        <com.makeramen.roundedimageview.RoundedImageView
                            android:id="@+id/imageView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="20dp"
                            android:src="@drawable/small_logo"
                            android:scaleType="centerInside"
                            app:riv_corner_radius="50dp" />

                        <!-- Luxury Overlay -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/luxury_logo_overlay"
                            android:alpha="0.3" />

                    </RelativeLayout>
                </com.github.florent37.shapeofview.shapes.CircleView>

                <!-- Floating Luxury Sparkles -->
                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_margin="8dp"
                    android:src="@drawable/luxury_diamond"
                    android:alpha="0.9"
                    android:rotation="15" />

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentStart="true"
                    android:layout_margin="8dp"
                    android:src="@drawable/luxury_crown"
                    android:alpha="0.8"
                    android:rotation="-15" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentStart="true"
                    android:layout_margin="20dp"
                    android:src="@drawable/sparkle_1"
                    android:alpha="0.9" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentEnd="true"
                    android:layout_margin="20dp"
                    android:src="@drawable/sparkle_2"
                    android:alpha="0.8" />

            </RelativeLayout>

            <!-- Luxury App Title -->
            <com.github.florent37.shapeofview.shapes.RoundRectView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:elevation="12dp"
                app:shape_roundRect_borderColor="@color/luxury_gold"
                app:shape_roundRect_borderWidth="4dp"
                app:shape_roundRect_topLeftRadius="30dp"
                app:shape_roundRect_topRightRadius="30dp"
                app:shape_roundRect_bottomLeftRadius="30dp"
                app:shape_roundRect_bottomRightRadius="30dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/luxury_nav_title_bg"
                    android:orientation="horizontal"
                    android:paddingHorizontal="28dp"
                    android:paddingVertical="16dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/luxury_crown"
                        android:tint="@android:color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textColor="@android:color/white"
                        android:textSize="28sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:shadowColor="#80000000"
                        android:shadowDx="3"
                        android:shadowDy="3"
                        android:shadowRadius="8" />

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginStart="12dp"
                        android:src="@drawable/luxury_diamond"
                        android:tint="@android:color/white" />

                </LinearLayout>
            </com.github.florent37.shapeofview.shapes.RoundRectView>

            <!-- Luxury Subtitle -->
            <com.github.florent37.shapeofview.shapes.RoundRectView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:elevation="8dp"
                app:shape_roundRect_borderColor="@color/luxury_silver"
                app:shape_roundRect_borderWidth="3dp"
                app:shape_roundRect_topLeftRadius="25dp"
                app:shape_roundRect_topRightRadius="25dp"
                app:shape_roundRect_bottomLeftRadius="25dp"
                app:shape_roundRect_bottomRightRadius="25dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/luxury_nav_subtitle_bg"
                    android:orientation="horizontal"
                    android:paddingHorizontal="24dp"
                    android:paddingVertical="12dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="10dp"
                        android:src="@drawable/luxury_magic_wand"
                        android:tint="@android:color/white"
                        android:rotation="15" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="عالم الألوان الفخم"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:fontFamily="@font/blabeloo"
                        android:shadowColor="#80000000"
                        android:shadowDx="2"
                        android:shadowDy="2"
                        android:shadowRadius="6" />

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/luxury_magic_wand"
                        android:tint="@android:color/white"
                        android:rotation="-15" />

                </LinearLayout>
            </com.github.florent37.shapeofview.shapes.RoundRectView>

        </LinearLayout>

    </RelativeLayout>

</com.github.florent37.shapeofview.shapes.ArcView>
