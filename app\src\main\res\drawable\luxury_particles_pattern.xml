<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="400dp"
    android:height="400dp"
    android:viewportWidth="400"
    android:viewportHeight="400">
    
    <!-- Lu<PERSON><PERSON> Particles Pattern -->
    <group android:name="particles">
        <!-- <PERSON> Shapes -->
        <path
            android:fillColor="#FFD700"
            android:pathData="M50,30 L60,40 L50,50 L40,40 Z" />
        <path
            android:fillColor="#C0C0C0"
            android:pathData="M150,80 L160,90 L150,100 L140,90 Z" />
        <path
            android:fillColor="#FFD700"
            android:pathData="M250,130 L260,140 L250,150 L240,140 Z" />
        <path
            android:fillColor="#C0C0C0"
            android:pathData="M350,180 L360,190 L350,200 L340,190 Z" />
            
        <!-- <PERSON> Shapes -->
        <path
            android:fillColor="#FFD700"
            android:pathData="M100,200 L105,210 L115,210 L107,217 L110,227 L100,220 L90,227 L93,217 L85,210 L95,210 Z" />
        <path
            android:fillColor="#C0C0C0"
            android:pathData="M200,250 L205,260 L215,260 L207,267 L210,277 L200,270 L190,277 L193,267 L185,260 L195,260 Z" />
        <path
            android:fillColor="#FFD700"
            android:pathData="M300,300 L305,310 L315,310 L307,317 L310,327 L300,320 L290,327 L293,317 L285,310 L295,310 Z" />
            
        <!-- Circle Shapes -->
        <circle
            android:fillColor="#FFD700"
            android:cx="80"
            android:cy="350"
            android:r="5" />
        <circle
            android:fillColor="#C0C0C0"
            android:cx="180"
            android:cy="50"
            android:r="4" />
        <circle
            android:fillColor="#FFD700"
            android:cx="280"
            android:cy="100"
            android:r="6" />
        <circle
            android:fillColor="#C0C0C0"
            android:cx="380"
            android:cy="350"
            android:r="5" />
    </group>
</vector>
